package models_api

type AllUsersRespond struct {
	ID        uint   `json:"id"`
	Picture   string `json:"user_picture"`
	FirstName string `json:"user_fname"`
	LastName  string `json:"user_lname"`
	Email     string `json:"user_email"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Status    int    `json:"user_status"`
	Role      int    `json:"user_role"`
	Slug      string `json:"user_slug"`
}

type UserRespond struct {
	Slug      string  `json:"slug"`
	Picture   string  `json:"user_picture"`
	Password  string  `json:"password"`
	FirstName string  `json:"user_fname"`
	LastName  string  `json:"user_lname"`
	Email     string  `json:"user_email"`
	Position  string  `json:"user_position"`
	LoginDate *string `json:"user_login_date"`
	Role      *int    `json:"role"`
	Status    *int    `json:"status"`
}

type UserRequest struct {
	Picture   string `json:"user_picture"`
	Password  string `json:"password"`
	FirstName string `json:"user_fname"`
	LastName  string `json:"user_lname"`
	Email     string `json:"user_email"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Role      int    `json:"role"`
	Status    int    `json:"status"`
}

type CreateUser struct {
	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
	Password  string `json:"password"`
	Email     string `json:"email"`
	Position  string `json:"position"`
	Role      int    `json:"role"`
	Picture   string `json:"profileImage"`
	Status    int    `json:"status"`
}

type MeResponse struct {
	ID        uint   `json:"id"`
	Username  string `json:"username"`
	FirstName string `json:"user_fname"`
	LastName  string `json:"user_lname"`
	Email     string `json:"user_email"`
	Position  string `json:"user_position"`
	Picture   string `json:"user_picture"`
	Role      string `json:"role"`
}

type Updatepassword struct {
	Email string `json:"user_email" gorm:"unique" binding:"email"`
}
