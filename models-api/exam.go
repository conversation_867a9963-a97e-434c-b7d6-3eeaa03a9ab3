package models_api

type ExamRespond struct {
	ID              uint                  `json:"id"`
	ExamName        string                `json:"exam_name"`
	ExamDescription string                `json:"exam_description"`
	ExamTime        int                   `json:"exam_time"`
	ExamStatus      bool                  `json:"exam_status"`
	PassingScore    int                   `json:"passing_score"`
	CourseSlug      string                `json:"course_slug"`
	Questions       []ExamQuestionRespond `json:"questions"`
}

type ExamQuestionRespond struct {
	ID           uint                `json:"id"`
	Question     string              `json:"question"`
	Detail       string              `json:"detail"`
	TimeInsert   int                 `json:"time_insert"`
	QuestionType uint                `json:"question_type"`
	Choices      []ExamChoiceRespond `json:"choices"`
}

type ExamChoiceRespond struct {
	ID         uint   `json:"id"`
	Choice     string `json:"choice"`
	IsCorrect  bool   `json:"is_correct"`
	ChoiceType uint   `json:"choice_type"`
}
