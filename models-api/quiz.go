package models_api

type QuizRespond struct {
	ID              uint                  `json:"id"`
	QuizName        string                `json:"quiz_name"`
	QuizDescription string                `json:"quiz_description"`
	QuizNumber      int                   `json:"quiz_number"`
	QuizTime        float32               `json:"quiz_time"`
	QuizStatus      bool                  `json:"quiz_status"`
	ContentSlug     string                `json:"content_slug"`
	LessonSlug      string                `json:"lesson_slug"`
	CourseSlug      string                `json:"course_slug"`
	CourseName      string                `json:"course_name"`
	Questions       []QuizQuestionRespond `json:"questions"`
	Slug            string                `json:"slug"`
}

type QuizQuestionRespond struct {
	ID            uint                `json:"id"`
	Question      string              `json:"question"`
	QuestionImage string              `json:"question_image,omitempty"`
	Detail        string              `json:"detail"`
	TimeInsert    int                 `json:"time_insert"`
	QuestionType  uint                `json:"question_type"`
	Slug          string              `json:"slug"`
	Choices       []QuizChoiceRespond `json:"choices"`
}

type QuizChoiceRespond struct {
	ID          uint   `json:"id"`
	Choice      string `json:"choice"`
	ChoiceImage string `json:"choice_image,omitempty"`
	IsCorrect   bool   `json:"is_correct"`
	ChoiceType  uint   `json:"choice_type"`
	Slug        string `json:"slug"`
}
