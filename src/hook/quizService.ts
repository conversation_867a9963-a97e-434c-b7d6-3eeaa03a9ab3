import { api } from '@/lib/api'

// Types for request/response (snake_case to match backend)
export interface QuizChoiceRequest {
  choice: string
  choice_image: string
  is_correct: boolean
  choice_type: number
}

export interface QuizQuestionRequest {
  question: string
  question_image: string
  detail: string
  time_insert: number
  question_type: number
  choice: QuizChoiceRequest[]
}

export interface QuizRequest {
  quiz_name: string
  slug: string
  quiz_description: string
  quiz_number: number
  quiz_time: number
  quiz_status: boolean
  content_lesson_id: string
  question: QuizQuestionRequest[]
}

export interface QuizResponse {
  message: string
  [key: string]: any
}

const QUIZ_API = '/quizs'

export const quizService = {
  async createQuiz(quizData: QuizRequest): Promise<QuizResponse> {
    try {
      const response = await api.post(QUIZ_API, quizData)
      return response as QuizResponse
    } catch (error) {
      console.error('Failed to create quiz:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to create quiz')
    }
  },
  async getAllQuizzes(): Promise<any[]> {
    try {
      const response = await api.get(QUIZ_API)
      return response as any[]
    } catch (error) {
      console.error('Failed to fetch quizzes:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch quizzes')
    }
  },
  async getQuizById(id: string | number): Promise<any> {
    try {
      const response = await api.get(`${QUIZ_API}/${id}`)
      return response as any
    } catch (error) {
      console.error('Failed to fetch quiz:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch quiz')
    }
  },
  async updateQuiz(id: string | number, quizData: QuizRequest): Promise<QuizResponse> {
    try {
      const response = await api.put(`${QUIZ_API}/${id}`, quizData)
      return response as QuizResponse
    } catch (error) {
      console.error('Failed to update quiz:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to update quiz')
    }
  },
  async deleteQuiz(id: string | number): Promise<QuizResponse> {
    try {
      const response = await api.delete(`${QUIZ_API}/${id}`)
      return response as QuizResponse
    } catch (error) {
      console.error('Failed to delete quiz:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to delete quiz')
    }
  },
}
