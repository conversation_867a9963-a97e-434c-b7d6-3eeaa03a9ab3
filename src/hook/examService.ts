import { api } from '@/lib/api'

// Types for request/response (snake_case to match backend)
export interface ExamChoiceRequest {
  choice: string
  choice_image?: string
  is_correct: boolean
  choice_type: number
}

export interface ExamQuestionRequest {
  question: string
  questino_image?: string
  detail: string
  time_insert: number
  question_type: number
  choices: ExamChoiceRequest[]
}

export interface ExamRequest {
  exam_name: string
  exam_description: string
  exam_time: number
  exam_status: boolean
  passing_score: number
  course_slug: string
  questions: ExamQuestionRequest[]
}

export interface Exam {
  id: number
  course_slug: string
  exam_name: string
  exam_description: string
  exam_time: number
  exam_status: boolean
  passing_score: number
  course_id: number
  questions: ExamQuestion[]
}

export interface ExamChoice {
  id: number
  choice: string
  choice_image?: string
  is_correct: boolean
  choice_type: number
}

export interface ExamQuestion {
  id: number
  question: string
  question_image?: string
  detail: string
  time_insert: number
  question_type: number
  exam_id: number
  choices: ExamChoice[]
}
export interface ExamTable {
  slug: number
  course_name: string
  exam_amount: number
  exam_name: string
  exam_time: number
  passing_score: number
}

const EXAM_API = '/exams'

export const examService = {
  async getAllExams(): Promise<Exam[]> {
    try {
      const response = await api.get(EXAM_API)
      return response as Exam[]
    } catch (error) {
      console.error('Failed to fetch exams:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch exams')
    }
  },
  async getExamsTable(): Promise<ExamTable> {
    try {
      const response = await api.get(`${EXAM_API}/table`)
      return response as ExamTable
    } catch (error) {
      console.error('Failed to fetch exams table:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch exams table')
    }
  },
  async getExamById(id: string | number): Promise<Exam> {
    try {
      const response = await api.get(`${EXAM_API}/${id}`)
      return response as Exam
    } catch (error) {
      console.error('Failed to fetch exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch exam')
    }
  },
  async createExam(data: ExamRequest): Promise<any> {
    try {
      const response = await api.post(EXAM_API, data)
      return response
    } catch (error) {
      console.error('Failed to create exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to create exam')
    }
  },
  async updateExam(id: string | number, data: ExamRequest): Promise<any> {
    try {
      const response = await api.put(`${EXAM_API}/${id}`, data)
      return response
    } catch (error) {
      console.error('Failed to update exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to update exam')
    }
  },
  async deleteExam(id: string | number): Promise<any> {
    try {
      const response = await api.delete(`${EXAM_API}/${id}`)
      return response
    } catch (error) {
      console.error('Failed to delete exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to delete exam')
    }
  },
}

export default examService
