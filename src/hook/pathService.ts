import { api } from '@/lib/api'
import { tableResponse, pathRequest, pathResponse } from '@/types/api/path';

export async function getPath(): Promise<tableResponse[]> {
    try {
        const response = await api.get<tableResponse[]>('/path/table');
        return response;
    } catch (error) {
        console.error('Failed to fetch path:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to fetch path');
    }
}

// Get specific path by ID
export async function getPathById(id: string): Promise<pathResponse> {
  try {
    const response = await api.get<pathResponse>(`/path/${id}`);
    return response;
  } catch (error) {
    console.error('Failed to fetch path by ID:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch path by ID');
  }
}



export async function postPath(data: pathRequest): Promise<any> {
  try {
    console.log('Posting path data:', data);
    console.log('API endpoint: /path');
    const response = await api.post('/path', data);
    console.log('Path post response:', response);
    return response;
  } catch (error) {
    console.error('Failed to post path:', error);
    console.error('Error details:', error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    throw new Error(error instanceof Error ? error.message : 'Failed to post path');
  }
}

// Update existing path by ID
export async function putPath(id: string, data: pathRequest): Promise<any> {
  try {
    const response = await api.put(`/path/${id}`, data);
    return response;
  } catch (error) {
    console.error('Failed to update path:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to update path');
  }
}

// Delete path by ID
export async function deletePath(id: string): Promise<any> {
  try {
    const response = await api.delete(`/path/${id}`);
    return response;
  } catch (error) {
    console.error('Failed to delete path:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to delete path');
  }
}


