import { api } from '@/lib/api'

// Types for user course dashboard (snake_case to match backend)
export interface PathCourse {
  lesson_slug: string
  lesson_name: string
}

export interface UserCourse {
  course_slug: string
  course_image: string
  course_graduated: boolean
  course_name: string
  course_path: PathCourse[]
  course_description: string
  course_processing: number
  course_amount: number
  course_lessoned: number
  course_difficulty: string
  course_duration: number
}

// Types for user pathways dashboard
export interface PathwayCourse {
  lesson_slug: string
  lesson_name: string
  lesson_progress: number
  lesson_duration: number
  lesson_difficulty: string
  lesson_has_exam: boolean // <-- added
  lesson_examined: boolean // <-- add this
}

export interface UserPathway {
  path_slug: string
  path_name: string // <-- added
  path_description: string
  path_amount: number
  path_lessoned: number
  path_duration: number
  path_processing: number
  courses: PathwayCourse[]
}

export interface PathwaysDashboardResponse {
  data: UserPathway[]
  pagination: {
    current_page: number
    has_next: boolean
    has_prev: boolean
    items_per_page: number
    total_items: number
    total_pages: number
  }
}

export interface CoursesDashboardResponse {
  data: UserCourse[]
  pagination: {
    current_page: number
    has_next: boolean
    has_prev: boolean
    items_per_page: number
    total_items: number
    total_pages: number
  }
}

// Certificate response types
export interface CertificatePath {
  username: string
  path_name: string
  date_certified: string
}

export interface CertificateCourse {
  username: string
  course_name: string
  date_certified: string
}

const PROFILE_API = '/user/dashboard/courses'
const PATHWAYS_API = '/user/dashboard/paths'
const CERTIFICATES_API = '/user/certificates'

export const profileService = {
  async getUserCoursesDashboard(page?: number, items_per_page?: number): Promise<CoursesDashboardResponse | UserCourse[]> {
    try {
      let url = PROFILE_API
      const params: string[] = []
      if (page) params.push(`page=${page}`)
      // Include items_per_page in query params if provided
      if (items_per_page) params.push(`items_per_page=${items_per_page}`)
      if (params.length > 0) url += `?${params.join("&")}`
      const response = await api.get<any>(url)
      // If no pagination requested, return just the array for dashboard compatibility
      if (!page && !items_per_page) {
        // Try to extract array from response
        if (Array.isArray(response)) return response
        if (response && Array.isArray(response.data)) return response.data
        if (response && Array.isArray(response.courses)) return response.courses
        return []
      }
      // Map backend pagination keys to frontend expected keys
      let pagination = response.pagination || {}
      pagination = {
        current_page: pagination.current_page,
        has_next: pagination.has_next,
        has_prev: pagination.has_prev,
        items_per_page: pagination.items_per_page || pagination.per_page, // Use only backend value
        total_items: pagination.total_items || pagination.total || 0,
        total_pages: pagination.total_pages || 1,
      }
      return {
        data: Array.isArray(response.data) ? response.data : [],
        pagination,
      }
    } catch (error) {
      console.error('Failed to fetch user course dashboard:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch user course dashboard')
    }
  },
  async getUserPathwaysDashboard(page?: number, items_per_page?: number): Promise<PathwaysDashboardResponse> {
    try {
      let url = PATHWAYS_API
      const params: string[] = []
      if (page) params.push(`page=${page}`)
      if (items_per_page) params.push(`items_per_page=${items_per_page}`)
      if (params.length > 0) url += `?${params.join("&")}`
      const response = await api.get<PathwaysDashboardResponse>(url)
      return response
    } catch (error) {
      console.error('Failed to fetch user pathways dashboard:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch user pathways dashboard')
    }
  },
  async getUserOverviewDashboard(): Promise<{ total_courses: number; total_certificates: number }> {
    try {
      const response = await api.get<{ total_courses: number; total_certificates: number }>(
        '/user/dashboard/overview-dashboard'
      )
      return response
    } catch (error) {
      console.error('Failed to fetch user overview dashboard:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch user overview dashboard')
    }
  },
  async getUserOverviewPath(): Promise<{ total_path: number; total_path_completed: number; total_path_in_progress: number; total_avg_time: number }> {
    try {
      const response = await api.get<{ total_path: number; total_path_completed: number; total_path_in_progress: number; total_avg_time: number }>(
        '/user/dashboard/overview-path'
      )
      return response
    } catch (error) {
      console.error('Failed to fetch user overview path:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch user overview path')
    }
  },
  async getPathCertificate(pathSlug: string): Promise<CertificatePath> {
    try {
      const response = await api.get<CertificatePath>(`${CERTIFICATES_API}/path/${pathSlug}`)
      return response
    } catch (error) {
      console.error('Failed to fetch path certificate:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch path certificate')
    }
  },

  async getCourseCertificate(courseSlug: string): Promise<CertificateCourse> {
    try {
      const response = await api.get<CertificateCourse>(`${CERTIFICATES_API}/course/${courseSlug}`)
      return response
    } catch (error) {
      console.error('Failed to fetch course certificate:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch course certificate')
    }
  },
}

export default profileService
