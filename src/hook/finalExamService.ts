import { api } from '@/lib/api'
import {
  FinalExam,
  FinalExamChoice,
  FinalExamQuestion,
  CreateFinalExamRequest,
  CreateFinalExamResponse,
  UpdateFinalExamResponse,
  DeleteFinalExamResponse,
  ExamAnswerSubmission,
  ExamCheckResult
} from '@/types/api/finalexam'

// Re-export types for convenience
export type {
  FinalExam,
  FinalExamChoice,
  FinalExamQuestion,
  CreateFinalExamRequest,
  CreateFinalExamResponse,
  ExamAnswerSubmission,
  ExamCheckResult
}

// Final Exam API service
export const finalExamService = {
  // Create a new final exam
  async createFinalExam(examData: CreateFinalExamRequest): Promise<CreateFinalExamResponse> {
    try {
      const response = await api.post<CreateFinalExamResponse>('/final-exams', examData)
      return response
    } catch (error) {
      console.error('Failed to create final exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to create final exam')
    }
  },

  // Get all final exams for a course by course ID or slug
  async getFinalExamsByCourse(courseId: string): Promise<FinalExam[]> {
    try {
      console.log("📡 API Call: getFinalExamsByCourse with courseId:", courseId)
      const response = await api.get<FinalExam[]>(`/final-exams/course/${courseId}`)
      console.log("📡 API Response:", response)
      return response
    } catch (error) {
      console.error('❌ Failed to fetch final exams by course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch final exams')
    }
  },

  // Get a final exam by exam slug
  async getFinalExamBySlug(examSlug: string): Promise<FinalExam> {
    try {
      const response = await api.get<FinalExam>(`/final-exams/${examSlug}`)
      return response
    } catch (error) {
      console.error('Failed to fetch final exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch final exam')
    }
  },

  // Update a final exam by exam slug
  async updateFinalExam(examSlug: string, examData: CreateFinalExamRequest): Promise<UpdateFinalExamResponse> {
    try {
      const response = await api.put<UpdateFinalExamResponse>(`/final-exams/${examSlug}`, examData)
      return response
    } catch (error) {
      console.error('Failed to update final exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to update final exam')
    }
  },

  // Delete a final exam by exam slug
  async deleteFinalExam(examSlug: string): Promise<DeleteFinalExamResponse> {
    try {
      const response = await api.delete<DeleteFinalExamResponse>(`/final-exams/${examSlug}`)
      return response
    } catch (error) {
      console.error('Failed to delete final exam:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to delete final exam')
    }
  },

  // Submit answers for a final exam and get results
  async submitFinalExamAnswers(examSlug: string, answers: Record<string, string | string[]>): Promise<ExamCheckResult> {
    try {
      console.log("📡 API Call: submitFinalExamAnswers", { examSlug, answers })
      // Convert answers object to array of { questionSlug, selectedChoiceSlug: string[] }
      const answersArray = Object.entries(answers).map(([questionSlug, selectedChoiceSlug]) => ({
        questionSlug,
        selectedChoiceSlug: Array.isArray(selectedChoiceSlug) ? selectedChoiceSlug : [selectedChoiceSlug]
      }))
      const submissionData = { answers: answersArray }
      const response = await api.post<ExamCheckResult>(`/final-exams/${examSlug}/submit`, submissionData)
      console.log("📡 API Response:", response)
      return response
    } catch (error) {
      console.error('❌ Failed to submit final exam answers:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to submit exam answers')
    }
  }
}

// Export individual functions for convenience
export const {
  createFinalExam,
  getFinalExamsByCourse,
  getFinalExamBySlug,
  updateFinalExam,
  deleteFinalExam,
  submitFinalExamAnswers
} = finalExamService