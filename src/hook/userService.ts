import { api } from '@/lib/api'
import { User, UserSlugResponse, UserTable } from '@/types/api/user';
import { UserFormData } from '@/types/users';

export async function getUsers(): Promise<User[]> {
    try {
        const response = await api.get<User[]>('/users');
        return response;
    } catch (error) {
        console.error('Failed to fetch users:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to fetch users');
    }
}

export async function getRole(): Promise<{
    id: string;
    name: string;
}> {
    try {
        const response = await api.get<{
            id: string;
            name: string;
        }>('/users/role');
        return response;
    } catch (error) {
        console.error('Failed to fetch user roles:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to fetch user roles');
    }
}

export async function getUserTable(): Promise<UserTable[]> {
    try {
        const response = await api.get<UserTable[]>('/users/table');
        return response;
    } catch (error) {
        console.error('Failed to fetch user table:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to fetch user table');
    }
}

export async function getUserBySlug(slug: string): Promise<UserSlugResponse> {
    try {
        const response = await api.get<UserSlugResponse>(`/users/${slug}`);
        return response;
    } catch (error) {
        console.error('Failed to fetch user by slug:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to fetch user by slug');
    }
}

export async function createUser(userData: UserFormData): Promise<UserFormData> {
    try {
        const response = await api.post<UserFormData>('/users', userData);
        return response;
    } catch (error) {
        console.error('Failed to create user:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to create user');
    }
}

export async function updateUser(slug: string, userData: UserFormData): Promise<UserFormData> {
    try {
        const response = await api.put<UserFormData>(`/users/${slug}`, userData);
        return response;
    } catch (error) {
        console.error('Failed to update user:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to update user');
    }
}

export async function deleteUser(slug: string): Promise<void> {
    try {
        await api.delete(`/users/${slug}`);
    } catch (error) {
        console.error('Failed to delete user:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to delete user');
    }
}