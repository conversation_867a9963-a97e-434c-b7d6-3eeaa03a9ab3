import { api } from '@/lib/api'
import { CreateCourseRequest, CreateCourseResponse } from '@/types/api/course'
import { User } from '@/types/api/user'
import { getUsers } from '@/hook/userService'

export type { User, CreateCourseRequest }

// Course API service
export const courseService = {
  // Create a new course
  async createCourse(courseData: CreateCourseRequest): Promise<CreateCourseResponse> {
    try {
      const response = await api.post<CreateCourseResponse>('/courses', courseData)
      return response
    } catch (error) {
      console.error('Failed to create course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to create course')
    }
  },

  // Get all courses
  async getCourses(): Promise<any[]> {
    try {
      const response = await api.get<any[]>('/courses')
      return response
    } catch (error) {
      console.error('Failed to fetch courses:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch courses')
    }
  },

  // Get course by ID
  async getCourseById(courseId: string): Promise<any> {
    try {
      const response = await api.get<any>(`/courses/${courseId}`)
      return response
    } catch (error) {
      console.error('Failed to fetch course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch course')
    }
  },

  // Get course by slug
  async getCourseBySlug(slug: string): Promise<any> {
    try {
      const response = await api.get<any>(`/courses/${slug}`)
      return response
    } catch (error) {
      console.error('Failed to fetch course by slug:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch course')
    }
  },

  // Update course by slug
  async updateCourse(slug: string, courseData: CreateCourseRequest): Promise<CreateCourseResponse> {
    try {
      const response = await api.put<CreateCourseResponse>(`/courses/${slug}`, courseData)
      return response
    } catch (error) {
      console.error('Failed to update course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to update course')
    }
  },

  // Delete course by slug
  async deleteCourse(slug: string): Promise<any> {
    try {
      const response = await api.delete<any>(`/courses/${slug}`)
      return response
    } catch (error) {
      console.error('Failed to delete course:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to delete course')
    }
  }
}

// User API service for fetching lecturers
export const userService = {
  // Get lecturers only
  async getLecturers(): Promise<User[]> {
    try {
      const users = await getUsers()
      return users.filter(user => user.user_role === 2)
    } catch (error) {
      console.error('Failed to fetch lecturers:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch lecturers')
    }
  }
}
