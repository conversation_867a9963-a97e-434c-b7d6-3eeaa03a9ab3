"use client"

import { useState } from "react"
import { Search, Download, Filter, Users, TrendingUp, Clock, Award, Calendar, X } from "lucide-react"
import AdminLayout from "@/components/admin/layout"
import { useRouter } from "next/navigation"
import Swal from "sweetalert2"

// Mock data
const mockStudentData = [
  {
    studentId: "STU001",
    name: "นายสมชาย ใจดี",
    department: "แผนกพยาบาล",
    position: "พยาบาลวิชาชีพ",
    enrollmentDate: "2024-01-15",
    lastActivity: "2024-12-15",
    progress: 85,
    status: "กำลังเรียน",
    certificatesEarned: 3,
    completionRate: 85
  },
  {
    studentId: "STU002", 
    name: "นางสาวสมหญิง รักเรียน",
    department: "แผนกเภสัชกรรม",
    position: "เภสัชกร",
    enrollmentDate: "2024-02-01",
    lastActivity: "2024-12-14",
    progress: 92,
    status: "เสร็จสิ้น",
    certificatesEarned: 5,
    completionRate: 92
  },
  {
    studentId: "STU003",
    name: "นายวิชัย ขยันเรียน", 
    department: "แผนกเทคนิคการแพทย์",
    position: "นักเทคนิคการแพทย์",
    enrollmentDate: "2024-01-20",
    lastActivity: "2024-12-10",
    progress: 67,
    status: "กำลังเรียน",
    certificatesEarned: 2,
    completionRate: 67
  }
]

const mockHRData = {
  totalStudents: 150,
  activeStudents: 142,
  completedTraining: 98,
  inProgress: 44,
  avgCompletionTime: 45,
  certificationRate: 85
}

const mockDepartmentStats = [
  {
    department: "แผนกพยาบาล",
    totalStudents: 50,
    completedTraining: 42,
    inProgress: 8,
    completionRate: 84.0
  },
  {
    department: "แผนกเภสัชกรรม",
    totalStudents: 30,
    completedTraining: 28,
    inProgress: 2,
    completionRate: 93.3
  },
  {
    department: "แผนกเทคนิคการแพทย์",
    totalStudents: 40,
    completedTraining: 28,
    inProgress: 12,
    completionRate: 70.0
  }
]

export default function HRReportPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("ทั้งหมด")
  const [dateRange, setDateRange] = useState({ start: "", end: "" })
  const [showFilters, setShowFilters] = useState(false)
  const [activeTab, setActiveTab] = useState("students")
  const [selectedStudentId, setSelectedStudentId] = useState("")
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  const handleDownloadPDF = async (type: 'overall' | 'selected' = 'overall') => {
    setIsGeneratingPDF(true)

    try {
      // Prepare request data
      const requestData = {
        type: type,
        searchTerm: searchTerm,
        department: selectedDepartment,
        dateStart: dateRange.start,
        dateEnd: dateRange.end,
        selectedStudentId: type === 'selected' ? selectedStudentId : undefined
      }

      // Show loading notification
      Swal.fire({
        title: 'กำลังสร้าง PDF...',
        text: 'กรุณารอสักครู่',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading()
        }
      })

      // Call backend API
      const response = await fetch('/api/reports/hr/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Get PDF blob
      const blob = await response.blob()
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      
      // Set filename
      const selectedStudent = selectedStudentId ? mockStudentData.find(s => s.studentId === selectedStudentId) : null
      const fileName = type === 'selected' && selectedStudent 
        ? `รายงาน_HR_${selectedStudent.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
        : `รายงาน_HR_ภาพรวม_${new Date().toISOString().split('T')[0]}.pdf`
      
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      
      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      // Close loading and show success
      Swal.close()
      Swal.fire({
        icon: 'success',
        title: 'สำเร็จ!',
        text: 'ดาวน์โหลด PDF เรียบร้อยแล้ว',
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error generating PDF:', error)
      Swal.close()
      Swal.fire({
        icon: 'error',
        title: 'เกิดข้อผิดพลาด!',
        text: 'ไม่สามารถสร้าง PDF ได้ กรุณาลองอีกครั้ง',
        confirmButtonText: 'ตกลง'
      })
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "เสร็จสิ้น":
        return "bg-green-100 text-green-800"
      case "กำลังเรียน":
        return "bg-yellow-100 text-yellow-800"
      case "ยังไม่เริ่ม":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "bg-green-500"
    if (progress >= 60) return "bg-yellow-500"
    if (progress >= 40) return "bg-orange-500"
    return "bg-red-500"
  }

  const filteredData = mockStudentData.filter(emp => {
    const matchesSearch = emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         emp.studentId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = selectedDepartment === "ทั้งหมด" || emp.department === selectedDepartment
    let matchesDate = true
    if (dateRange.start && dateRange.end) {
      const activityDate = new Date(emp.lastActivity)
      const startDate = new Date(dateRange.start)
      const endDate = new Date(dateRange.end)
      // Include the end date by setting time to end of day
      endDate.setHours(23, 59, 59, 999)
      matchesDate = activityDate >= startDate && activityDate <= endDate
    }
    
    return matchesSearch && matchesDepartment && matchesDate
  })

  const filteredDepartmentStats = mockDepartmentStats.filter(dept => 
    selectedDepartment === "ทั้งหมด" || dept.department === selectedDepartment
  )

  const clearFilters = () => {
    setSearchTerm("")
    setSelectedDepartment("ทั้งหมด")
    setDateRange({ start: "", end: "" })
    setShowFilters(false)
  }

  const hasActiveFilters = searchTerm || selectedDepartment !== "ทั้งหมด" || dateRange.start || dateRange.end

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">รายงาน HR</h1>
              <p className="text-gray-600 mt-1">ติดตามความคืบหน้าการเรียนรู้ของพนักงาน</p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => handleDownloadPDF('overall')}
                disabled={isGeneratingPDF}
                className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                <Download className="w-4 h-4" />
                {isGeneratingPDF ? 'กำลังสร้าง...' : 'ดาวน์โหลด PDF'}
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-blue-600 font-medium">นักเรียนทั้งหมด</p>
                  <p className="text-2xl font-bold text-blue-900">{mockHRData.totalStudents}</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-green-600 font-medium">นักเรียนที่ใช้งาน</p>
                  <p className="text-2xl font-bold text-green-900">{mockHRData.activeStudents}</p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-purple-600 font-medium">เวลาเฉลี่ย (นาที)</p>
                  <p className="text-2xl font-bold text-purple-900">{mockHRData.avgCompletionTime}</p>
                </div>
              </div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Award className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-orange-600 font-medium">อัตราใบรับรอง</p>
                  <p className="text-2xl font-bold text-orange-900">{mockHRData.certificationRate}%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
