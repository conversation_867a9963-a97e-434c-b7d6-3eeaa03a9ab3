"use client"

import AdminLayout from "@/components/admin/layout"
import { useSearchParams } from "next/navigation"
import { Award, BookOpen, TrendingUp, Download } from "lucide-react"
import { useState } from "react"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"

// Mock data for student details
const mockStudentDetails = {
  "STU001": {
    name: "นางสาวสุภา ใจดี",
    department: "แผนกการพยาบาล",
    position: "พยาบาลประจำการ",
    mandatoryCourses: [
      {
        id: 1,
        name: "หลักการพยาบาลเบื้องต้น",
        progress: 100,
        status: "ผ่าน"
      },
      {
        id: 2,
        name: "จรรยาบรรณวิชาชีพ",
        progress: 95,
        status: "ผ่าน"
      },
      {
        id: 3,
        name: "ความปลอดภัยในโรงพยาบาล",
        progress: 80,
        status: "ไม่ผ่าน"
      }
    ],
    technicalCourses: [
      {
        id: 4,
        name: "การดูแลผู้ป่วยวิกฤต",
        score: 85,
        status: "ผ่าน"
      },
      {
        id: 5,
        name: "เวชศาสตร์ฉุกเฉิน",
        score: 78,
        status: "ผ่าน"
      },
      {
        id: 6,
        name: "การจัดการยาและเวชภัณฑ์",
        score: 92,
        status: "ผ่าน"
      },
      {
        id: 7,
        name: "เทคโนโลยีการแพทย์",
        score: 65,
        status: "ไม่ผ่าน"
      }
    ]
  },
  "STU002": {
    name: "นายสมชาย รักดี",
    department: "แผนกเวชกรรม",
    position: "แพทย์ประจำการ",
    mandatoryCourses: [
      {
        id: 1,
        name: "หลักการแพทย์เบื้องต้น",
        progress: 100,
        status: "ผ่าน"
      },
      {
        id: 2,
        name: "จรรยาบรรณแพทย์",
        progress: 100,
        status: "ผ่าน"
      },
      {
        id: 3,
        name: "ความปลอดภัยในโรงพยาบาล",
        progress: 100,
        status: "ผ่าน"
      }
    ],
    technicalCourses: [
      {
        id: 4,
        name: "การวินิจฉัยโรค",
        score: 95,
        status: "ผ่าน"
      },
      {
        id: 5,
        name: "เวชศาสตร์ครอบครัว",
        score: 88,
        status: "ผ่าน"
      },
      {
        id: 6,
        name: "การแพทย์ฉุกเฉิน",
        score: 92,
        status: "ผ่าน"
      }
    ]
  }
}

export default function HRDetailPage() {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const params = useSearchParams()
  const id = params.get("id")
  const name = params.get("name")

  const studentDetail = id && mockStudentDetails[id as keyof typeof mockStudentDetails] 
    ? mockStudentDetails[id as keyof typeof mockStudentDetails] 
    : null

  const handleDownloadPDF = async () => {
    if (!studentDetail) {
      alert("ไม่พบข้อมูลนักเรียน")
      return
    }

    setIsGeneratingPDF(true)
    
    try {
      // Create an isolated iframe for PDF generation
      const iframe = document.createElement('iframe')
      iframe.style.position = 'absolute'
      iframe.style.top = '-10000px'
      iframe.style.left = '-10000px'
      iframe.style.width = '1200px'
      iframe.style.height = '1600px'
      document.body.appendChild(iframe)

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) throw new Error('Unable to access iframe document')

      // Calculate scores for PDF
      const mandatoryPassedCount = studentDetail.mandatoryCourses.filter((c: any) => c.status === "ผ่าน").length
      const mandatoryTotalCount = studentDetail.mandatoryCourses.length
      const mandatoryScore = (mandatoryPassedCount / mandatoryTotalCount) * 100
      const technicalAverageScore = studentDetail.technicalCourses.reduce((sum: number, c: any) => sum + c.score, 0) / studentDetail.technicalCourses.length
      const totalScore = (mandatoryScore * 0.1) + (technicalAverageScore * 0.9)

      // Create PDF HTML content with isolated styles
      const pdfContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>รายงานรายละเอียดนักเรียน - ${studentDetail.name}</title>
          <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            
            body {
              font-family: 'Noto Sans Thai', sans-serif;
              line-height: 1.6;
              color: #1f2937;
              background: #ffffff;
            }
            
            .container {
              max-width: 1200px;
              margin: 0 auto;
              padding: 40px;
            }
            
            .header {
              text-align: center;
              margin-bottom: 40px;
              border-bottom: 3px solid #008067;
              padding-bottom: 30px;
            }
            
            .logo {
              font-size: 28px;
              font-weight: 700;
              color: #008067;
              margin-bottom: 10px;
            }
            
            .title {
              font-size: 24px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 8px;
            }
            
            .subtitle {
              font-size: 16px;
              color: #6b7280;
            }
            
            .student-info {
              background: linear-gradient(135deg, #008067 0%, #006b57 100%);
              color: white;
              border-radius: 12px;
              padding: 30px;
              margin-bottom: 30px;
            }
            
            .student-name {
              font-size: 22px;
              font-weight: 600;
              margin-bottom: 20px;
            }
            
            .student-details {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 20px;
            }
            
            .detail-item {
              display: flex;
              flex-direction: column;
            }
            
            .detail-label {
              font-size: 14px;
              opacity: 0.8;
              margin-bottom: 4px;
            }
            
            .detail-value {
              font-size: 16px;
              font-weight: 500;
            }
            
            .section {
              margin-bottom: 40px;
            }
            
            .section-header {
              background: #f8fafc;
              border-left: 4px solid #008067;
              padding: 20px;
              margin-bottom: 20px;
              border-radius: 0 8px 8px 0;
            }
            
            .section-title {
              font-size: 20px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 4px;
            }
            
            .section-description {
              font-size: 14px;
              color: #6b7280;
            }
            
            .table {
              width: 100%;
              border-collapse: collapse;
              background: white;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
            
            .table th {
              background: #f9fafb;
              padding: 16px;
              text-align: left;
              font-weight: 600;
              color: #374151;
              font-size: 14px;
              border-bottom: 1px solid #e5e7eb;
            }
            
            .table td {
              padding: 16px;
              border-bottom: 1px solid #f3f4f6;
              font-size: 14px;
            }
            
            .table tr:last-child td {
              border-bottom: none;
            }
            
            .status-badge {
              display: inline-block;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
            }
            
            .status-pass {
              background: #dcfce7;
              color: #166534;
            }
            
            .status-fail {
              background: #fef2f2;
              color: #991b1b;
            }
            
            .progress-bar {
              width: 80px;
              height: 8px;
              background: #e5e7eb;
              border-radius: 4px;
              overflow: hidden;
              display: inline-block;
            }
            
            .progress-fill {
              height: 100%;
              border-radius: 4px;
            }
            
            .progress-high {
              background: #10b981;
            }
            
            .progress-medium {
              background: #f59e0b;
            }
            
            .progress-low {
              background: #ef4444;
            }
            
            .summary-stats {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 30px;
            }
            
            .stat-card {
              background: rgba(255, 255, 255, 0.1);
              border-radius: 8px;
              padding: 20px;
              text-align: center;
            }
            
            .stat-number {
              font-size: 24px;
              font-weight: 700;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 14px;
              opacity: 0.75;
            }
            
            .footer {
              margin-top: 60px;
              padding-top: 30px;
              border-top: 2px solid #e5e7eb;
              text-align: center;
              color: #6b7280;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">ระบบการเรียนรู้ออนไลน์ E-Med</div>
              <div class="title">รายงานรายละเอียดนักเรียน</div>
              <div class="subtitle">ข้อมูลผลการเรียนรู้และความคืบหน้า</div>
            </div>

            <div class="student-info">
              <div class="student-name">${studentDetail.name}</div>
              <div class="student-details">
                <div class="detail-item">
                  <div class="detail-label">แผนก</div>
                  <div class="detail-value">${studentDetail.department}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">ตำแหน่ง</div>
                  <div class="detail-value">${studentDetail.position}</div>
                </div>
              </div>
              
              <div class="summary-stats" style="margin-top: 30px;">
                <div class="stat-card">
                  <div class="stat-number">${mandatoryScore.toFixed(1)}</div>
                  <div class="stat-label">คอร์สบังคับ (10%)</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number">${technicalAverageScore.toFixed(1)}</div>
                  <div class="stat-label">คอร์สเทคนิค (90%)</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number">${totalScore.toFixed(1)}</div>
                  <div class="stat-label">คะแนนรวม</div>
                </div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">คอร์สเรียนบังคับ</div>
                <div class="section-description">รายการคอร์สที่จำเป็นต้องเรียนให้ครบถ้วน</div>
              </div>
              <table class="table">
                <thead>
                  <tr>
                    <th>ลำดับ</th>
                    <th>ชื่อคอร์ส</th>
                    <th>ความคืบหน้า</th>
                    <th>สถานะ</th>
                  </tr>
                </thead>
                <tbody>
                  ${studentDetail.mandatoryCourses.map((course: any, index: number) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td>${course.name}</td>
                      <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                          <div class="progress-bar">
                            <div class="progress-fill ${course.progress >= 80 ? 'progress-high' : course.progress >= 60 ? 'progress-medium' : 'progress-low'}" 
                                 style="width: ${course.progress}%"></div>
                          </div>
                          <span>${course.progress}%</span>
                        </div>
                      </td>
                      <td>
                        <span class="status-badge ${course.status === 'ผ่าน' ? 'status-pass' : 'status-fail'}">
                          ${course.status}
                        </span>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">คอร์สเรียนเทคนิค</div>
                <div class="section-description">รายการคอร์สเฉพาะทางตามสาขาวิชาชีพ</div>
              </div>
              <table class="table">
                <thead>
                  <tr>
                    <th>ลำดับ</th>
                    <th>ชื่อคอร์ส</th>
                    <th>คะแนน</th>
                    <th>สถานะ</th>
                  </tr>
                </thead>
                <tbody>
                  ${studentDetail.technicalCourses.map((course: any, index: number) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td>${course.name}</td>
                      <td>${course.score}</td>
                      <td>
                        <span class="status-badge ${course.status === 'ผ่าน' ? 'status-pass' : 'status-fail'}">
                          ${course.status}
                        </span>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="footer">
              <div>รายงานนี้สร้างโดยระบบการเรียนรู้ออนไลน์ E-Med</div>
              <div>วันที่สร้าง: ${new Date().toLocaleDateString('th-TH')} เวลา: ${new Date().toLocaleTimeString('th-TH')}</div>
            </div>
          </div>
        </body>
        </html>
      `

      // Write content to iframe
      iframeDoc.open()
      iframeDoc.write(pdfContent)
      iframeDoc.close()

      // Wait for fonts to load
      await new Promise(resolve => {
        const checkFontLoad = () => {
          if (iframeDoc.fonts.ready) {
            iframeDoc.fonts.ready.then(resolve)
          } else {
            // Fallback timeout
            setTimeout(resolve, 2000)
          }
        }
        
        if (iframe.contentWindow) {
          iframe.onload = checkFontLoad
        } else {
          checkFontLoad()
        }
      })

      // Additional wait to ensure everything is rendered
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Generate PDF
      const canvas = await html2canvas(iframeDoc.body, {
        width: 1200,
        height: 1600,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      
      const imgWidth = 210
      const pageHeight = 297
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight
      let position = 0

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // Clean up iframe
      document.body.removeChild(iframe)

      // Download PDF
      const fileName = `รายงานรายละเอียด_${studentDetail.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('เกิดข้อผิดพลาดในการสร้าง PDF กรุณาลองอีกครั้ง')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  if (!studentDetail) {
    return (
      <AdminLayout>
        <div className="bg-gray-50 min-h-screen">
          <div className="max-w-4xl mx-auto py-8 px-6">
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">ไม่พบข้อมูลนักเรียน</div>
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ผ่าน":
        return "bg-green-100 text-green-800"
      case "ไม่ผ่าน":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "bg-green-500"
    if (progress >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  // Calculate scores
  const mandatoryPassedCount = studentDetail.mandatoryCourses.filter((c: any) => c.status === "ผ่าน").length
  const mandatoryTotalCount = studentDetail.mandatoryCourses.length
  const mandatoryScore = (mandatoryPassedCount / mandatoryTotalCount) * 100

  const technicalAverageScore = studentDetail.technicalCourses.reduce((sum: number, c: any) => sum + c.score, 0) / studentDetail.technicalCourses.length

  const totalScore = (mandatoryScore * 0.1) + (technicalAverageScore * 0.9)

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        <div className="max-w-6xl mx-auto py-8 px-6">
          {/* Header */}
          <div className="mb-8">
            <nav className="flex mb-4 text-sm">
              <span className="text-gray-500">รายงาน</span>
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-gray-500">รายงาน HR</span>
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-[#008067] font-medium">รายละเอียดนักเรียน</span>
            </nav>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">รายละเอียดนักเรียน: {studentDetail.name}</h1>
                <p className="text-gray-600">{studentDetail.department} - {studentDetail.position}</p>
              </div>
              <button 
                onClick={handleDownloadPDF}
                disabled={isGeneratingPDF}
                className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                  isGeneratingPDF
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-[#008067] text-white hover:bg-[#006b57]'
                }`}
              >
                <Download size={16} className="mr-2" />
                {isGeneratingPDF ? 'กำลังสร้าง PDF...' : 'ดาวน์โหลด PDF'}
              </button>
            </div>
          </div>

          {/* Top Half - Mandatory Courses */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center mb-6">
              <Award className="w-6 h-6 text-[#008067] mr-3" />
              <h2 className="text-xl font-bold text-[#008067]">คอร์สเรียนบังคับ</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">คอร์ส</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ความคืบหน้า</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">สถานะ</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {studentDetail.mandatoryCourses.map((course: any) => (
                    <tr key={course.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{course.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                            <div 
                              className={`h-2 rounded-full ${getProgressColor(course.progress)}`}
                              style={{ width: `${course.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">{course.progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(course.status)}`}>
                          {course.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Bottom Half - Technical Courses */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center mb-6">
              <BookOpen className="w-6 h-6 text-[#008067] mr-3" />
              <h2 className="text-xl font-bold text-[#008067]">คอร์สเรียนเทคนิค</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">คอร์ส</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">คะแนน</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">สถานะ</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {studentDetail.technicalCourses.map((course: any) => (
                    <tr key={course.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{course.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{course.score}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(course.status)}`}>
                          {course.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Summary Section */}
          <div className="bg-gradient-to-r from-[#008067] to-[#006b57] rounded-lg shadow-sm p-6 text-white">
            <div className="flex items-center mb-4">
              <TrendingUp className="w-6 h-6 mr-3" />
              <h2 className="text-xl font-bold">สรุปคะแนนรวม</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-sm opacity-90">คอร์สบังคับ (10%)</div>
                <div className="text-2xl font-bold">{mandatoryScore.toFixed(1)}</div>
                <div className="text-xs opacity-75">{mandatoryPassedCount}/{mandatoryTotalCount} ผ่าน</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-sm opacity-90">คอร์สเทคนิค (90%)</div>
                <div className="text-2xl font-bold">{technicalAverageScore.toFixed(1)}</div>
                <div className="text-xs opacity-75">คะแนนเฉลี่ย</div>
              </div>
              <div className="bg-white/20 rounded-lg p-4 border-2 border-white/30">
                <div className="text-sm opacity-90">คะแนนรวม</div>
                <div className="text-3xl font-bold">{totalScore.toFixed(1)}</div>
                <div className="text-xs opacity-75">คะแนนสุดท้าย</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
