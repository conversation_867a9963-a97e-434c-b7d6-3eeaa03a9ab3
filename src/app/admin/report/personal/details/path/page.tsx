"use client"

import AdminLayout from "@/components/admin/layout"
import { useSearchParams } from "next/navigation"
import { BookOpen, Route, ChevronDown, ChevronRight, Play, FileText, CheckCircle } from "lucide-react"
import { useState } from "react"

type PathDetail = { 
  name: string; 
  courses: { 
    id: number; 
    name: string;
    content: {
      id: number;
      title: string;
      type: 'video' | 'text' | 'quiz';
      duration?: string;
      completed: boolean;
    }[];
  }[] 
}

const mockPathDetails: Record<number, PathDetail> = {
  1: {
    name: "เส้นทางการเรียนรู้พยาบาลเบื้องต้น",
    courses: [
      { 
        id: 1, 
        name: "หลักการพยาบาลเบื้องต้น",
        content: [
          { id: 1, title: "บทนำ: ความสำคัญของการพยาบาล", type: 'video', duration: '15 นาที', completed: true },
          { id: 2, title: "หลักการดูแลผู้ป่วย", type: 'text', completed: true },
          { id: 3, title: "การประเมินสภาวะผู้ป่วย", type: 'video', duration: '20 นาที', completed: false },
          { id: 4, title: "แบบทดสอบท้ายบท", type: 'quiz', completed: false }
        ]
      },
      { 
        id: 4, 
        name: "การจัดการยาและเวชภัณฑ์",
        content: [
          { id: 5, title: "ประเภทของยา", type: 'text', completed: false },
          { id: 6, title: "วิธีการจัดเก็บยา", type: 'video', duration: '12 นาที', completed: false },
          { id: 7, title: "การคำนวณขนาดยา", type: 'text', completed: false },
          { id: 8, title: "แบบทดสอบการจัดการยา", type: 'quiz', completed: false }
        ]
      }
    ]
  },
  2: {
    name: "เส้นทางการเรียนรู้การดูแลผู้ป่วยวิกฤต",
    courses: [
      { 
        id: 2, 
        name: "การดูแลผู้ป่วยวิกฤต",
        content: [
          { id: 9, title: "การประเมินผู้ป่วยวิกฤต", type: 'video', duration: '25 นาที', completed: true },
          { id: 10, title: "เครื่องมือช่วยชีวิต", type: 'text', completed: false },
          { id: 11, title: "การใช้เครื่องช่วยหายใจ", type: 'video', duration: '18 นาที', completed: false }
        ]
      },
      { 
        id: 3, 
        name: "เวชศาสตร์ฉุกเฉิน",
        content: [
          { id: 12, title: "การปฐมพยาบาลเบื้องต้น", type: 'video', duration: '30 นาที', completed: false },
          { id: 13, title: "การจัดการเหตุฉุกเฉิน", type: 'text', completed: false }
        ]
      }
    ]
  },
  3: {
    name: "เส้นทางการเรียนรู้เวชศาสตร์ฉุกเฉิน",
    courses: [
      { 
        id: 3, 
        name: "เวชศาสตร์ฉุกเฉิน",
        content: [
          { id: 14, title: "การปฐมพยาบาลเบื้องต้น", type: 'video', duration: '30 นาที', completed: true },
          { id: 15, title: "การจัดการเหตุฉุกเฉิน", type: 'text', completed: true }
        ]
      },
      { 
        id: 1, 
        name: "หลักการพยาบาลเบื้องต้น",
        content: [
          { id: 16, title: "บทนำ: ความสำคัญของการพยาบาล", type: 'video', duration: '15 นาที', completed: false },
          { id: 17, title: "หลักการดูแลผู้ป่วย", type: 'text', completed: false }
        ]
      }
    ]
  }
}

export default function PathDetailsPage() {
  const params = useSearchParams()
  const id = params.get("id")
  const name = params.get("name")
  const [expandedCourses, setExpandedCourses] = useState<number[]>([])

  const idNum = id ? parseInt(id, 10) : undefined
  const pathDetail = idNum && mockPathDetails[idNum] ? mockPathDetails[idNum] : null

  const title = pathDetail ? `รายละเอียดเส้นทาง: ${pathDetail.name}` : "ไม่พบข้อมูล"

  const toggleCourse = (courseId: number) => {
    setExpandedCourses(prev => 
      prev.includes(courseId) 
        ? prev.filter(id => id !== courseId)
        : [...prev, courseId]
    )
  }

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'video': return <Play className="w-4 h-4" />
      case 'text': return <FileText className="w-4 h-4" />
      case 'quiz': return <BookOpen className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'video': return 'วิดีโอ'
      case 'text': return 'เอกสาร'
      case 'quiz': return 'แบบทดสอบ'
      default: return 'เนื้อหา'
    }
  }

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto py-8 px-6">
          {/* Header with breadcrumb */}
          <div className="mb-8">
            <nav className="flex mb-4 text-sm">
              <span className="text-gray-500">รายงาน</span>
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-gray-500">รายงานส่วนบุคคล</span>
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-[#008067] font-medium">รายละเอียดเส้นทาง</span>
            </nav>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
            <p className="text-gray-600">รายการคอร์สในเส้นทางการเรียนรู้</p>
          </div>

          {/* Content */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            {!pathDetail ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg">ไม่พบข้อมูล</div>
              </div>
            ) : (
              <div>
                {/* Path Header */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <Route className="w-6 h-6 text-[#008067] mr-3" />
                    <h2 className="text-2xl font-bold text-[#008067]">{pathDetail.name}</h2>
                  </div>
                  <div className="h-1 bg-gradient-to-r from-[#008067] to-[#006b57] rounded-full w-24 mb-6"></div>
                </div>

                {/* Timeline Content */}
                <div className="relative">
                  {/* Vertical line */}
                  <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[#008067] to-[#006b57] opacity-30"></div>
                  
                  {pathDetail.courses.map((course, courseIdx) => {
                    const isExpanded = expandedCourses.includes(course.id)
                    const completedContent = course.content.filter(item => item.completed).length
                    const totalContent = course.content.length
                    const progressPercentage = (completedContent / totalContent) * 100

                    return (
                      <div key={courseIdx} className="relative mb-8 last:mb-0">
                        {/* Timeline dot */}
                        <div className="absolute left-4 flex items-center justify-center w-5 h-5 bg-[#008067] rounded-full border-4 border-white shadow-lg z-10">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                        
                        {/* Course Content */}
                        <div className="ml-16">
                          <div className="bg-gradient-to-r from-gray-50 to-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                            {/* Course Header - Clickable */}
                            <button
                              onClick={() => toggleCourse(course.id)}
                              className="w-full p-6 text-left hover:bg-gray-50 transition-colors duration-200 rounded-lg"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-start flex-1">
                                  <div className="flex-shrink-0 w-8 h-8 bg-[#008067] rounded-full flex items-center justify-center mr-4">
                                    <span className="text-white font-bold text-sm">{courseIdx + 1}</span>
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center">
                                      <BookOpen className="w-5 h-5 text-[#008067] mr-2" />
                                      <h3 className="text-lg font-semibold text-gray-800">{course.name}</h3>
                                    </div>
                                    <div className="mt-2 flex items-center space-x-4">
                                      <span className="text-sm text-gray-600">
                                        {completedContent}/{totalContent} เนื้อหา
                                      </span>
                                      <div className="flex items-center space-x-2">
                                        <div className="w-20 bg-gray-200 rounded-full h-2">
                                          <div 
                                            className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                            style={{ width: `${progressPercentage}%` }}
                                          ></div>
                                        </div>
                                        <span className="text-xs text-gray-600">{Math.round(progressPercentage)}%</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex-shrink-0 ml-4">
                                  {isExpanded ? (
                                    <ChevronDown className="w-5 h-5 text-gray-400" />
                                  ) : (
                                    <ChevronRight className="w-5 h-5 text-gray-400" />
                                  )}
                                </div>
                              </div>
                            </button>

                            {/* Collapsible Content */}
                            {isExpanded && (
                              <div className="px-6 pb-6 border-t border-gray-100">
                                <div className="mt-4 space-y-3">
                                  {course.content.map((content, contentIdx) => (
                                    <div 
                                      key={content.id} 
                                      className={`flex items-center p-3 rounded-lg border transition-all duration-200 ${
                                        content.completed 
                                          ? 'bg-green-50 border-green-200' 
                                          : 'bg-white border-gray-200 hover:bg-gray-50'
                                      }`}
                                    >
                                      <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                                        content.completed ? 'bg-green-500' : 'bg-gray-300'
                                      }`}>
                                        {content.completed ? (
                                          <CheckCircle className="w-4 h-4 text-white" />
                                        ) : (
                                          <span className="text-xs font-bold text-white">{contentIdx + 1}</span>
                                        )}
                                      </div>
                                      
                                      <div className="flex-1">
                                        <div className="flex items-center space-x-2">
                                          <div className={`p-1 rounded ${
                                            content.type === 'video' ? 'bg-red-100 text-red-600' :
                                            content.type === 'text' ? 'bg-blue-100 text-blue-600' :
                                            'bg-yellow-100 text-yellow-600'
                                          }`}>
                                            {getContentIcon(content.type)}
                                          </div>
                                          <span className="font-medium text-gray-800">{content.title}</span>
                                        </div>
                                        <div className="flex items-center space-x-3 mt-1">
                                          <span className="text-xs text-gray-500">{getContentTypeLabel(content.type)}</span>
                                          {content.duration && (
                                            <>
                                              <span className="text-xs text-gray-400">•</span>
                                              <span className="text-xs text-gray-500">{content.duration}</span>
                                            </>
                                          )}
                                        </div>
                                      </div>

                                      {content.completed && (
                                        <div className="text-green-600 text-sm font-medium">เสร็จแล้ว</div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
