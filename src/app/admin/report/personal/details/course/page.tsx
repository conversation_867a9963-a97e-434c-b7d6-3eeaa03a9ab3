"use client"

import AdminLayout from "@/components/admin/layout"
import { useSearchParams } from "next/navigation"
import { BookOpen } from "lucide-react"

type CourseDetail = { name: string; content: string }

const mockCourseDetails: Record<number, CourseDetail> = {
  1: {
    name: "หลักการพยาบาลเบื้องต้น",
    content: `
      <ul class='list-disc pl-5'>
        <li>บทนำเกี่ยวกับวิชาชีพพยาบาลและจรรยาบรรณ</li>
        <li>บทบาทและหน้าที่ของพยาบาลในโรงพยาบาล</li>
        <li>การสื่อสารกับผู้ป่วยและทีมสุขภาพ</li>
        <li>การดูแลสุขอนามัยเบื้องต้น</li>
        <li>การวัดสัญญาณชีพและการบันทึกผล</li>
        <li>การป้องกันการติดเชื้อในโรงพยาบาล</li>
      </ul>
    `
  },
  2: {
    name: "การดูแลผู้ป่วยวิกฤต",
    content: `
      <ul class='list-disc pl-5'>
        <li>แนวคิดพื้นฐานของการดูแลผู้ป่วยวิกฤต</li>
        <li>การประเมินและติดตามอาการผู้ป่วย</li>
        <li>การใช้เครื่องมือและอุปกรณ์ช่วยชีวิต</li>
        <li>การดูแลทางเดินหายใจและการให้ออกซิเจน</li>
        <li>การดูแลผู้ป่วยที่ใช้เครื่องช่วยหายใจ</li>
        <li>การสื่อสารกับญาติและทีมสหวิชาชีพ</li>
      </ul>
    `
  },
  3: {
    name: "เวชศาสตร์ฉุกเฉิน",
    content: `
      <ul class='list-disc pl-5'>
        <li>หลักการปฐมพยาบาลเบื้องต้น</li>
        <li>การดูแลผู้ป่วยอุบัติเหตุและฉุกเฉิน</li>
        <li>การประเมินและจัดลำดับความสำคัญของผู้ป่วย</li>
        <li>การช่วยฟื้นคืนชีพ (CPR)</li>
        <li>การเคลื่อนย้ายผู้ป่วยอย่างปลอดภัย</li>
        <li>การเตรียมความพร้อมรับสถานการณ์ฉุกเฉิน</li>
      </ul>
    `
  },
  4: {
    name: "การจัดการยาและเวชภัณฑ์",
    content: `
      <ul class='list-disc pl-5'>
        <li>หลักการบริหารยาอย่างปลอดภัย</li>
        <li>การคำนวณและเตรียมยา</li>
        <li>การเก็บรักษาและตรวจสอบเวชภัณฑ์</li>
        <li>การบันทึกและรายงานการใช้ยา</li>
        <li>การป้องกันข้อผิดพลาดทางยา</li>
        <li>การให้ความรู้ผู้ป่วยเกี่ยวกับการใช้ยา</li>
      </ul>
    `
  }
}

export default function CourseDetailsPage() {
  const params = useSearchParams()
  const id = params.get("id")
  const name = params.get("name")

  const idNum = id ? parseInt(id, 10) : undefined
  const courseDetail = idNum && mockCourseDetails[idNum] ? mockCourseDetails[idNum] : null

  const title = courseDetail ? `รายละเอียดคอร์ส: ${courseDetail.name}` : "ไม่พบข้อมูล"

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto py-8 px-6">
          {/* Header with breadcrumb */}
          <div className="mb-8">
            <nav className="flex mb-4 text-sm">
              <span className="text-gray-500">รายงาน</span>
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-gray-500">รายงานส่วนบุคคล</span>
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-[#008067] font-medium">รายละเอียดคอร์ส</span>
            </nav>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
            <p className="text-gray-600">รายละเอียดเนื้อหาการเรียนรู้</p>
          </div>

          {/* Content */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            {!courseDetail ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg">ไม่พบข้อมูล</div>
              </div>
            ) : (
              <div>
                {/* Course Header */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <BookOpen className="w-6 h-6 text-[#008067] mr-3" />
                    <h2 className="text-2xl font-bold text-[#008067]">{courseDetail.name}</h2>
                  </div>
                  <div className="h-1 bg-gradient-to-r from-[#008067] to-[#006b57] rounded-full w-24 mb-6"></div>
                </div>

                {/* Timeline Content */}
                <div className="relative">
                  {/* Vertical line */}
                  <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[#008067] to-[#006b57] opacity-30"></div>
                  
                  {(() => {
                    // Extract li items from the HTML string
                    const liMatches = courseDetail.content.match(/<li>(.*?)<\/li>/g) || [];
                    const liContents = liMatches.map(li => li.replace(/<li>(.*?)<\/li>/, '$1'));
                    
                    return liContents.length === 0 ? (
                      <div className="text-gray-500 ml-16">ไม่พบรายละเอียด</div>
                    ) : (
                      liContents.map((li, liIdx) => (
                        <div key={liIdx} className="relative mb-8 last:mb-0">
                          {/* Timeline dot */}
                          <div className="absolute left-4 flex items-center justify-center w-5 h-5 bg-[#008067] rounded-full border-4 border-white shadow-lg">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                          
                          {/* Content */}
                          <div className="ml-16">
                            <div className="bg-gradient-to-r from-gray-50 to-white rounded-lg p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                              <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-[#008067] rounded-full flex items-center justify-center mr-4">
                                  <span className="text-white font-bold text-sm">{liIdx + 1}</span>
                                </div>
                                <div className="flex-1">
                                  <div className="text-gray-800 font-medium leading-relaxed">
                                    <span dangerouslySetInnerHTML={{ __html: li }} />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
