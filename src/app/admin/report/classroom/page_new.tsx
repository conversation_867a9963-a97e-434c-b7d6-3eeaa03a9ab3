"use client"

import { useState } from "react"
import { Search, Download, Filter, Users, BookOpen, TrendingUp, ChevronDown, X } from "lucide-react"
import AdminLayout from "@/components/admin/layout"
import { useRouter } from "next/navigation"
import Swal from "sweetalert2"

// Mock classroom list for dropdown
const mockClassroomList = [
  {
    id: "CLASS001",
    name: "หลักการพยาบาลเบื้องต้น - กลุ่ม A",
    instructor: "อาจารย์ดร.สุนีย์ ใจดี",
    startDate: "2024-01-15",
    endDate: "2024-04-15",
    totalStudents: 25,
    activeStudents: 23,
    completedStudents: 18,
    studentResults: [
      {
        id: 1,
        name: "นางสาวสุภา ใจดี",
        progress: 100,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 85,
        type: "course"
      },
      {
        id: 2,
        name: "นายสมชาย รักดี",
        progress: 95,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 92,
        type: "course"
      },
      {
        id: 3,
        name: "นางสาวมานี ใจงาม",
        progress: 88,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 78,
        type: "course"
      }
    ]
  },
  {
    id: "CLASS002",
    name: "เภสัชกรรมคลินิก - กลุ่ม B",
    instructor: "ผศ.ดร.วิชัย เรียนดี",
    startDate: "2024-02-01",
    endDate: "2024-05-01",
    totalStudents: 20,
    activeStudents: 18,
    completedStudents: 15,
    studentResults: [
      {
        id: 4,
        name: "นายธนาคาร เรียนดี",
        progress: 98,
        courseOrPath: "เภสัชกรรมคลินิก",
        totalScore: 89,
        type: "course"
      }
    ]
  }
]

export default function ClassroomReportPage() {
  const router = useRouter()
  const [selectedClass, setSelectedClass] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [showDropdown, setShowDropdown] = useState(false)
  const [filterType, setFilterType] = useState("all")
  const [sortBy, setSortBy] = useState("name")

  // Filter classrooms based on search term
  const filteredClassrooms = mockClassroomList.filter(classroom =>
    classroom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classroom.instructor.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get students for selected class
  const sortedStudents = selectedClass ? selectedClass.studentResults.sort((a: any, b: any) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name)
      case "progress":
        return b.progress - a.progress
      case "score":
        return b.totalScore - a.totalScore
      default:
        return 0
    }
  }) : []

  // Statistics
  const passRate = selectedClass ? (sortedStudents.filter((s: any) => s.totalScore >= 70).length / (sortedStudents.length || 1)) * 100 : 0
  const averageScore = selectedClass ? sortedStudents.reduce((sum: number, s: any) => sum + s.totalScore, 0) / (sortedStudents.length || 1) : 0

  // PDF Download function using backend API
  const handleDownloadPDF = async () => {
    if (!selectedClass) {
      Swal.fire({
        icon: 'warning',
        title: 'กรุณาเลือกห้องเรียน',
        text: 'กรุณาเลือกห้องเรียนก่อนดาวน์โหลดรายงาน',
        confirmButtonText: 'ตกลง'
      })
      return
    }

    try {
      // Prepare request data
      const requestData = {
        courseId: selectedClass.id,
        pathId: null, // Add path support later if needed
        dateStart: "",
        dateEnd: "",
        includeDetails: true
      }

      // Show loading notification
      Swal.fire({
        title: 'กำลังสร้าง PDF...',
        text: 'กรุณารอสักครู่',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading()
        }
      })

      // Call backend API
      const response = await fetch('/api/reports/classroom/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Get PDF blob
      const blob = await response.blob()
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      
      // Set filename
      const fileName = `รายงานห้องเรียน_${selectedClass.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
      
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      
      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      // Close loading and show success
      Swal.close()
      Swal.fire({
        icon: 'success',
        title: 'สำเร็จ!',
        text: 'ดาวน์โหลด PDF เรียบร้อยแล้ว',
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error generating PDF:', error)
      Swal.close()
      Swal.fire({
        icon: 'error',
        title: 'เกิดข้อผิดพลาด!',
        text: 'ไม่สามารถสร้าง PDF ได้ กรุณาลองอีกครั้ง',
        confirmButtonText: 'ตกลง'
      })
    }
  }

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-[#008067] rounded-lg">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">รายงานห้องเรียน</h1>
                <p className="text-sm text-gray-600">ตรวจสอบผลการเรียนรู้และความคืบหน้าของห้องเรียน</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button 
                onClick={handleDownloadPDF}
                disabled={!selectedClass}
                className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                  !selectedClass
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-[#008067] text-white hover:bg-[#006b57]'
                }`}
              >
                <Download className="w-4 h-4 mr-2" />
                ดาวน์โหลด PDF
              </button>
            </div>
          </div>

          {/* Classroom Selection */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              เลือกห้องเรียน
            </label>
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="w-full max-w-md flex items-center justify-between px-4 py-3 bg-white border border-gray-300 rounded-lg shadow-sm hover:border-[#008067] focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent transition-colors"
              >
                <span className={selectedClass ? "text-gray-900" : "text-gray-500"}>
                  {selectedClass ? selectedClass.name : "เลือกห้องเรียน..."}
                </span>
                <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
              </button>

              {showDropdown && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
                  {(() => {
                    const classroomsToShow = searchTerm ? filteredClassrooms : mockClassroomList

                    if (classroomsToShow.length === 0) {
                      return (
                        <div className="px-4 py-3 text-center text-gray-500">
                          {searchTerm ? 'ไม่พบห้องเรียนที่ค้นหา' : 'ไม่มีข้อมูลห้องเรียน'}
                        </div>
                      )
                    }

                    return classroomsToShow.map((classroom) => (
                      <button
                        key={classroom.id}
                        onClick={() => {
                          setSelectedClass(classroom)
                          setShowDropdown(false)
                        }}
                        className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
                      >
                        <div className="font-medium text-gray-900">{classroom.name}</div>
                        <div className="text-sm text-gray-500">ผู้สอน: {classroom.instructor}</div>
                        <div className="text-xs text-gray-400 mt-1">
                          นักเรียน: {classroom.totalStudents} คน | ใช้งาน: {classroom.activeStudents} คน
                        </div>
                      </button>
                    ))
                  })()}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Selected Classroom Info */}
        {selectedClass && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">ข้อมูลห้องเรียน</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-blue-600 font-medium">นักเรียนทั้งหมด</p>
                    <p className="text-2xl font-bold text-blue-900">{selectedClass.totalStudents}</p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <TrendingUp className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-green-600 font-medium">อัตราผ่าน</p>
                    <p className="text-2xl font-bold text-green-900">{passRate.toFixed(1)}%</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <BookOpen className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-purple-600 font-medium">คะแนนเฉลี่ย</p>
                    <p className="text-2xl font-bold text-purple-900">{averageScore.toFixed(1)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
