"use client"

import { useState } from "react"
import { Search, Download, Filter, Users, BookOpen, TrendingUp, ChevronDown, X } from "lucide-react"
import AdminLayout from "@/components/admin/layout"
import { useRouter } from "next/navigation"
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

// Mock classroom list for dropdown
const mockClassroomList = [
  {
    id: "CLASS001",
    name: "หลักการพยาบาลเบื้องต้น - กลุ่ม A",
    instructor: "อาจารย์ดร.สุนีย์ ใจดี",
    startDate: "2024-01-15",
    endDate: "2024-04-15",
    totalStudents: 25,
    activeStudents: 23,
    completedStudents: 18,
    studentResults: [
      {
        id: 1,
        name: "นางสาวสุภา ใจดี",
        progress: 100,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 85,
        type: "course"
      },
      {
        id: 2,
        name: "นายสมชาย รักดี",
        progress: 95,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 92,
        type: "course"
      },
      {
        id: 3,
        name: "นางสาวมานี ใจงาม",
        progress: 88,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 78,
        type: "course"
      },
      {
        id: 4,
        name: "นายธนาคาร เรียนดี",
        progress: 98,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 88,
        type: "course"
      },
      {
        id: 5,
        name: "นางสาวปรียา ขยันเรียน",
        progress: 82,
        courseOrPath: "หลักการพยาบาลเบื้องต้น",
        totalScore: 75,
        type: "course"
      }
    ]
  },
  {
    id: "CLASS002", 
    name: "การดูแลผู้ป่วยวิกฤต - กลุ่ม B",
    instructor: "อาจารย์สมชาย รักดี",
    startDate: "2024-02-01",
    endDate: "2024-05-01",
    totalStudents: 30,
    activeStudents: 28,
    completedStudents: 20,
    studentResults: [
      {
        id: 6,
        name: "นางสาววิลาวัลย์ สุขใส",
        progress: 94,
        courseOrPath: "การดูแลผู้ป่วยวิกฤต",
        totalScore: 89,
        type: "course"
      },
      {
        id: 7,
        name: "นายกิตติพงษ์ เก่งเรียน",
        progress: 87,
        courseOrPath: "การดูแลผู้ป่วยวิกฤต",
        totalScore: 82,
        type: "course"
      },
      {
        id: 8,
        name: "นางสาวนิตยา ขยันดี",
        progress: 91,
        courseOrPath: "การดูแลผู้ป่วยวิกฤต",
        totalScore: 86,
        type: "course"
      },
      {
        id: 9,
        name: "นายวีระชัย มั่นใจ",
        progress: 76,
        courseOrPath: "การดูแลผู้ป่วยวิกฤต",
        totalScore: 73,
        type: "course"
      },
      {
        id: 10,
        name: "นางสาวสุดใจ ใฝ่เรียน",
        progress: 85,
        courseOrPath: "การดูแลผู้ป่วยวิกฤต",
        totalScore: 79,
        type: "course"
      }
    ]
  },
  {
    id: "CLASS003",
    name: "เวชศาสตร์ฉุกเฉิน - กลุ่ม C", 
    instructor: "อาจารย์มานี ใจงาม",
    startDate: "2024-03-10",
    endDate: "2024-06-10",
    totalStudents: 20,
    activeStudents: 19,
    completedStudents: 15,
    studentResults: [
      {
        id: 11,
        name: "นายอนุชา ขมความ",
        progress: 89,
        courseOrPath: "เวชศาสตร์ฉุกเฉิน",
        totalScore: 84,
        type: "course"
      },
      {
        id: 12,
        name: "นางสาวชลธิชา อิ่มใจ",
        progress: 92,
        courseOrPath: "เวชศาสตร์ฉุกเฉิน",
        totalScore: 87,
        type: "course"
      },
      {
        id: 13,
        name: "นายธนภัทร มั่นคง",
        progress: 65,
        courseOrPath: "เวชศาสตร์ฉุกเฉิน",
        totalScore: 68,
        type: "course"
      },
      {
        id: 14,
        name: "นางสาวรัชนี เพียรพยายาม",
        progress: 88,
        courseOrPath: "เวชศาสตร์ฉุกเฉิน",
        totalScore: 81,
        type: "course"
      },
      {
        id: 15,
        name: "นายปิยะ ตั้งใจ",
        progress: 77,
        courseOrPath: "เวชศาสตร์ฉุกเฉิน",
        totalScore: 74,
        type: "course"
      }
    ]
  }
]

// Mock filter options
const filterOptions = [
  { label: "คอร์ส", value: "course" },
  { label: "เส้นทางการเรียน", value: "path" },
  { label: "ภาพรวม", value: "overall" },
]

// Mock course/path data for demonstration
const mockCoursePathList = [
  { id: 1, type: "course", name: "หลักการพยาบาลเบื้องต้น" },
  { id: 2, type: "course", name: "การดูแลผู้ป่วยเบื้องต้น" },
  { id: 3, type: "path", name: "เส้นทางการเรียนรู้พยาบาลใหม่" },
]

type Classroom = {
  id: string
  name: string
  instructor: string
  startDate: string
  endDate: string
  totalStudents: number
  activeStudents: number
  completedStudents: number
  studentResults: {
    id: number
    name: string
    progress: number
    courseOrPath: string
    totalScore: number
    type: string
  }[]
}

export default function ClassroomReportPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [studentSearchTerm, setStudentSearchTerm] = useState("")
  const [selectedClass, setSelectedClass] = useState<Classroom | null>(null)
  const [filterType, setFilterType] = useState("overall")
  const [showDropdown, setShowDropdown] = useState(false)
  const router = useRouter()

  // Filter classrooms based on search term
  const filteredClassrooms = mockClassroomList.filter(c => 
    c.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    c.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    c.instructor.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleClassroomSelect = (classroom: Classroom) => {
    setSelectedClass(classroom)
    setSearchTerm(classroom.name)
    setShowDropdown(false)
  }

  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    setShowDropdown(value.length > 0 || showDropdown)
    
    // If the search term exactly matches a classroom name, auto-select it
    const exactMatch = mockClassroomList.find(c => 
      c.name.toLowerCase() === value.toLowerCase()
    )
    if (exactMatch) {
      setSelectedClass(exactMatch)
    }
  }

  const handleInputFocus = () => {
    setShowDropdown(true)
  }

  const handleInputBlur = () => {
    // Delay hiding dropdown to allow clicks on dropdown items
    setTimeout(() => setShowDropdown(false), 200)
  }

  // Function to handle student name click navigation
  const handleStudentClick = (student: any) => {
    // Determine if it's a course or path and navigate to appropriate detail page
    if (student.type === "course") {
      // Navigate to course detail page
      const courseId = mockCoursePathList.find(item => item.name === student.courseOrPath)?.id || 1
      router.push(`/admin/report/personal/details/course?id=${courseId}&name=${encodeURIComponent(student.courseOrPath)}`)
    } else if (student.type === "path") {
      // Navigate to path detail page
      const pathId = mockCoursePathList.find(item => item.name === student.courseOrPath)?.id || 1
      router.push(`/admin/report/personal/details/path?id=${pathId}&name=${encodeURIComponent(student.courseOrPath)}`)
    } else {
      // For overall filter, navigate to personal report page
      router.push(`/admin/report/personal`)
    }
  }

  // Filtered students by filterType and search term
  const filteredStudents = selectedClass ? selectedClass.studentResults.filter(s => {
    const matchesFilter = filterType === "overall" ? true : s.type === filterType
    const matchesSearch = studentSearchTerm === "" || s.name.toLowerCase().includes(studentSearchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  }) : []
  // Sort students by totalScore descending for rating
  const sortedStudents = [...filteredStudents].sort((a, b) => b.totalScore - a.totalScore)

  // Statistics
  const passRate = (sortedStudents.filter(s => s.totalScore >= 70).length / (sortedStudents.length || 1)) * 100
  const averageScore = sortedStudents.reduce((sum, s) => sum + s.totalScore, 0) / (sortedStudents.length || 1)

  // PDF Download function (Mock implementation with Thai support)
  const handleDownloadPDF = async () => {
    if (!selectedClass) {
      alert("กรุณาเลือกห้องเรียนก่อนดาวน์โหลดรายงาน")
      return
    }

    try {
      // Show loading state
      console.log('Generating PDF with Thai support...')

      // Create a completely isolated iframe for PDF generation
      const iframe = document.createElement('iframe')
      iframe.style.position = 'absolute'
      iframe.style.left = '-9999px'
      iframe.style.top = '0'
      iframe.style.width = '794px' // A4 width in pixels
      iframe.style.height = '1123px' // A4 height in pixels
      iframe.style.border = 'none'
      
      document.body.appendChild(iframe)
      
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) throw new Error('Cannot access iframe document')

      // Generate HTML content for PDF with Thai support
      const filteredStudentsForPDF = sortedStudents.slice(0, 20)

      const currentDate = new Date().toLocaleDateString('th-TH', {
        year: 'numeric',
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })

      const filterLabel = filterType === 'course' ? 'คอร์ส' : filterType === 'path' ? 'เส้นทางการเรียน' : 'ภาพรวม'

      // Write complete HTML document to iframe
      iframeDoc.open()
      iframeDoc.write(`
        <!DOCTYPE html>
        <html lang="th">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>รายงานห้องเรียน</title>
          <link rel="preconnect" href="https://fonts.googleapis.com">
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@100..900&display=swap" rel="stylesheet">
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              font-family: 'Noto Sans Thai', Arial, sans-serif !important;
            }
            body {
              background: white;
              color: black;
              padding: 20px;
              font-size: 14px;
              line-height: 1.6;
              width: 794px;
              min-height: 1123px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 2px solid rgb(0, 128, 103);
            }
            .header h1 {
              font-size: 24px;
              font-weight: 700;
              color: rgb(0, 128, 103);
              margin-bottom: 10px;
            }
            .header .date {
              font-size: 12px;
              color: rgb(102, 102, 102);
            }
            .classroom-info {
              background: rgb(0, 128, 103);
              color: white;
              padding: 20px;
              border-radius: 8px;
              margin-bottom: 30px;
            }
            .classroom-info h2 {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 15px;
            }
            .info-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
            }
            .info-item .label {
              font-size: 12px;
              opacity: 0.8;
              margin-bottom: 2px;
            }
            .info-item .value {
              font-weight: 500;
            }
            .filter-info {
              background: rgb(240, 253, 244);
              border: 1px solid rgb(187, 247, 208);
              border-radius: 6px;
              padding: 10px 15px;
              margin-bottom: 20px;
              font-size: 13px;
              color: rgb(22, 101, 52);
            }
            .stats-grid {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 30px;
            }
            .stat-card {
              background: rgb(248, 249, 250);
              padding: 20px;
              border-radius: 8px;
              text-align: center;
              border-left: 4px solid rgb(0, 128, 103);
            }
            .stat-card h3 {
              font-size: 12px;
              color: rgb(102, 102, 102);
              margin-bottom: 5px;
              text-transform: uppercase;
            }
            .stat-card .value {
              font-size: 24px;
              font-weight: 700;
              color: rgb(0, 128, 103);
            }
            .students-section h2 {
              font-size: 18px;
              font-weight: 600;
              color: rgb(51, 51, 51);
              margin-bottom: 15px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
              background: white;
            }
            th, td {
              padding: 12px 8px;
              text-align: left;
              border-bottom: 1px solid rgb(229, 231, 235);
            }
            th {
              background: rgb(249, 250, 251);
              font-weight: 600;
              color: rgb(55, 65, 81);
              font-size: 12px;
            }
            tr:nth-child(even) {
              background: rgb(249, 250, 251);
            }
            .rank {
              font-weight: 700;
              color: rgb(0, 128, 103);
            }
            .progress-container {
              display: flex;
              align-items: center;
            }
            .progress-bar {
              width: 100px;
              height: 8px;
              background: rgb(229, 231, 235);
              border-radius: 4px;
              margin-right: 8px;
              overflow: hidden;
            }
            .progress-fill {
              height: 100%;
              background: rgb(0, 128, 103);
              border-radius: 4px;
            }
            .progress-text {
              font-size: 11px;
              color: rgb(107, 114, 128);
            }
            .footer {
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid rgb(229, 231, 235);
              text-align: center;
              font-size: 12px;
              color: rgb(107, 114, 128);
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>รายงานห้องเรียน</h1>
            <div class="date">สร้างเมื่อ: ${currentDate}</div>
          </div>
          
          <div class="classroom-info">
            <h2>${selectedClass.name}</h2>
            <div class="info-grid">
              <div class="info-item">
                <div class="label">ผู้สอน:</div>
                <div class="value">${selectedClass.instructor}</div>
              </div>
              <div class="info-item">
                <div class="label">วันเริ่ม:</div>
                <div class="value">${selectedClass.startDate}</div>
              </div>
              <div class="info-item">
                <div class="label">วันสิ้นสุด:</div>
                <div class="value">${selectedClass.endDate}</div>
              </div>
              <div class="info-item">
                <div class="label">นักเรียนทั้งหมด:</div>
                <div class="value">${selectedClass.totalStudents} คน</div>
              </div>
            </div>
          </div>
          
          ${filterType !== 'overall' ? `
            <div class="filter-info">
              <strong>ตัวกรอง:</strong> ${filterLabel}
            </div>
          ` : ''}
          
          <div class="stats-grid">
            <div class="stat-card">
              <h3>นักเรียนทั้งหมด</h3>
              <div class="value">${selectedClass.totalStudents}</div>
            </div>
            <div class="stat-card">
              <h3>อัตราผ่าน</h3>
              <div class="value">${passRate.toFixed(1)}%</div>
            </div>
            <div class="stat-card">
              <h3>คะแนนเฉลี่ย</h3>
              <div class="value">${averageScore.toFixed(1)}</div>
            </div>
          </div>
          
          <div class="students-section">
            <h2>ผลการเรียนรู้ของนักเรียน</h2>
            <table>
              <thead>
                <tr>
                  <th>อันดับ</th>
                  <th>ชื่อ</th>
                  <th>ความคืบหน้า</th>
                  <th>คอร์ส/เส้นทาง</th>
                  <th>คะแนน</th>
                </tr>
              </thead>
              <tbody>
                ${filteredStudentsForPDF.map((student, index) => `
                  <tr>
                    <td class="rank">${index + 1}</td>
                    <td>${student.name}</td>
                    <td>
                      <div class="progress-container">
                        <div class="progress-bar">
                          <div class="progress-fill" style="width: ${student.progress}%;"></div>
                        </div>
                        <span class="progress-text">${student.progress}%</span>
                      </div>
                    </td>
                    <td>${student.courseOrPath}</td>
                    <td>${student.totalScore}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
          
          <div class="footer">
            <p>รายงานนี้สร้างโดยระบบ E-Learning Platform</p>
            <p>© ${new Date().getFullYear()} โรงพยาบาลสินแพทย์</p>
          </div>
        </body>
        </html>
      `)
      iframeDoc.close()

      // Wait for fonts to load in iframe
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Use html2canvas to capture the iframe content
      const canvas = await html2canvas(iframeDoc.body, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      // Remove the iframe
      document.body.removeChild(iframe)

      // Create PDF from canvas
      const pdf = new jsPDF('p', 'pt', 'a4')
      const imgData = canvas.toDataURL('image/png')
      
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = pdf.internal.pageSize.getHeight()
      const imgWidth = canvas.width
      const imgHeight = canvas.height
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight)
      
      const imgX = (pdfWidth - imgWidth * ratio) / 2
      const imgY = 0
      
      pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio)
      
      // If content is too long, handle multiple pages
      if (imgHeight * ratio > pdfHeight) {
        const totalPages = Math.ceil((imgHeight * ratio) / pdfHeight)
        for (let i = 1; i < totalPages; i++) {
          pdf.addPage()
          const yOffset = -pdfHeight * i
          pdf.addImage(imgData, 'PNG', imgX, imgY + yOffset, imgWidth * ratio, imgHeight * ratio)
        }
      }

      // Download the PDF
      const fileName = `classroom-report-${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)
      
      console.log('PDF with Thai support generated successfully!')
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('เกิดข้อผิดพลาดในการสร้าง PDF')
    }
  }

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#008067] rounded-lg">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">รายงานห้องเรียน</h1>
                <p className="text-sm text-gray-600">ตรวจสอบผลการเรียนรู้และความคืบหน้าของห้องเรียน</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button 
                onClick={handleDownloadPDF}
                disabled={!selectedClass}
                className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                  !selectedClass
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-[#008067] text-white hover:bg-[#006b57]'
                }`}
              >
                <Download size={16} className="mr-2" />
                ดาวน์โหลด PDF
              </button>
              <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md">
                <Filter size={16} className="mr-2" />
                ตัวกรอง
              </button>
            </div>
          </div>

          {/* Combined Search & Select Classroom */}
          <div className="space-y-4">
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-10" size={20} />
                <input
                  type="text"
                  placeholder="ค้นหาและเลือกห้องเรียน..."
                  value={searchTerm}
                  onChange={e => handleSearchChange(e.target.value)}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent transition-all duration-200"
                />
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    setShowDropdown(!showDropdown)
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors z-10"
                  type="button"
                >
                  <ChevronDown size={20} />
                </button>
                
                {/* Dropdown for filtered classrooms */}
                {showDropdown && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
                    {(() => {
                      const classroomsToShow = searchTerm ? filteredClassrooms : mockClassroomList
                      
                      if (classroomsToShow.length === 0) {
                        return (
                          <div className="px-4 py-3 text-center text-gray-500">
                            {searchTerm ? 'ไม่พบห้องเรียนที่ค้นหา' : 'ไม่มีข้อมูลห้องเรียน'}
                          </div>
                        )
                      }
                      
                      return classroomsToShow.map(classroom => (
                        <button
                          key={classroom.id}
                          onClick={() => handleClassroomSelect(classroom)}
                          className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium text-gray-900">{classroom.name}</div>
                              <div className="text-sm text-gray-500">{classroom.instructor}</div>
                            </div>
                            <div className="text-sm text-gray-400">{classroom.id}</div>
                          </div>
                        </button>
                      ))
                    })()}
                  </div>
                )}
              </div>
            </div>
            
            {/* Search results info */}
            {searchTerm && !selectedClass && filteredClassrooms.length > 0 && (
              <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2">
                  <Search size={16} className="text-blue-500" />
                  <span>พบห้องเรียน {filteredClassrooms.length} ห้อง - คลิกเพื่อเลือก</span>
                </div>
              </div>
            )}
            
            {/* No results */}
            {searchTerm && filteredClassrooms.length === 0 && (
              <div className="text-sm text-gray-500 bg-red-50 p-3 rounded-lg border border-red-200">
                <div className="flex items-center space-x-2">
                  <Search size={16} className="text-red-500" />
                  <span>ไม่พบห้องเรียนที่ตรงกับ "{searchTerm}"</span>
                </div>
              </div>
            )}
          </div>

          {/* Classroom Info */}
          {selectedClass && (
            <div className="mt-6 bg-gradient-to-r from-[#008067] to-[#006b57] text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold flex items-center space-x-2">
                  <Users size={24} />
                  <span>{selectedClass.name}</span>
                </h2>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm">
                  {selectedClass.id}
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div>
                    <span className="opacity-80">ผู้สอน:</span>
                    <div className="font-medium">{selectedClass.instructor}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div>
                    <span className="opacity-80">วันเริ่ม:</span>
                    <div className="font-medium">{selectedClass.startDate}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div>
                    <span className="opacity-80">วันสิ้นสุด:</span>
                    <div className="font-medium">{selectedClass.endDate}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div>
                    <span className="opacity-80">นักเรียนทั้งหมด:</span>
                    <div className="font-medium">{selectedClass.totalStudents} คน</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!selectedClass && (
            <div className="mt-6 text-center py-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-200">
              <Users size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">เลือกห้องเรียนเพื่อดูรายงาน</h3>
              <p className="text-gray-600">ใช้ช่องค้นหาหรือเลือกจากรายการด้านบนเพื่อดูผลการเรียนรู้</p>
            </div>
          )}
        </div>

        {/* Statistics Cards */}
        {selectedClass && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">นักเรียนทั้งหมด</p>
                  <p className="text-2xl font-bold text-gray-900">{selectedClass.totalStudents}</p>
                </div>
                <Users className="w-8 h-8 text-blue-500" />
              </div>
            </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">อัตราผ่าน</p>
                <p className="text-2xl font-bold text-green-600">{passRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">คะแนนเฉลี่ย</p>
                <p className="text-2xl font-bold text-orange-600">{averageScore.toFixed(1)}</p>
              </div>
              <BookOpen className="w-8 h-8 text-orange-500" />
            </div>
          </div>
          </div>
        )}

        {/* Student Results Table */}
        {selectedClass && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">ผลการเรียนรู้ของนักเรียน</h2>
              
              {/* Filter Bar */}
              <div className="flex items-center space-x-4 mt-4">
                {filterOptions.map(opt => (
                  <button
                    key={opt.value}
                    className={`px-4 py-2 rounded-md border ${filterType === opt.value ? 'bg-[#008067] text-white border-[#008067]' : 'bg-white text-gray-700 border-gray-300'} font-medium transition-colors`}
                    onClick={() => setFilterType(opt.value)}
                  >
                    {opt.label}
                  </button>
                ))}
              </div>
              
              {/* Student Search */}
              <div className="relative mt-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="ค้นหานักเรียนในห้องเรียนนี้..."
                  value={studentSearchTerm}
                  onChange={(e) => setStudentSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent"
                />
              </div>
            </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">อันดับ</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ชื่อ</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ความคืบหน้า</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ชื่อคอร์ส/เส้นทางการเรียน</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">คะแนน</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedStudents.map((student, idx) => (
                  <tr key={student.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-[#008067]">{idx + 1}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleStudentClick(student)}
                        className="text-sm font-medium text-[#008067] hover:text-[#006b57] hover:underline cursor-pointer transition-colors"
                      >
                        {student.name}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="w-32">
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 rounded-full h-3 mr-2">
                            <div
                              className="bg-[#008067] h-3 rounded-full"
                              style={{ width: `${student.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-xs font-medium text-gray-700">{student.progress}%</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{student.courseOrPath}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{student.totalScore}</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
