"use client"

import AdminLayout from "@/components/admin/layout"
import { Users, BookOpen, FileText, TrendingUp, TrendingDown, Award, Clock, User<PERSON>heck, ChevronDown, LayoutDashboard } from "lucide-react"
import ActiveUsersChart from "@/components/admin/charts/ActiveUsersChart"
import StudentProgressChart from "@/components/admin/charts/StudentProgressChart"
import GrowthMetrics from "@/components/admin/charts/GrowthMetrics"
import CertificationChart from "@/components/admin/charts/CertificationChart"
import DropoffFunnel<PERSON>hart from "@/components/admin/charts/DropoffFunnelChart"
import CourseProgressChart from "@/components/admin/charts/CourseProgressChart"

export default function AdminDashboardPage() {
  const stats = [
    {
      title: "ผู้ใช้ทั้งหมด",
      value: "1,245",
      change: "+12.5%",
      trend: "up",
      icon: <Users size={24} className="text-blue-500" />
    },
    {
      title: "คอร์สทั้งหมด",
      value: "24",
      change: "+8.3%",
      trend: "up",
      icon: <BookOpen size={24} className="text-green-500" />
    },
    {
      title: "เส้นทางการเรียนรู้",
      value: "12",
      change: "+16.7%",
      trend: "up",
      icon: <FileText size={24} className="text-purple-500" />
    },
    {
      title: "ใบรับรองที่ออก",
      value: "892",
      change: "+22.1%",
      trend: "up",
      icon: <Award size={24} className="text-yellow-500" />
    },
    {
      title: "เวลาเรียนเฉลี่ย",
      value: "45 นาที",
      change: "-5.2%",
      trend: "down",
      icon: <Clock size={24} className="text-indigo-500" />
    },
    {
      title: "ผู้ใช้ที่ใช้งาน",
      value: "156",
      change: "+18.9%",
      trend: "up",
      icon: <UserCheck size={24} className="text-emerald-500" />
    },
  ]

  return (
    <AdminLayout>
      <div className="relative bg-gradient-to-r from-[#008067] via-[#2e907b] to-[#30d8b6] rounded-3xl p-6 sm:p-8 text-white overflow-hidden shadow-2xl mb-6 animate-gradient-x">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-transparent to-pink-500/20 backdrop-blur-sm"></div>
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-300/30 via-orange-400/20 to-red-400/10 rounded-full -translate-y-32 translate-x-32 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/20 via-cyan-300/15 to-green-300/10 rounded-full translate-y-24 -translate-x-24 animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-gradient-to-r from-pink-400/10 to-purple-400/10 rounded-full -translate-x-1/2 -translate-y-1/2 animate-spin-slow"></div>

        <div className="relative z-10">
          <div className="flex flex-col sm:flex-row md:flex-row md:items-center gap-3">
            <div className="p-3 bg-gradient-to-br from-white/30 via-white/20 to-white/10 rounded-2xl backdrop-blur-sm border border-white/20 shadow-lg">
              <LayoutDashboard className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-white drop-shadow-lg" strokeWidth={2.2} />
            </div>
            <div>
              <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-1 bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent drop-shadow-md">แดชบอร์ด</h1>
              <p className="text-sm sm:text-lg md:text-xl lg:text-xl opacity-95 font-medium text-white/95 drop-shadow-sm">
                ยินดีต้อนรับสู่ระบบจัดการ E-MED LEARNING
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-blue-50/30 to-transparent rounded-full -translate-y-12 translate-x-12"></div>
            <div className="relative z-10">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <p className="text-sm font-semibold text-gray-600 mb-2">{stat.title}</p>
                  <h3 className="text-3xl font-bold text-gray-800 mb-3">{stat.value}</h3>
                  <div className="flex items-center">
                    {stat.trend === "up" ? (
                      <div className="flex items-center px-2 py-1 bg-green-100 rounded-full">
                        <TrendingUp size={14} className="text-green-600 mr-1" />
                        <span className="text-xs font-semibold text-green-700">{stat.change}</span>
                      </div>
                    ) : (
                      <div className="flex items-center px-2 py-1 bg-red-100 rounded-full">
                        <TrendingDown size={14} className="text-red-600 mr-1" />
                        <span className="text-xs font-semibold text-red-700">{stat.change}</span>
                      </div>
                    )}
                    <span className="text-xs text-gray-500 ml-2">จากเดือนที่แล้ว</span>
                  </div>
                </div>
                <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-50 rounded-2xl shadow-inner group-hover:shadow-lg transition-shadow duration-300">
                  {stat.icon}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Active Users & Engagement Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-50/40 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">ผู้ใช้ที่ใช้งาน</h2>
              <div className="p-2 bg-blue-100 rounded-xl">
                <Users size={20} className="text-blue-600" />
              </div>
            </div>
            <ActiveUsersChart />
          </div>
        </div>

        <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-50/40 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">อัตราการเติบโต</h2>
              <div className="p-2 bg-green-100 rounded-xl">
                <TrendingUp size={20} className="text-green-600" />
              </div>
            </div>
            <GrowthMetrics />
          </div>
        </div>
      </div>

      {/* Student Progress Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-purple-50/40 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">การเดินทางของนักเรียน</h2>
              <div className="p-2 bg-purple-100 rounded-xl">
                <FileText size={20} className="text-purple-600" />
              </div>
            </div>
            <DropoffFunnelChart />
          </div>
        </div>

        <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50/40 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">ความคืบหน้าของคอร์ส</h2>
              <div className="p-2 bg-orange-100 rounded-xl">
                <BookOpen size={20} className="text-orange-600" />
              </div>
            </div>
            <CourseProgressChart />
          </div>
        </div>
      </div>

      {/* Course Selection Bar */}
      <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 mb-8 overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-indigo-50/30 to-transparent rounded-full -translate-y-20 translate-x-20"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 rounded-xl">
                <BookOpen size={20} className="text-indigo-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">เลือกคอร์สเรียนเพื่อดูรายละเอียด</h2>
            </div>
          </div>
          <div className="relative">
            <select className="w-full px-4 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-2 border-gray-200 rounded-xl text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none cursor-pointer font-medium shadow-inner hover:shadow-lg transition-all duration-200">
              <option value="">เลือกคอร์สเรียน...</option>
              <option value="pathology-basics">Pathology Basics - พื้นฐานพยาธิวิทยา</option>
              <option value="clinical-skills">Clinical Skills - ทักษะคลินิก</option>
              <option value="pneumonia-treatment">Pneumonia Treatment - การรักษาปอดบวม</option>
              <option value="cardiac-care">Cardiac Care - การดูแลหัวใจ</option>
              <option value="emergency-medicine">Emergency Medicine - เวชศาสตร์ฉุกเฉิน</option>
              <option value="pediatric-care">Pediatric Care - การดูแลเด็ก</option>
              <option value="surgical-techniques">Surgical Techniques - เทคนิคการผ่าตัด</option>
              <option value="diagnostic-imaging">Diagnostic Imaging - การถ่ายภาพวินิจฉัย</option>
              <option value="pharmacology">Pharmacology - เภสัชวิทยา</option>
              <option value="anatomy-physiology">Anatomy & Physiology - กายวิภาคและสรีรวิทยา</option>
              <option value="medical-ethics">Medical Ethics - จริยธรรมทางการแพทย์</option>
              <option value="infection-control">Infection Control - การควบคุมการติดเชื้อ</option>
            </select>
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-sm">
              <ChevronDown className="text-gray-500" size={16} />
            </div>
          </div>
          <p className="text-sm text-gray-600 mt-3 flex items-center gap-2">
            <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
            เลือกคอร์สเรียนเพื่อดูสถิติและความคืบหน้าของนักเรียนในคอร์สนั้น ๆ
          </p>
        </div>
      </div>

      {/* Certification Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-teal-50/40 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">ความคืบหน้าของนักเรียน</h2>
              <div className="p-2 bg-teal-100 rounded-xl">
                <UserCheck size={20} className="text-teal-600" />
              </div>
            </div>
            <StudentProgressChart />
          </div>
        </div>

        <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-yellow-50/40 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">การออกใบรับรอง</h2>
              <div className="p-2 bg-yellow-100 rounded-xl">
                <Award size={20} className="text-yellow-600" />
              </div>
            </div>
            <CertificationChart />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="relative bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-pink-50/30 to-transparent rounded-full -translate-y-20 translate-x-20"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-pink-100 rounded-xl">
              <Clock size={20} className="text-pink-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">กิจกรรมล่าสุด</h2>
          </div>
          <div className="space-y-4">
            {[
              { action: "ผู้ใช้ใหม่ได้ลงทะเบียนเข้าสู่ระบบ", time: "2 ชั่วโมงที่แล้ว", icon: Users, color: "blue" as const },
              { action: "นักเรียนได้รับใบรับรอง Pathology", time: "3 ชั่วโมงที่แล้ว", icon: Award, color: "yellow" as const },
              { action: "คอร์สใหม่ 'Clinical Skills' ถูกเพิ่ม", time: "5 ชั่วโมงที่แล้ว", icon: BookOpen, color: "green" as const },
              { action: "นักเรียน 15 คนเสร็จสิ้นคอร์ส Pneumonia Treatment", time: "1 วันที่แล้ว", icon: FileText, color: "purple" as const },
            ].map((activity, index) => {
              const IconComponent = activity.icon
              const colorClasses = {
                blue: "bg-blue-100 text-blue-600",
                yellow: "bg-yellow-100 text-yellow-600",
                green: "bg-green-100 text-green-600",
                purple: "bg-purple-100 text-purple-600"
              }
              return (
                <div key={index} className="flex items-start pb-4 border-b border-gray-100 last:border-0 last:pb-0 group hover:bg-gray-50/50 p-3 rounded-xl transition-all duration-200">
                  <div className={`w-12 h-12 rounded-2xl ${colorClasses[activity.color]} flex items-center justify-center mr-4 flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow duration-200`}>
                    <IconComponent size={20} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-800 mb-1">{activity.action}</p>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
