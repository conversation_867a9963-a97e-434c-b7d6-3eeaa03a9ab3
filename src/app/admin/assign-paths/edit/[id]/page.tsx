"use client"

import { use } from "react"
import AssignEditor from "@/components/admin/assign-path-editor"
import AdminLayout from "@/components/admin/layout"
import { getUserBySlug } from "@/hook/userService"
import type { UserSlugResponse } from "@/types/api/users"


interface EditAssignPageProps {
  params: Promise<{ id: string }>
}

export default async function EditAssignPage({ params }: EditAssignPageProps) {
  const { id: studentId } = await params

  // Fetch real user data from API
  const student: UserSlugResponse | null = await getUserBySlug(studentId)

  if (!student) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">ไม่พบข้อมูลนักเรียน</h1>
          <p className="text-gray-600">ไม่สามารถหาข้อมูลนักเรียนที่ต้องการแก้ไขได้</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
        </div>

        <AssignEditor
          mode="edit"
          preSelectedStudentId={student.slug}
        />
      </div>
    </AdminLayout>
  )
}
