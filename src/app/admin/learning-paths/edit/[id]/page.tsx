import AdminLayout from "@/components/admin/layout"
import LearningPathEditor from "@/components/admin/learning-paths-editor"
import { learningPathsData } from "@/data/learningPaths"
import { notFound } from "next/navigation"

interface EditLearningPathPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function EditLearningPathPage({ params }: EditLearningPathPageProps) {
  const { id } = await params
  // No need to fetch data here since LearningPathEditor handles API calls internally
  
  return (
    <AdminLayout>
      <LearningPathEditor mode="edit" pathId={id} />
    </AdminLayout>
  )
}
