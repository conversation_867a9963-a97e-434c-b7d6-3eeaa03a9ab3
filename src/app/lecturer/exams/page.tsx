import LecturerLayout from '@/components/lecturer/layout'
import ExamsTable from '@/components/lecturer/exams-table'
import { FileText } from 'lucide-react'

export default function ExamsListPage() {
  return (
    <LecturerLayout>
      <div className="flex flex-col gap-4">
        <div className="relative bg-gradient-to-r from-[#293D97] via-[#3949ab] to-[#5c6bc0] rounded-3xl p-6 sm:p-8 text-white overflow-hidden shadow-2xl mt-5">
          <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row md:flex-row md:items-center gap-3">
              <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                <FileText className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10" strokeWidth={2.2} />
              </div>
              <div>
                <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-1">จัดการข้อสอบท้ายบท</h1>
                <p className="text-sm sm:text-lg md:text-xl lg:text-xl opacity-90 font-medium">
                  จัดการข้อสอบท้ายบททั้งหมดในระบบ
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="rounded-lg border mt-3 bg-white p-4 shadow-sm">
          <ExamsTable />
        </div>
      </div>
    </LecturerLayout>
  )
}
