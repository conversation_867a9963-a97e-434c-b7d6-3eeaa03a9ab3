import LecturerLayout from '@/components/lecturer/layout'
import AssignEditor from '@/components/lecturer/assign-path-editor'

interface EditAssignPathPageProps {
  params: Promise<{ id: string }>
}

export default async function EditAssignPathPage({ params }: EditAssignPathPageProps) {
  const { id } = await params
  
  return (
    <LecturerLayout>
      <div className="mx-auto py-4">
        <AssignEditor mode="edit" preSelectedStudentId={id} />
      </div>
    </LecturerLayout>
  )
}
