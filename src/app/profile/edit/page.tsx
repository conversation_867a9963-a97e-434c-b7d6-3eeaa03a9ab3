"use client"

import type React from "react"
import type { MedicalUser, PasswordChangeData } from "@/types/users"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ArrowLeft, Camera, Lock, Edit, Eye, EyeOff } from "lucide-react"
import { getMedicalUsersData } from "@/data/allUsers"
import Navbar from "@/components/headers"

// ประเภทข้อมูลสำหรับฟอร์มแก้ไขโปรไฟล์
interface ProfileFormData {
  uid: string
  email: string
  firstname: string
  lastname: string
  position: string
  role: string
}

// ประเภทข้อมูลสำหรับข้อผิดพลาดของรหัสผ่าน
interface PasswordErrors {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export default function EditProfilePage() {
  const router = useRouter()
  const [user, setUser] = useState<MedicalUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)

  // เพิ่ม state สำหรับรหัสผ่าน
  const [passwordData, setPasswordData] = useState<PasswordChangeData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })
  // เพิ่ม state สำหรับการซ่อน/แสดงรหัสผ่าน
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  // เพิ่ม state สำหรับการลาก drop รูปภาพ
  const [isDraggingOver, setIsDraggingOver] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const [profileImage, setProfileImage] = useState<string | null>(null)

  const [formData, setFormData] = useState<ProfileFormData>({
    uid: "",
    email: "",
    firstname: "",
    lastname: "",
    position: "",
    role: "",
  })

  // เพิ่ม state สำหรับเก็บข้อความ error ของแต่ละฟิลด์
  const [passwordErrors, setPasswordErrors] = useState<PasswordErrors>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  // เพิ่ม state สำหรับ popup แจ้งเตือนข้อผิดพลาด
  const [showErrorModal, setShowErrorModal] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")

  useEffect(() => {
    // ตรวจสอบว่ามีการ login หรือไม่
    const userId = localStorage.getItem("userId")
    if (!userId) {
      router.push("/login")
      return
    }

    // ดึงข้อมูลผู้ใช้
    const userData = getMedicalUsersData.find((user) => user.id === userId)
    if (userData) {
      // แปลง status จาก string เป็น boolean
      const mappedUser: MedicalUser = {
        ...userData,
        status: userData.status === "active",
      }
      setUser(mappedUser)
      setProfileImage(userData.profileImage || null)

      // ตั้งค่าข้อมูลฟอร์ม
      setFormData({
        uid: userData.id,
        email: userData.email,
        firstname: userData.firstname,
        lastname: userData.lastname,
        position: userData.position,
        role: userData.role,
      })
    }

    setIsLoading(false)
  }, [router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        setProfileImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const triggerFileInput = () => {
    if (isEditing) {
      fileInputRef.current?.click()
    }
  }

  // เพิ่มฟังก์ชันสำหรับการ drag and drop รูปภาพ
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    if (!isEditing) return
    e.preventDefault()
    setIsDraggingOver(true)
  }

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    if (!isEditing) return
    e.preventDefault()
    setIsDraggingOver(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    if (!isEditing) return
    e.preventDefault()
    setIsDraggingOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]

      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!file.type.match("image.*")) {
        alert("กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น")
        return
      }

      const reader = new FileReader()
      reader.onload = (event) => {
        setProfileImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // เพิ่มฟังก์ชันตรวจสอบรหัสผ่านแบบ real-time หลังจากฟังก์ชัน handleDrop
  const validatePassword = (field: keyof PasswordErrors, value: string): boolean => {
    // รีเซ็ตเฉพาะ error ของฟิลด์ที่กำลังตรวจสอบ
    setPasswordErrors((prev) => ({
      ...prev,
      [field]: "",
    }))

    if (!user) return false

    // ตรวจสอบรหัสผ่านปัจจุบัน
    if (field === "currentPassword" && value) {
      if (value !== user.password) {
        setPasswordErrors((prev) => ({
          ...prev,
          currentPassword: "รหัสผ่านปัจจุบันไม่ถูกต้อง",
        }))
        return false
      }
    }

    // ตรวจสอบรหัสผ่านใหม่
    if (field === "newPassword" && value) {
      if (value.length < 8) {
        setPasswordErrors((prev) => ({
          ...prev,
          newPassword: "รหัสผ่านใหม่ต้องมีความยาวอย่างน้อย 8 ตัวอักษร",
        }))
        return false
      }

      // ตรวจสอบว่าตรงกับยืนยันรหัสผ่านหรือไม่ (ถ้ามีการกรอกยืนยันรหัสผ่านแล้ว)
      if (passwordData.confirmPassword && value !== passwordData.confirmPassword) {
        setPasswordErrors((prev) => ({
          ...prev,
          confirmPassword: "รหัสผ่านใหม่และยืนยันรหัสผ่านใหม่ไม่ตรงกัน",
        }))
        return false
      }
    }

    // ตรวจสอบยืนยันรหัสผ่าน
    if (field === "confirmPassword" && value) {
      if (value !== passwordData.newPassword) {
        setPasswordErrors((prev) => ({
          ...prev,
          confirmPassword: "รหัสผ่านใหม่และยืนยันรหัสผ่านใหม่ไม่ตรงกัน",
        }))
        return false
      }
    }

    return true
  }

  // แก้ไขฟังก์ชัน handleSubmit
  const handleSubmit = () => {
    if (!user) return

    // รีเซ็ต error messages
    setPasswordErrors({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    })

    let hasError = false
    let errorMsg = ""

    // ตรวจสอบรหัสผ่านถ้ามีการเปลี่ยนแปลง
    if (passwordData.currentPassword || passwordData.newPassword || passwordData.confirmPassword) {
      // ตรวจสอบว่ารหัสผ่านปัจจุบันถูกต้อง
      if (passwordData.currentPassword !== user.password) {
        setPasswordErrors((prev) => ({ ...prev, currentPassword: "รหัสผ่านปัจจุบันไม่ถูกต้อง" }))
        errorMsg = "รหัสผ่านปัจจุบันไม่ถูกต้อง"
        hasError = true
      }

      // ตรวจสอบว่ารหัสผ่านใหม่ไม่ว่างเปล่า
      if (!passwordData.newPassword) {
        setPasswordErrors((prev) => ({ ...prev, newPassword: "กรุณากรอกรหัสผ่านใหม่" }))
        errorMsg = errorMsg || "กรุณากรอกรหัสผ่านใหม่"
        hasError = true
      }

      // ตรวจสอบความยาวรหัสผ่านขั้นต่ำ
      if (passwordData.newPassword && passwordData.newPassword.length < 8) {
        setPasswordErrors((prev) => ({ ...prev, newPassword: "รหัสผ่านใหม่ต้องมีความยาวอย่างน้อย 8 ตัวอักษร" }))
        errorMsg = errorMsg || "รหัสผ่านใหม่ต้องมีความยาวอย่างน้อย 8 ตัวอักษร"
        hasError = true
      }

      // ตรวจสอบว่ารหัสผ่านใหม่และยืนยันรหัสผ่านใหม่ตรงกัน
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        setPasswordErrors((prev) => ({ ...prev, confirmPassword: "รหัสผ่านใหม่และยืนยันรหัสผ่านใหม่ไม่ตรงกัน" }))
        errorMsg = errorMsg || "รหัสผ่านใหม่และยืนยันรหัสผ่านใหม่ไม่ตรงกัน"
        hasError = true
      }
    }

    if (hasError) {
      // แสดง popup แจ้งเตือนเมื่อมีข้อผิดพลาด
      setErrorMessage(errorMsg)
      setShowErrorModal(true)
      return
    }

    // ในสถานการณ์จริงจะต้องส่งข้อมูลไปยัง API
    console.log("Form submitted:", formData)

    // ถ้ามีการเปลี่ยนรหัสผ่านและผ่านการตรวจสอบแล้ว
    if (passwordData.currentPassword && passwordData.newPassword) {
      console.log("Password changed:", passwordData)
    }

    alert("บันทึกข้อมูลเรียบร้อยแล้ว")
    setIsEditing(false)
  }

  // แก้ไขการจัดการ onChange ของรหัสผ่าน
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // ตรวจสอบรหัสผ่านแบบ real-time
    validatePassword(name as keyof PasswordErrors, value)
  }

  // แปลงบทบาทเป็นภาษาไทย
  const getRoleText = (role: string): string => {
    switch (role) {
      case "student":
        return "นักศึกษา"
      case "lecturer":
        return "อาจารย์"
      case "admin":
        return "ผู้ดูแลระบบ"
      default:
        return role
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">ไม่พบข้อมูลผู้ใช้</h1>
          <p className="text-gray-600 mb-6">กรุณาเข้าสู่ระบบเพื่อดูโปรไฟล์</p>
          <button
            onClick={() => router.push("/login")}
            className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
          >
            เข้าสู่ระบบ
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f9f4f8] pt-16 pb-10">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-2xl shadow-sm p-6 md:p-8">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/profile/dashboard')}
                  className="mr-4 p-2 rounded-full hover:bg-gray-100"
                >
                  <ArrowLeft size={20} />
                </button>
                <h1 className="text-2xl font-bold">โปรไฟล์ของฉัน</h1>
              </div>
              <div className="flex items-center">
                <Link href="/profile/my-courses" className="text-[#008268] hover:underline mr-4">
                  คอร์สของฉัน
                </Link>
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full overflow-hidden mr-2">
                    {profileImage ? (
                      <Image
                        src={profileImage || "/placeholder.svg"}
                        alt={user.firstname}
                        width={32}
                        height={32}
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-[#004c41] to-[#2fbcc1] flex items-center justify-center text-white">
                        <span className="text-xs font-medium">
                          {user.firstname.charAt(0)}
                          {user.lastname.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                  <span className="text-sm font-medium">
                    {user.firstname} {user.lastname}
                  </span>
                </div>
              </div>
            </div>

            {/* Role Badge */}
            <div className="mb-6">
              <span className="inline-block px-3 py-1 bg-[#fff4e6] text-[#ff9500] text-sm font-medium rounded-full">
                {getRoleText(user.role)}
              </span>
            </div>

            {/* Profile Content */}
            <div className="bg-white rounded-lg">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium">ข้อมูลส่วนตัว</h3>
                {!isEditing && (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
                  >
                    <Edit size={16} />
                    แก้ไขข้อมูล
                  </button>
                )}
              </div>

              {/* Profile Image and Info */}
              <div className="flex flex-col md:flex-row gap-8 mb-8">
                {/* Profile Image */}
                <div className="w-full md:w-auto flex flex-col items-center">
                  <div className="relative mb-2">
                    <div
                      className={`w-32 h-32 rounded-full overflow-hidden border-4 ${isDraggingOver ? "border-[#008268] bg-[#E6F2F0]" : "border-gray-100"
                        } ${isEditing ? "cursor-pointer" : ""}`}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                      onClick={triggerFileInput}
                    >
                      {profileImage ? (
                        <Image
                          src={profileImage || "/placeholder.svg"}
                          alt={user.firstname}
                          width={128}
                          height={128}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-[#004c41] to-[#2fbcc1] flex items-center justify-center text-white">
                          <span className="text-4xl font-medium">
                            {user.firstname.charAt(0)}
                            {user.lastname.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleImageUpload}
                      className="hidden"
                      accept="image/*"
                      disabled={!isEditing}
                    />
                    {isEditing && (
                      <button
                        type="button"
                        onClick={triggerFileInput}
                        className="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-md border border-gray-200"
                      >
                        <Camera size={20} className="text-gray-700" />
                      </button>
                    )}
                  </div>
                  {isEditing && <p className="text-sm text-gray-500 text-center">คลิกเพื่อเปลี่ยนรูปโปรไฟล์</p>}
                </div>

                {/* User Info */}
                <div className="flex-1 w-full">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {isEditing ? (
                      <>
                        <div>
                          <label htmlFor="firstname" className="block text-sm text-gray-500 mb-1">
                            ชื่อ
                          </label>
                          <input
                            type="text"
                            id="firstname"
                            name="firstname"
                            value={formData.firstname}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]"
                          />
                        </div>
                        <div>
                          <label htmlFor="lastname" className="block text-sm text-gray-500 mb-1">
                            นามสกุล
                          </label>
                          <input
                            type="text"
                            id="lastname"
                            name="lastname"
                            value={formData.lastname}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]"
                          />
                        </div>
                      </>
                    ) : (
                      <div>
                        <p className="text-sm text-gray-500 mb-1">ชื่อ-สกุล</p>
                        <p className="font-medium">
                          {formData.firstname} {formData.lastname}
                        </p>
                      </div>
                    )}
                    <div className="md:col-span-2">
                      <p className="text-sm text-gray-500 mb-1">อีเมล</p>
                      {isEditing ? (
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]"
                        />
                      ) : (
                        <p className="font-medium">{formData.email}</p>
                      )}
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">ตำแหน่ง</p>
                      <div className="flex items-center">
                        <p className="font-medium">{formData.position}</p>
                        {isEditing && <Lock size={14} className="ml-2 text-gray-400" />}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">บทบาท</p>
                      <div className="flex items-center">
                        <p className="font-medium">{getRoleText(formData.role)}</p>
                        {isEditing && <Lock size={14} className="ml-2 text-gray-400" />}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Password Change Section */}
              {isEditing && (
                <div className="border-t border-gray-200 pt-6 mt-6">
                  <h3 className="text-lg font-medium mb-4">เปลี่ยนรหัสผ่าน</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        รหัสผ่านปัจจุบัน
                        {passwordErrors.currentPassword && (
                          <span className="text-red-500 ml-2 text-xs">{passwordErrors.currentPassword}</span>
                        )}
                      </label>
                      <div className="relative">
                        <input
                          type={showCurrentPassword ? "text" : "password"}
                          id="currentPassword"
                          name="currentPassword"
                          value={passwordData.currentPassword}
                          onChange={handlePasswordChange}
                          onBlur={(e) => validatePassword("currentPassword", e.target.value)}
                          className={`w-full px-3 py-2 bg-white border ${passwordErrors.currentPassword ? "border-red-300" : "border-gray-300"
                            } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]`}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        >
                          {showCurrentPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        รหัสผ่านใหม่
                        {passwordErrors.newPassword && (
                          <span className="text-red-500 ml-2 text-xs">{passwordErrors.newPassword}</span>
                        )}
                      </label>
                      <div className="relative">
                        <input
                          type={showNewPassword ? "text" : "password"}
                          id="newPassword"
                          name="newPassword"
                          value={passwordData.newPassword}
                          onChange={handlePasswordChange}
                          onBlur={(e) => validatePassword("newPassword", e.target.value)}
                          className={`w-full px-3 py-2 bg-white border ${passwordErrors.newPassword ? "border-red-300" : "border-gray-300"
                            } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]`}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        ยืนยันรหัสผ่านใหม่
                        {passwordErrors.confirmPassword && (
                          <span className="text-red-500 ml-2 text-xs">{passwordErrors.confirmPassword}</span>
                        )}
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          id="confirmPassword"
                          name="confirmPassword"
                          value={passwordData.confirmPassword}
                          onChange={handlePasswordChange}
                          onBlur={(e) => validatePassword("confirmPassword", e.target.value)}
                          className={`w-full px-3 py-2 bg-white border ${passwordErrors.confirmPassword ? "border-red-300" : "border-gray-300"
                            } rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]`}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {isEditing && (
                <div className="flex justify-end gap-4 mt-8">
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="px-6 py-3 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 font-medium"
                  >
                    ยกเลิก
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowConfirmModal(true)}
                    className="px-6 py-3 bg-[#008268] text-white rounded-md hover:bg-[#006e58] font-medium"
                  >
                    บันทึกการเปลี่ยนแปลง
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* เพิ่ม Modal ยืนยันการบันทึก */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium mb-4">ยืนยันการบันทึกข้อมูล</h3>
            <p className="text-gray-600 mb-6">คุณต้องการบันทึกการเปลี่ยนแปลงข้อมูลบัญชีใช่หรือไม่?</p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              >
                ยกเลิก
              </button>
              <button
                onClick={() => {
                  handleSubmit()
                  setShowConfirmModal(false)
                }}
                className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
              >
                ยืนยัน
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal แจ้งเตือนข้อผิดพลาด */}
      {showErrorModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium mb-4 text-red-600">พบข้อผิดพลาด</h3>
            <p className="text-gray-600 mb-6">{errorMessage}</p>

            <div className="flex justify-end">
              <button
                onClick={() => setShowErrorModal(false)}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                ตกลง
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
