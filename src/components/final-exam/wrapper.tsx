"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { finalExamService, FinalExam as FinalExamType, ExamCheckResult } from "@/hook/finalExamService"
import FinalExamComponent from "@/components/learming/final-exam"
import { Clock, AlertCircle, ArrowLeft } from "lucide-react"

interface FinalExamWrapperProps {
  coursesID: string
}

export default function FinalExamWrapper({ coursesID }: FinalExamWrapperProps) {
  const router = useRouter()
  const [finalExamData, setFinalExamData] = useState<FinalExamType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Test function for the new submit API
  const testSubmitAPI = async (examSlug: string, testAnswers: Record<string, string>) => {
    try {
      console.log("🧪 Testing Submit API:", { examSlug, testAnswers })
      const result = await finalExamService.submitFinalExamAnswers(examSlug, testAnswers)
      console.log("✅ Submit API Test Success:", result)
      
      // Make result globally accessible for testing
      if (typeof window !== "undefined") {
        (window as any).lastSubmitResult = result
        console.log("🔧 Submit result accessible via window.lastSubmitResult")
      }
      
      return result
    } catch (error) {
      console.error("❌ Submit API Test Failed:", error)
      throw error
    }
  }

  // Make test function globally accessible
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).testSubmitAPI = testSubmitAPI
    }
  }, [])

  // Load final exam data from API
  useEffect(() => {
    const loadFinalExam = async () => {
      try {
        setLoading(true)
        console.log("🔍 Loading final exam for course:", coursesID)
        
        const examList = await finalExamService.getFinalExamsByCourse(coursesID)
        
        if (examList && examList.length > 0) {
          const exam = examList[0]
          // Normalize slug property for downstream code
          if (!exam.slug && exam.examSlug) {
            exam.slug = exam.examSlug
          }
          setFinalExamData(exam)
          console.log("✅ Final exam loaded successfully:", exam)
          
          // Make exam data globally accessible for testing
          if (typeof window !== "undefined") {
            (window as any).finalExamData = exam
            console.log("🔧 Final exam data is now accessible via window.finalExamData")
          }
        } else {
          setError("No final exam found for this course")
          console.log("❌ No final exam found for course:", coursesID)
        }
      } catch (err) {
        console.error("❌ Error loading final exam:", err)
        setError("Failed to load final exam. Please try again later.")
      } finally {
        setLoading(false)
      }
    }

    if (coursesID) {
      loadFinalExam()
    }
  }, [coursesID])

  // Transform API data to match the existing component's expected format
  const transformExamData = (apiExam: FinalExamType) => {
    return {
      id: apiExam.examSlug || apiExam.slug || apiExam.id || '', // Use examSlug as primary ID
      slug: apiExam.examSlug || apiExam.slug || apiExam.id || '', // Ensure slug is always present
      name: apiExam.name,
      description: apiExam.description,
      questions: apiExam.questions?.map(q => ({
        id: q.slug || q.id || '', // Use slug as primary ID for questions
        title: q.title,
        type: q.type,
        content: q.content,
        imageUrl: q.imageUrl,
        choices: q.choices?.map(c => ({
          id: c.slug || c.id || '', // Use slug as primary ID for choices
          content: c.content,
          type: c.type,
          isCorrect: c.isCorrect || false, // Default to false since we don't get this from GET
          imageUrl: c.imageUrl
        })) || []
      })) || [],
      passingScore: apiExam.passingScore,
      timeLimit: apiExam.timeLimit
    }
  }

  // Handle exam completion
  const handleExamComplete = (passed: boolean, score: number) => {
    console.log("🎯 Exam completed:", { passed, score, coursesID })
    
    // Here you could save the result to the backend
    // await examResultService.saveResult({ coursesID, passed, score })
    
    // The existing component already handles the UI for results
  }

  // API submit function to pass to the exam component
  const handleSubmitAnswers = async (answers: Record<string, string[]>) => {
    console.log("🌟 === WRAPPER SUBMIT STARTED ===")
    if (!finalExamData?.slug) {
      console.log("❌ No exam slug available")
      throw new Error("No exam slug available")
    }
    try {
      const result = await finalExamService.submitFinalExamAnswers(finalExamData.slug, answers)
      return result
    } catch (error) {
      throw error
    }
  }

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Final Exam</h2>
          <p className="text-gray-600">Please wait while we prepare your exam...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Exam</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex justify-center space-x-4">
            <button 
              onClick={() => router.back()}
              className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </button>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  // No exam found
  if (!finalExamData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <AlertCircle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Final Exam Available</h2>
          <p className="text-gray-600 mb-6">This course doesn't have a final exam yet.</p>
          <button 
            onClick={() => router.back()}
            className="flex items-center mx-auto px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Course
          </button>
        </div>
      </div>
    )
  }

  // Render the existing beautiful final exam component
  return (
    <div>
      <FinalExamComponent 
        examData={transformExamData(finalExamData)}
        onComplete={handleExamComplete}
        onSubmitAnswers={handleSubmitAnswers}
      />
      
      {/* Debug info for development - Always show in dev or when debug flag is set */}
      {/* {(process.env.NODE_ENV === 'development' || typeof window !== 'undefined' && (window as any).showDebug) && (
        // <div className="fixed top-4 right-4 z-50 max-w-sm bg-gray-900 text-white rounded-lg shadow-xl overflow-hidden text-xs">
        //   <div className="bg-green-600 px-3 py-2 font-medium">
        //     ✅ Final Exam API Ready (Security Mode)
        //   </div>
        //   <div className="p-3">
        //     <div className="mb-2">
        //       <div className="text-green-400 font-medium">📋 Exam Info:</div>
        //       <div className="text-gray-300">
        //         Name: {finalExamData.name}<br/>
        //         Slug: {finalExamData.slug}<br/>
        //         Questions: {finalExamData.questions?.length || 0}<br/>
        //         Time: {finalExamData.timeLimit} min<br/>
        //         Pass: {finalExamData.passingScore}%
        //       </div>
        //     </div>
            
        //     <div className="mb-2">
        //       <div className="text-blue-400 font-medium">� Security Features:</div>
        //       <div className="text-gray-300 text-xs">
        //         • GET: No correct answers<br/>
        //         • POST: Real-time scoring<br/>
        //         • Slug-based IDs<br/>
        //         • Server-side validation
        //       </div>
        //     </div>
            
        //     <div className="mb-2">
        //       <div className="text-yellow-400 font-medium">🧪 Test Submit API:</div>
        //       <div className="text-gray-300 font-mono text-xs bg-gray-800 p-2 rounded">
        //         window.testSubmitAPI("{finalExamData.slug}", {"{"}q1: "choice1"{"}"})
        //       </div>
        //     </div>
            
        //     <div className="text-purple-400 font-medium">📡 Available:</div>
        //     <div className="text-gray-300 text-xs">
        //       • window.finalExamData<br/>
        //       • window.testSubmitAPI()<br/>
        //       • window.lastSubmitResult
        //     </div>
        //   </div>
        // </div>
      )} */}
    </div>
  )
}
