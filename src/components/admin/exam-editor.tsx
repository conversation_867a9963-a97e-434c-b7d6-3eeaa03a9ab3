"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import {
  Upload,
  ChevronDown,
  Plus,
  Trash2,
  Save,
  AlertTriangle,
  X,
  GripVertical,
  Check,
  PlusCircle,
  ChevronLeft,
} from "lucide-react"
import { getFinalExamById, type FinalExamData, type Question, type Choice } from "@/data/finalExamQuestions"
import { getCoursesData } from "@/data/allCourses"
import { examService, type ExamRequest, type Exam } from "@/hook/examService"
import { courseService } from "@/hook/courseService"
import { showSuccessAlert, showErrorAlert, showConfirmDialog } from "@/lib/sweetAlert"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
  DragOverlay,
} from "@dnd-kit/core"
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

// ฟังก์ชันสร้าง ID ที่ไม่ซ้ำกัน
function generateId(): string {
  return (
    Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + Date.now().toString(36)
  )
}

// ฟังก์ชันแปลงข้อมูลจาก backend เป็น frontend format
function transformExamFromAPI(apiExam: Exam): FinalExamData {
  return {
    id: apiExam.id?.toString(),
    name: apiExam.exam_name,
    description: apiExam.exam_description,
    courseId: apiExam.course_slug,
    questions: apiExam.questions?.map((q) => ({
      id: q.id?.toString() || generateId(),
      title: q.question,
      imageUrl: q.question_image || "",
      type: q.question_type === 1 ? "text" : q.question_type === 2 ? "image" : "text_and_image",
      content: q.detail || "",
      choices: q.choices?.map((c) => ({
        id: c.id?.toString() || generateId(),
        content: c.choice,
        imageUrl: c.choice_image || "",
        type: c.choice_type === 1 ? "text" : "image",
        isCorrect: c.is_correct,
      })) || [],
    })) || [],
    passingScore: apiExam.passing_score,
    timeLimit: apiExam.exam_time,
  }
}

// ฟังก์ชันแปลงข้อมูลจาก frontend เป็น backend format
function transformExamToAPI(examData: FinalExamData, courseSlug: string): ExamRequest {
  return {
    exam_name: examData.name,
    exam_description: examData.description || "",
    exam_time: examData.timeLimit,
    exam_status: true,
    passing_score: examData.passingScore,
    course_slug: courseSlug,
    questions: examData.questions.map((q, index) => ({
      question: q.title,
      question_image: q.imageUrl || "",
      detail: q.content,
      time_insert: index + 1,
      question_type: q.type === "text" ? 1 : q.type === "image" ? 2 : 3,
      choices: q.choices.map((c) => ({
        choice: c.content,
        choice_image: c.imageUrl || "",
        is_correct: c.isCorrect,
        choice_type: c.type === "text" ? 1 : 2,
      })),
    })),
  }
}

// ฟังก์ชันแปลงข้อมูลคอร์สจาก API เป็น frontend format
function transformCourseFromAPI(apiCourse: any): any {
  return {
    name: apiCourse.course_name,
    slug: apiCourse.slug
  }
}

// SortableQuestion component for drag and drop
function SortableQuestion({
  question,
  index,
  isSelected,
  onSelect,
}: {
  question: Question
  index: number
  isSelected: boolean
  onSelect: () => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ 
    id: question.id
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`p-3 rounded-md mb-2 ${
        isSelected ? "bg-[#E6F2F0] border border-[#008268]" : "bg-white border border-gray-200"
      } ${isDragging ? "shadow-lg border-[#008268]" : ""} hover:shadow-sm transition-shadow`}
    >
      <div className="flex items-center">
        <div
          {...attributes}
          {...listeners}
          className="mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100"
          title="ลากเพื่อเรียงลำดับ"
        >
          <GripVertical size={16} />
        </div>
        <span className="text-sm font-medium mr-2">{index + 1}.</span>
        <div 
          className="flex-1 text-sm truncate cursor-pointer" 
          onClick={onSelect}
          onMouseDown={(e) => {
            // Prevent drag from starting when clicking on the text area
            e.stopPropagation();
          }}
        >
          {question.title ? question.title : `คำถามที่ ${index + 1}`}
        </div>
      </div>
    </div>
  )
}

interface ExamEditorProps {
  examId?: string
}

export default function ExamEditor({ examId }: ExamEditorProps) {
  const router = useRouter()
  const [courses, setCourses] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<"exam" | "questions">("exam")
  const [selectedQuestion, setSelectedQuestion] = useState<number>(0)
  const questionImageRef = useRef<HTMLInputElement>(null)
  const choiceImageRefs = useRef<(HTMLInputElement | null)[]>([])
  const questionDropzoneRef = useRef<HTMLDivElement>(null)
  const choiceDropzoneRefs = useRef<(HTMLDivElement | null)[]>([])
  const [isDraggingOver, setIsDraggingOver] = useState<number | null>(null)
  const [isQuestionDraggingOver, setIsQuestionDraggingOver] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [questionToDelete, setQuestionToDelete] = useState<number | null>(null)
  const [activeId, setActiveId] = useState<string | null>(null)

  // สถานะสำหรับข้อมูลข้อสอบ
  const [examData, setExamData] = useState<FinalExamData>({
    id: generateId(),
    name: "",
    description: "",
    courseId: "",
    questions: [],
    passingScore: 70,
    timeLimit: 30,
  })

  // โหลดข้อมูลข้อสอบและคอร์สเมื่อมี examId
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // โหลดข้อมูลคอร์สจาก API
        try {
          const coursesData = await courseService.getCourses()
          // แปลงข้อมูลคอร์สให้เป็นรูปแบบที่ frontend ต้องการ
          const transformedCourses = coursesData.map(transformCourseFromAPI)
          setCourses(transformedCourses)
        } catch (courseErr: any) {
          console.error('Failed to load courses from API:', courseErr)
          setCourses([])
        }

        // โหลดข้อมูลข้อสอบถ้ามี examId
        if (examId) {
          try {
            // โหลดข้อมูลจาก API
            const apiExam = await examService.getExamById(examId)
            const transformedExam = transformExamFromAPI(apiExam)
            setExamData(transformedExam)
          } catch (examErr: any) {
            console.error('Failed to load exam:', examErr)
            
            // ถ้าโหลดจาก API ไม่ได้ ให้ลองโหลดจาก mock data
            const mockExam = getFinalExamById(examId)
            if (mockExam) {
              setExamData(mockExam)
            } else {
              throw new Error(examErr.message || 'ไม่สามารถโหลดข้อมูลข้อสอบได้')
            }
          }
        }
      } catch (err: any) {
        console.error('Failed to load data:', err)
        setError(err.message || 'ไม่สามารถโหลดข้อมูลได้')
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [examId])

  // เพิ่ม useEffect เพื่อจัดการ refs สำหรับรูปภาพตัวเลือก
  useEffect(() => {
    // ตรวจสอบว่ามีคำถามที่เลือกอยู่หรือไม่
    if (examData.questions.length > 0) {
      // กำหนดให้ choiceImageRefs มีขนาดเท่ากับจำนวนตัวเลือกในคำถามปัจจุบัน
      choiceImageRefs.current = Array(examData.questions[selectedQuestion].choices.length).fill(null)
      choiceDropzoneRefs.current = Array(examData.questions[selectedQuestion].choices.length).fill(null)
    }
  }, [selectedQuestion, examData.questions])

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // ฟังก์ชันอัปเดตข้อมูลทั่วไปของข้อสอบ
  const handleExamChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setExamData((prev) => ({
      ...prev,
      [name]: name === "passingScore" || name === "timeLimit" ? Number.parseInt(value) : value,
    }))
  }

  // ฟังก์ชันเพิ่มคำถามใหม่
  const addQuestion = () => {
    const newQuestion: Question = {
      id: generateId(),
      title: "",
      type: "text",
      content: "",
      choices: [
        { id: generateId(), content: "", type: "text", isCorrect: false },
        { id: generateId(), content: "", type: "text", isCorrect: false },
        { id: generateId(), content: "", type: "text", isCorrect: false },
        { id: generateId(), content: "", type: "text", isCorrect: false },
      ],
    }
    setExamData((prev) => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }))
    setSelectedQuestion(examData.questions.length)
  }

  // ฟังก์ชันลบคำถาม
  const removeQuestion = () => {
    if (questionToDelete === null || examData.questions.length <= 1) return

    const updatedQuestions = examData.questions.filter((_, i) => i !== questionToDelete)
    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))

    if (selectedQuestion >= updatedQuestions.length) {
      setSelectedQuestion(updatedQuestions.length - 1)
    } else if (selectedQuestion === questionToDelete) {
      setSelectedQuestion(Math.max(0, questionToDelete - 1))
    }

    setShowDeleteConfirm(false)
    setQuestionToDelete(null)
  }

  const confirmDeleteQuestion = (index: number) => {
    setQuestionToDelete(index)
    setShowDeleteConfirm(true)
  }

  // ฟังก์ชันอัปเดตคำถาม
  const handleQuestionInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    index: number,
  ) => {
    const { name, value } = e.target
    const updatedQuestions = [...examData.questions]
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      [name]: value,
    }
    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันเพิ่มตัวเลือกคำตอบ
  const addChoice = (questionIndex: number) => {
    const newChoice: Choice = {
      id: generateId(),
      content: "",
      type: "text",
      isCorrect: false,
    }
    const updatedQuestions = [...examData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices, newChoice]

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันลบตัวเลือกคำตอบ
  const removeChoice = (questionIndex: number, choiceIndex: number) => {
    const updatedQuestions = [...examData.questions]
    const updatedChoices = updatedQuestions[questionIndex].choices.filter((_, i) => i !== choiceIndex)

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันอัปเดตตัวเลือกคำตอบ
  const handleChoiceInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    questionIndex: number,
    choiceIndex: number,
  ) => {
    const { name, value } = e.target
    const updatedQuestions = [...examData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices]

    updatedChoices[choiceIndex] = {
      ...updatedChoices[choiceIndex],
      [name]: value,
    }

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันเปลี่ยนประเภทของตัวเลือก
  const handleChoiceTypeChange = (questionIndex: number, choiceIndex: number, type: string) => {
    const updatedQuestions = [...examData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices]

    updatedChoices[choiceIndex] = {
      ...updatedChoices[choiceIndex],
      type,
    }

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันเปลี่ยนสถานะความถูกต้องของตัวเลือก
  const handleCorrectAnswerChange = (questionIndex: number, choiceIndex: number) => {
    const updatedQuestions = [...examData.questions]
    const updatedChoices = [...updatedQuestions[questionIndex].choices]

    // Toggle isCorrect สำหรับตัวเลือกที่คลิก
    updatedChoices[choiceIndex] = {
      ...updatedChoices[choiceIndex],
      isCorrect: !updatedChoices[choiceIndex].isCorrect,
    }

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      choices: updatedChoices,
    }

    setExamData((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }))
  }

  // ฟังก์ชันสำหรับการ resize รูปภาพ
  const resizeImage = (file: File, maxWidth: number, maxHeight: number): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (event) => {
        const img = document.createElement("img")
        img.src = event.target?.result as string
        img.onload = () => {
          const canvas = document.createElement("canvas")
          let width = img.width
          let height = img.height

          // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width)
              width = maxWidth
            }
          } else {
            if (height > maxHeight) {
              width = Math.round((width * maxHeight) / height)
              height = maxHeight
            }
          }

          canvas.width = width
          canvas.height = height
          const ctx = canvas.getContext("2d")
          ctx?.drawImage(img, 0, 0, width, height)

          // แปลงเป็น base64
          const dataUrl = canvas.toDataURL("image/jpeg", 0.9)
          resolve(dataUrl)
        }
      }
    })
  }

  const handleQuestionImageUpload = async (e: React.ChangeEvent<HTMLInputElement>, questionIndex: number) => {
    const file = e.target.files?.[0]
    if (file) {
      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        await showErrorAlert("ขนาดไฟล์ใหญ่เกินไป", "ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...examData.questions]
        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          imageUrl: resizedImage,
        }
        setExamData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        await showErrorAlert("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  const handleChoiceImageUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    questionIndex: number,
    choiceIndex: number,
  ) => {
    const file = e.target.files?.[0]
    if (file) {
      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        await showErrorAlert("ขนาดไฟล์ใหญ่เกินไป", "ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...examData.questions]
        const updatedChoices = [...updatedQuestions[questionIndex].choices]

        updatedChoices[choiceIndex] = {
          ...updatedChoices[choiceIndex],
          imageUrl: resizedImage,
        }

        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          choices: updatedChoices,
        }

        setExamData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        await showErrorAlert("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  // ฟังก์ชันสำหรับการ drag and drop รูปภาพคำถาม
  const handleQuestionDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsQuestionDraggingOver(true)
  }

  const handleQuestionDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsQuestionDraggingOver(false)
  }

  const handleQuestionDrop = async (e: React.DragEvent<HTMLDivElement>, questionIndex: number) => {
    e.preventDefault()
    setIsQuestionDraggingOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]

      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!file.type.match("image.*")) {
        await showErrorAlert("ไฟล์ไม่ถูกต้อง", "กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น")
        return
      }

      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        await showErrorAlert("ขนาดไฟล์ใหญ่เกินไป", "ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...examData.questions]
        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          imageUrl: resizedImage,
        }

        setExamData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        await showErrorAlert("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  // ฟังก์ชันสำหรับการ drag and drop รูปภาพตัวเลือก
  const handleChoiceDragOver = (e: React.DragEvent<HTMLDivElement>, choiceIndex: number) => {
    e.preventDefault()
    setIsDraggingOver(choiceIndex)
  }

  const handleChoiceDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDraggingOver(null)
  }

  const handleChoiceDrop = async (e: React.DragEvent<HTMLDivElement>, questionIndex: number, choiceIndex: number) => {
    e.preventDefault()
    setIsDraggingOver(null)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]

      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!file.type.match("image.*")) {
        await showErrorAlert("ไฟล์ไม่ถูกต้อง", "กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น")
        return
      }

      // ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB)
      if (file.size > 2 * 1024 * 1024) {
        await showErrorAlert("ขนาดไฟล์ใหญ่เกินไป", "ขนาดไฟล์ต้องไม่เกิน 2MB")
        return
      }

      try {
        // Resize รูปภาพเป็น 500x500
        const resizedImage = await resizeImage(file, 500, 500)

        const updatedQuestions = [...examData.questions]
        const updatedChoices = [...updatedQuestions[questionIndex].choices]

        updatedChoices[choiceIndex] = {
          ...updatedChoices[choiceIndex],
          imageUrl: resizedImage,
        }

        updatedQuestions[questionIndex] = {
          ...updatedQuestions[questionIndex],
          choices: updatedChoices,
        }

        setExamData((prev) => ({
          ...prev,
          questions: updatedQuestions,
        }))
      } catch (error) {
        console.error("Error resizing image:", error)
        await showErrorAlert("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ")
      }
    }
  }

  const triggerQuestionImageInput = () => {
    questionImageRef.current?.click()
  }

  const triggerChoiceImageInput = (index: number) => {
    choiceImageRefs.current[index]?.click()
  }

  const saveQuestion = (index: number) => {
    // สำหรับการบันทึกคำถามแต่ละข้อ (ถ้าต้องการ)
    // ในที่นี้เป็นเพียงการแสดงสถานะการบันทึก
    console.log("กำลังบันทึกคำถาม:", examData.questions[index])
    
    // แสดงการบันทึกสำเร็จด้วย SweetAlert
    showSuccessAlert("บันทึกคำถามสำเร็จ", `คำถามข้อที่ ${index + 1} ได้รับการบันทึกแล้ว`)
  }

  // ฟังก์ชันบันทึกข้อสอบ
  const confirmSaveAllChanges = async () => {
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!examData.name.trim()) {
      await showErrorAlert("กรุณากรอกชื่อข้อสอบ", "ชื่อข้อสอบเป็นข้อมูลที่จำเป็น")
      setActiveTab("exam")
      return
    }

    if (!examData.courseId) {
      await showErrorAlert("กรุณาเลือกคอร์สที่เกี่ยวข้อง", "คอร์สเป็นข้อมูลที่จำเป็น")
      setActiveTab("exam")  
      return
    }

    if (examData.questions.length === 0) {
      await showErrorAlert("กรุณาเพิ่มคำถาม", "ข้อสอบต้องมีคำถามอย่างน้อย 1 ข้อ")
      setActiveTab("questions")
      return
    }

    // ตรวจสอบคำถามทุกข้อ
    const invalidQuestions = examData.questions.findIndex((q, idx) => {
      if (!q.title.trim()) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        showErrorAlert("กรุณากรอกคำถาม", `กรุณากรอกคำถามข้อที่ ${idx + 1}`)
        return true
      }

      // ตรวจสอบตัวเลือก
      const emptyChoices = q.choices.some((c) => c.type === "text" && !c.content.trim())
      if (emptyChoices) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        showErrorAlert("กรุณากรอกตัวเลือก", `กรุณากรอกตัวเลือกให้ครบทุกข้อในคำถามข้อที่ ${idx + 1}`)
        return true
      }

      // ตรวจสอบว่ามีการเลือกคำตอบที่ถูกต้องอย่างน้อย 1 ข้อ
      const hasCorrectAnswer = q.choices.some((c) => c.isCorrect)
      if (!hasCorrectAnswer) {
        setSelectedQuestion(idx)
        setActiveTab("questions")
        showErrorAlert("กรุณาเลือกคำตอบที่ถูกต้อง", `กรุณาเลือกคำตอบที่ถูกต้องอย่างน้อย 1 ข้อในคำถามข้อที่ ${idx + 1}`)
        return true
      }

      return false
    })

    // ถ้าผ่านการตรวจสอบทั้งหมด แสดง confirm dialog
    if (invalidQuestions === -1) {
      const result = await showConfirmDialog(
        'ยืนยันการบันทึกข้อสอบ',
        `คุณต้องการบันทึกข้อสอบ "${examData.name}" หรือไม่?`
      )
      
      if (result.isConfirmed) {
        await saveAllChanges()
      }
    }
  }

  const saveAllChanges = async () => {
    setIsSaving(true)
    setError(null)

    try {
      // หาข้อมูลคอร์สเพื่อใช้ course slug
      const selectedCourse = courses.find(course => course.slug === examData.courseId)
      if (!selectedCourse) {
        throw new Error('ไม่พบข้อมูลคอร์สที่เลือก')
      }

      // ใช้ course slug หรือ id เป็น slug
      const courseSlug = selectedCourse.slug

      // แปลงข้อมูลเป็นรูปแบบที่ API ต้องการ
      const apiData = transformExamToAPI(examData, courseSlug)

      let response
      if (examId) {
        // อัปเดตข้อสอบ
        response = await examService.updateExam(examId, apiData)
      } else {
        // สร้างข้อสอบใหม่
        response = await examService.createExam(apiData)
      }

      console.log('บันทึกข้อสอบสำเร็จ:', response)

      // แสดงการบันทึกสำเร็จ
      await showSuccessAlert(
        'บันทึกสำเร็จ!',
        `ข้อสอบ "${examData.name}" ได้รับการบันทึกเรียบร้อยแล้ว`
      )

      // กลับไปหน้าจัดการข้อสอบ
      router.push("/admin/exams")

    } catch (err: any) {
      console.error('Failed to save exam:', err)
      const errorMessage = err.message || 'ไม่สามารถบันทึกข้อสอบได้'
      setError(errorMessage)
      
      await showErrorAlert(
        'เกิดข้อผิดพลาด',
        errorMessage
      )
    } finally {
      setIsSaving(false)
    }
  }

  // ฟังก์ชันรีเฟรชข้อมูลคอร์ส
  const refreshCourses = async () => {
    try {
      const coursesData = await courseService.getCourses()
      const transformedCourses = coursesData.map(transformCourseFromAPI)
      setCourses(transformedCourses)
      await showSuccessAlert("โหลดข้อมูลสำเร็จ", "ข้อมูลคอร์สได้รับการอัปเดตแล้ว")
    } catch (err: any) {
      console.error('Failed to refresh courses:', err)
      await showErrorAlert("เกิดข้อผิดพลาด", "ไม่สามารถโหลดข้อมูลคอร์สใหม่ได้")
    }
  }

  // Handle drag start event
  const handleDragStart = (event: any) => {
    console.log('Drag started:', event.active.id)
    setActiveId(event.active.id)
  }

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    
    console.log('Drag ended:', { activeId: active.id, overId: over?.id })
    setActiveId(null)

    if (over && active.id !== over.id) {
      setExamData((prev) => {
        // Find the indices of the dragged item and the drop target
        const oldIndex = prev.questions.findIndex((question) => question.id === active.id)
        const newIndex = prev.questions.findIndex((question) => question.id === over.id)

        console.log('Moving question from index', oldIndex, 'to index', newIndex)

        // Check if both indices are valid
        if (oldIndex === -1 || newIndex === -1) {
          console.warn('Invalid drag operation: could not find question indices')
          return prev
        }

        // Update selectedQuestion to follow the dragged item
        if (selectedQuestion === oldIndex) {
          setSelectedQuestion(newIndex)
        } else if (selectedQuestion >= newIndex && selectedQuestion < oldIndex) {
          setSelectedQuestion(selectedQuestion + 1)
        } else if (selectedQuestion <= newIndex && selectedQuestion > oldIndex) {
          setSelectedQuestion(selectedQuestion - 1)
        }

        return {
          ...prev,
          questions: arrayMove(prev.questions, oldIndex, newIndex),
        }
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">
            {examId ? "กำลังโหลดข้อมูลข้อสอบและคอร์ส..." : "กำลังโหลดข้อมูลคอร์ส..."}
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="bg-red-100 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">เกิดข้อผิดพลาด</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-[#008268] hover:bg-[#006e58] text-white px-4 py-2 rounded-md"
            >
              โหลดข้อมูลใหม่
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with ChevronLeft back button and save button */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <button
            onClick={() => router.push('/admin/exams')}
            className="p-0 bg-transparent border-none focus:outline-none flex items-center"
            aria-label="ย้อนกลับไปหน้าตารางข้อสอบ"
            type="button"
          >
            <ChevronLeft className="h-7 w-7" />
          </button>
          <h1 className="text-xl font-bold text-gray-800">{examId ? "แก้ไขข้อสอบท้ายบท" : "สร้างข้อสอบท้ายบทใหม่"}</h1>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={confirmSaveAllChanges}
            className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md font-medium flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            บันทึก
          </button>
        </div>
      </div>

      {/* Navigation tabs */}
      <div className="flex border-b border-gray-200">
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${
            activeTab === "exam" ? "text-[#008268] border-b-2 border-[#008268]" : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("exam")}
        >
          ข้อมูลข้อสอบ
        </button>
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${
            activeTab === "questions"
              ? "text-[#008268] border-b-2 border-[#008268]"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("questions")}
        >
          จัดการคำถาม
        </button>
      </div>

      {/* Exam form */}
      {activeTab === "exam" && (
        <div className="p-6">
          {/* Two columns for details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - Exam details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดของข้อสอบ</h3>

                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    ชื่อข้อสอบ
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={examData.name}
                    onChange={handleExamChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกชื่อข้อสอบ"
                  />
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-1">
                    <label htmlFor="courseId" className="block text-sm font-medium text-gray-700">
                      คอร์สที่เกี่ยวข้อง
                    </label>
                    <button
                      type="button"
                      onClick={refreshCourses}
                      className="text-xs text-[#008268] hover:text-[#006e58] flex items-center gap-1"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      รีเฟรช
                    </button>
                  </div>
                  <div className="relative">
                    <select
                      id="courseId"
                      name="courseId"
                      value={examData.courseId}
                      onChange={handleExamChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="">เลือกคอร์ส</option>
                      {courses.length > 0 ? (
                        courses.map((course) => (
                          <option key={course.slug} value={course.slug}>
                            {course.name}
                          </option>
                        ))
                      ) : (
                        <option value="" disabled>
                          ไม่พบข้อมูลคอร์ส
                        </option>
                      )}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    คำอธิบายข้อสอบ
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={examData.description || ""}
                    onChange={handleExamChange}
                    rows={5}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำอธิบายข้อสอบ"
                  />
                </div>
              </div>
            </div>

            {/* Right column - Additional details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดเพิ่มเติม</h3>

                <div className="mb-4">
                  <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 mb-1">
                    เวลาในการทำข้อสอบ (นาที)
                  </label>
                  <input
                    type="number"
                    id="timeLimit"
                    name="timeLimit"
                    min="1"
                    max="180"
                    value={examData.timeLimit}
                    onChange={handleExamChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passingScore" className="block text-sm font-medium text-gray-700 mb-1">
                    คะแนนผ่าน (%)
                  </label>
                  <input
                    type="number"
                    id="passingScore"
                    name="passingScore"
                    min="1"
                    max="100"
                    value={examData.passingScore}
                    onChange={handleExamChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">จำนวนคำถาม</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-700">
                    {examData.questions.length} คำถาม
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Questions form */}
      {activeTab === "questions" && (
        <div className="flex h-[calc(100vh-220px)] min-h-[500px]">
          {/* Left sidebar - Question list with drag and drop */}
          <div className="w-full md:w-1/3 lg:w-3/10 border-r border-gray-200 bg-gray-50 p-4 overflow-y-auto">
            <DndContext 
              sensors={sensors} 
              collisionDetection={closestCenter} 
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={examData.questions.map((question) => question.id)}
                strategy={verticalListSortingStrategy}
              >
                {examData.questions.map((question, index) => (
                  <SortableQuestion
                    key={question.id}
                    question={question}
                    index={index}
                    isSelected={selectedQuestion === index}
                    onSelect={() => setSelectedQuestion(index)}
                  />
                ))}
              </SortableContext>
              <DragOverlay>
                {activeId ? (
                  <div className="p-3 rounded-md bg-white border border-[#008268] shadow-lg">
                    <div className="flex items-center">
                      <div className="mr-2 text-gray-400">
                        <GripVertical size={16} />
                      </div>
                      <span className="text-sm font-medium mr-2">
                        {examData.questions.findIndex(q => q.id === activeId) + 1}.
                      </span>
                      <div className="flex-1 text-sm truncate">
                        {examData.questions.find(q => q.id === activeId)?.title || 
                         `คำถามที่ ${examData.questions.findIndex(q => q.id === activeId) + 1}`}
                      </div>
                    </div>
                  </div>
                ) : null}
              </DragOverlay>
            </DndContext>

            <button
              onClick={addQuestion}
              className="mt-4 w-full flex items-center justify-center p-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-1" />
              <span className="text-sm">เพิ่มคำถาม</span>
            </button>
          </div>

          {/* Right content - Question editor */}
          <div className="w-full md:w-2/3 lg:w-7/10 p-6 overflow-y-auto">
            {examData.questions.length > 0 && (
              <div>
                <div className="mb-4">
                  <label htmlFor="questionType" className="block text-sm font-medium text-gray-700 mb-1">
                    ประเภทคำถาม
                  </label>
                  <div className="relative">
                    <select
                      id="questionType"
                      name="type"
                      value={examData.questions[selectedQuestion].type || "text"}
                      onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="text">ข้อความ</option>
                      <option value="image">รูปภาพ</option>
                      <option value="text_and_image">ข้อความและรูปภาพ</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="questionTitle" className="block text-sm font-medium text-gray-700 mb-1">
                    คำถาม
                  </label>
                  <input
                    type="text"
                    id="questionTitle"
                    name="title"
                    value={examData.questions[selectedQuestion].title}
                    onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำถาม"
                  />
                </div>

                {(examData.questions[selectedQuestion].type === "image" ||
                  examData.questions[selectedQuestion].type === "text_and_image") && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">รูปภาพประกอบคำถาม</label>
                    <input
                      type="file"
                      ref={questionImageRef}
                      onChange={(e) => handleQuestionImageUpload(e, selectedQuestion)}
                      className="hidden"
                      accept="image/*"
                    />
                    <div
                      ref={questionDropzoneRef}
                      onClick={triggerQuestionImageInput}
                      onDragOver={handleQuestionDragOver}
                      onDragLeave={handleQuestionDragLeave}
                      onDrop={(e) => handleQuestionDrop(e, selectedQuestion)}
                      className={`relative w-full h-40 bg-gray-100 rounded-lg border-2 ${
                        isQuestionDraggingOver ? "border-[#008268] bg-[#E6F2F0]" : "border-dashed border-gray-300"
                      } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50`}
                    >
                      {examData.questions[selectedQuestion].imageUrl ? (
                        <div className="relative w-full h-full">
                          <Image
                            src={examData.questions[selectedQuestion].imageUrl || "/placeholder.svg"}
                            alt="Question image"
                            fill
                            sizes="100%"
                            className="object-contain rounded-lg"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                            <div className="bg-white p-2 rounded-full">
                              <Upload className="h-5 w-5 text-gray-700" />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <Upload className="h-12 w-12 text-gray-400 mb-2" />
                          <p className="text-sm text-gray-500">คลิกหรือลากไฟล์มาวางที่นี่เพื่ออัพโหลดรูปภาพ</p>
                          <p className="text-xs text-gray-400 mt-1">ขนาดแนะนำ 500x500 px</p>
                        </>
                      )}
                    </div>
                  </div>
                )}
                {(examData.questions[selectedQuestion].type === "text" ||
                  examData.questions[selectedQuestion].type === "text_and_image") && (
                  <div className="mb-4">
                    <label htmlFor="questionContent" className="block text-sm font-medium text-gray-700 mb-1">
                      รายละเอียดคำถาม
                    </label>
                    <textarea
                      id="questionContent"
                      name="content"
                      value={examData.questions[selectedQuestion].content}
                      onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                      placeholder="กรอกรายละเอียดคำถาม (ถ้ามี)"
                    />
                  </div>
                )}
                <div className="mt-6 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">ตัวเลือกคำตอบ</h3>

                  <div className="space-y-4">
                    {examData.questions[selectedQuestion].choices.map((choice, choiceIndex) => (
                      <div key={choice.id} className="border border-gray-200 rounded-md p-4">
                        <div className="flex items-center mb-3">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={`choice-${choiceIndex}`}
                              name={`correct-answer-${selectedQuestion}-${choiceIndex}`}
                              checked={choice.isCorrect}
                              onChange={() => handleCorrectAnswerChange(selectedQuestion, choiceIndex)}
                              className="h-4 w-4 text-[#008268] focus:ring-[#008268] border-gray-300 rounded"
                            />
                            <label htmlFor={`choice-${choiceIndex}`} className="ml-2 text-sm font-medium text-gray-700">
                              คำตอบที่ถูกต้อง
                            </label>
                          </div>

                          <div className="ml-auto">
                            <div className="relative">
                              <select
                                value={choice.type || "text"}
                                onChange={(e) => handleChoiceTypeChange(selectedQuestion, choiceIndex, e.target.value)}
                                className="text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-8 pl-2 py-1"
                              >
                                <option value="text">ข้อความ</option>
                                <option value="image">รูปภาพ</option>
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <ChevronDown className="h-3 w-3 text-gray-500" />
                              </div>
                            </div>
                          </div>
                        </div>

                        {choice.type === "text" ? (
                          <input
                            type="text"
                            name="content"
                            value={choice.content}
                            onChange={(e) => handleChoiceInputChange(e, selectedQuestion, choiceIndex)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                            placeholder={`ตัวเลือกที่ ${choiceIndex + 1}`}
                          />
                        ) : (
                          <div>
                            <input
                              type="file"
                              ref={(el) => {
                                // แก้ไขวิธีการกำหนดค่า ref
                                if (choiceImageRefs.current.length > choiceIndex) {
                                  choiceImageRefs.current[choiceIndex] = el
                                }
                              }}
                              onChange={(e) => handleChoiceImageUpload(e, selectedQuestion, choiceIndex)}
                              className="hidden"
                              accept="image/*"
                            />
                            <div
                              ref={(el) => {
                                if (choiceDropzoneRefs.current.length > choiceIndex) {
                                  choiceDropzoneRefs.current[choiceIndex] = el
                                }
                              }}
                              onClick={() => triggerChoiceImageInput(choiceIndex)}
                              onDragOver={(e) => handleChoiceDragOver(e, choiceIndex)}
                              onDragLeave={handleChoiceDragLeave}
                              onDrop={(e) => handleChoiceDrop(e, selectedQuestion, choiceIndex)}
                              className={`relative w-full h-24 bg-gray-100 rounded-lg border-2 ${
                                isDraggingOver === choiceIndex
                                  ? "border-[#008268] bg-[#E6F2F0]"
                                  : "border-dashed border-gray-300"
                              } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50`}
                            >
                              {choice.imageUrl ? (
                                <div className="relative w-full h-full">
                                  <Image
                                    src={choice.imageUrl || "/placeholder.svg"}
                                    alt={`Choice ${choiceIndex + 1}`}
                                    fill
                                    sizes="100%"
                                    className="object-contain rounded-lg"
                                  />
                                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                                    <div className="bg-white p-2 rounded-full">
                                      <Upload className="h-4 w-4 text-gray-700" />
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <>
                                  <Upload className="h-8 w-8 text-gray-400 mb-1" />
                                  <p className="text-xs text-gray-500">คลิกหรือลากไฟล์มาวางที่นี่</p>
                                  <p className="text-xs text-gray-400">ขนาดแนะนำ 500x500 px</p>
                                </>
                              )}
                            </div>
                            <input
                              type="text"
                              name="content"
                              value={choice.content}
                              onChange={(e) => handleChoiceInputChange(e, selectedQuestion, choiceIndex)}
                              className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                              placeholder="คำอธิบายรูปภาพ (ถ้ามี)"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  <button
                    type="button"
                    onClick={() => addChoice(selectedQuestion)}
                    className="mt-3 inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <PlusCircle className="h-4 w-4 mr-1" />
                    เพิ่มตัวเลือก
                  </button>
                </div>
                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => confirmDeleteQuestion(selectedQuestion)}
                    className="flex items-center px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50"
                    disabled={examData.questions.length <= 1}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    <span>ลบคำถาม</span>
                  </button>
                  <button
                    onClick={() => saveQuestion(selectedQuestion)}
                    className="px-4 py-2 bg-[#008268] hover:bg-[#006e58] text-white rounded-md flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    บันทึกคำถาม
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete confirmation modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>

            <div className="flex items-center mb-4">
              <div className="bg-red-100 p-3 rounded-full mr-4">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">ยืนยันการลบคำถาม</h3>
            </div>

            <p className="text-gray-600 mb-6">
              คุณแน่ใจหรือไม่ว่าต้องการลบคำถาม "
              {questionToDelete !== null && examData.questions[questionToDelete]?.title
                ? examData.questions[questionToDelete].title
                : `คำถามที่ ${questionToDelete !== null ? questionToDelete + 1 : ""}`}
              "? การกระทำนี้ไม่สามารถย้อนกลับได้
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                ยกเลิก
              </button>
              <button
                onClick={removeQuestion}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                ยืนยันการลบ
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
