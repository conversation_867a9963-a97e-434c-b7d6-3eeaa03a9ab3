"use client"

import { type ReactNode, useState } from "react"
import Sidebar from "./sidebar"
import Navbar from "./navbar"

interface AdminLayoutProps {
  children: ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  // Add state for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  // Mock user data - replace with actual user data from your auth system
  const user = {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Admin",
    avatar: "", // Add avatar URL if available
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar 
        user={user} 
        isCollapsed={isCollapsed} 
        setIsCollapsed={setIsCollapsed}
        isMobileOpen={isMobileOpen}
        setIsMobileOpen={setIsMobileO<PERSON>}
      />
      <Navbar 
        isCollapsed={isCollapsed}
        toggleMobileSidebar={toggleMobileSidebar}
        isMobileOpen={isMobileOpen}
      />

      <main className={`pt-16 sm:pt-16 transition-all duration-300 ${isCollapsed ? "md:pl-20" : "md:pl-64"}`}>
        <div className="p-4 md:p-6">{children}</div>
      </main>
    </div>
  )
}

