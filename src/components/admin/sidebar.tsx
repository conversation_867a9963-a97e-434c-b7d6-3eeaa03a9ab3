"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Menu, BookOpen, FileText, Users, AlignJustify, Home, Route, Compass, X, PictureInPicture2 } from "lucide-react"

interface SidebarProps {
  user: {
    name: string
    role: string
    avatar?: string
  }
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
  isMobileOpen?: boolean
  setIsMobileOpen?: (open: boolean) => void
}

export default function Sidebar({ user, isCollapsed, setIsCollapsed, isMobileOpen: propIsMobileOpen, setIsMobileOpen }: SidebarProps) {
  const [isMobileOpen, setIsMobileOpenLocal] = useState(false)
  const pathname = usePathname()

  // Use prop state if provided, otherwise use local state
  const actualIsMobileOpen = propIsMobileOpen !== undefined ? propIsMobileOpen : isMobileOpen
  const actualSetIsMobileOpen = setIsMobileOpen || setIsMobileOpenLocal

  useEffect(() => {
    actualSetIsMobileOpen(false)
  }, [pathname, actualSetIsMobileOpen])

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const toggleMobileSidebar = () => {
    actualSetIsMobileOpen(!actualIsMobileOpen)
  }

  // Store icon as component, not element
  const menuCategories = [
    {
      category: "การจัดการระบบ",
      items: [
        {
          title: "หน้าหลัก",
          icon: Home,
          path: "/admin",
          active: pathname === "/admin" || pathname === "/admin/",
        },
        {
          title: "จัดการผู้ใช้",
          icon: Users,
          path: "/admin/users",
          active: pathname.startsWith("/admin/users"),
        },
        {
          title: "จัดการคอร์ส",
          icon: BookOpen,
          path: "/admin/courses",
          active: pathname.startsWith("/admin/courses"),
        },
      ],
    },
    {
      category: "การจัดการเนื้อหา",
      items: [
        {
          title: "จัดการควิซ",
          icon: PictureInPicture2,
          path: "/admin/quiz",
          active: pathname.startsWith("/admin/quiz"),
        },
        {
          title: "จัดการข้อสอบท้ายบท",
          icon: FileText,
          path: "/admin/exams",
          active: pathname.startsWith("/admin/exams"),
        },
        {
          title: "จัดการเส้นทางการเรียนรู้",
          icon: Route,
          path: "/admin/learning-paths",
          active: pathname.startsWith("/admin/learning-paths"),
        },
        {
          title: "กำหนดเส้นทางการเรียนรู้",
          icon: Compass,
          path: "/admin/assign-paths",
          active: pathname.startsWith("/admin/assign-paths"),
        },
      ],
    },
    {
      category: "รายงาน",
      items: [
        {
          title: "รายงานส่วนบุคคล",
          icon: Users, // You can change the icon if needed
          path: "/admin/report/personal",
          active: pathname.startsWith("/admin/report/personal"),
        },
        {
          title: "รายงานห้องเรียน",
          icon: BookOpen, // You can change the icon if needed
          path: "/admin/report/classroom",
          active: pathname.startsWith("/admin/report/classroom"),
        },
        {
          title: "รายงานHR",
          icon: FileText, // You can change the icon if needed
          path: "/admin/report/hr",
          active: pathname.startsWith("/admin/report/hr"),
        },
      ],
    },
  ]

  return (
    <>
      {/* Mobile Toggle Button */}
      <button
        data-drawer-target="sidebar-multi-level-sidebar"
        data-drawer-toggle="sidebar-multi-level-sidebar"
        aria-controls="sidebar-multi-level-sidebar"
        type="button"
        className="inline-flex items-center p-2 mt-2 ms-3 text-sm text-[#008067] rounded-lg sm:hidden hover:bg-[#008067]/10 focus:outline-none focus:ring-2 focus:ring-[#008067] group"
        onClick={toggleMobileSidebar}
      >
        <span className="sr-only">Open sidebar</span>
        <AlignJustify className="w-6 h-6 transition-colors group-hover:text-black" />
      </button>

      {/* Sidebar */}
      <aside
        id="sidebar-multi-level-sidebar"
        className={`fixed top-0 left-0 z-50 ${isCollapsed ? 'w-20' : 'w-64'} h-screen transition-all duration-300 bg-white \
          ${actualIsMobileOpen ? 'translate-x-0' : '-translate-x-full sm:translate-x-0'} \
          shadow-xl shadow-gray-300`}
        aria-label="Sidebar"
      >
        <div className="h-full px-3 py-4 overflow-y-auto">
          {/* Mobile Navbar Spacer - Only on mobile when sidebar is open */}
          <div className="sm:hidden h-16 mb-4"></div>
          
          {/* Sidebar Header */}
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'} mb-4 h-16`}>
            {!isCollapsed && (
              <div className="flex items-center space-x-3">
                <Image
                  className="w-full h-12 object-cover"
                  src={user.avatar || "/e-med/img/logo-1.png"}
                  alt="Admin Logo"
                  width={200}
                  height={48}
                />
              </div>
            )}
            {/* Collapse Button (desktop only) */}
            <button
              onClick={toggleSidebar}
              className={`text-[#008067] hover:text-white hidden sm:flex items-center justify-center ${isCollapsed ? 'h-10 w-10 rounded-md hover:bg-[#008067] bg-white' : ''}`}
            >
              <Menu size={24} />
            </button>
            {/* Mobile Close Button */}
            <button onClick={toggleMobileSidebar} className="text-[#008067] hover:text-white sm:hidden">
              <X size={20} />
            </button>
          </div>
          {/* Sidebar Menu */}
          <ul className="space-y-6 font-medium">
            {menuCategories.map((cat, catIdx) => (
              <li key={catIdx}>
                {!isCollapsed && (
                  <div className="text-xs text-gray-400 font-bold px-3 mb-1 mt-2 select-none">
                    {cat.category}
                  </div>
                )}
                <ul className="space-y-2">
                  {cat.items.map((item, index) => (
                    <li key={index}>
                      <Link
                        href={item.path}
                        className={`group flex items-center ${isCollapsed ? 'justify-center' : ''} px-3 py-4 rounded-lg transition-all duration-200 font-medium
                          ${item.active ? 'bg-[#008067] text-white shadow' : 'text-gray-900 hover:bg-gray-200'}
                          ${isCollapsed ? 'h-16' : ''}
                        `}
                      >
                        <span
                          className={`flex items-center justify-center transition-all duration-150
                            ${isCollapsed ? 'p-1.5 rounded-md' : 'mr-3'}
                            ${item.active ? 'bg-[#008067] text-white' : 'bg-transparent text-black'}
                          `}
                        >
                          <item.icon size={24} />
                        </span>
                        {!isCollapsed && <span className="flex-1 whitespace-nowrap">{item.title}</span>}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      </aside>
    </>
  )
}
