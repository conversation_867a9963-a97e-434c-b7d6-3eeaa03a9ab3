"use client"

import { useState } from "react"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js"
import { Line } from "react-chartjs-2"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function ActiveUsersChart() {
  const [timeRange, setTimeRange] = useState<"30days" | "12months">("30days")

  // Mock data for 30 days
  const dailyData = {
    labels: Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return date.toLocaleDateString("th-TH", { month: "short", day: "numeric" })
    }),
    datasets: [
      {
        label: "ผู้ใช้ที่ใช้งานรายวัน",
        data: [
          120, 135, 142, 128, 156, 189, 167, 145, 178, 192, 
          156, 134, 167, 189, 201, 178, 156, 189, 234, 198,
          167, 145, 189, 212, 234, 189, 167, 178, 201, 156
        ],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: "rgb(59, 130, 246)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 8,
        pointHoverBackgroundColor: "rgb(59, 130, 246)",
        pointHoverBorderColor: "#ffffff",
        pointHoverBorderWidth: 3,
      },
    ],
  }

  // Mock data for 12 months
  const monthlyData = {
    labels: [
      "ม.ค.", "ก.พ.", "มี.ค.", "เม.ย.", "พ.ค.", "มิ.ย.",
      "ก.ค.", "ส.ค.", "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค."
    ],
    datasets: [
      {
        label: "ผู้ใช้ที่ใช้งานรายเดือน",
        data: [3200, 3450, 3890, 4120, 4350, 4680, 4920, 5150, 5380, 5620, 5890, 6120],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: "rgb(59, 130, 246)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 8,
        pointHoverBackgroundColor: "rgb(59, 130, 246)",
        pointHoverBorderColor: "#ffffff",
        pointHoverBorderWidth: 3,
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          usePointStyle: true,
          pointStyle: "circle",
          padding: 20,
          color: "#374151",
        },
      },
      tooltip: {
        backgroundColor: "rgba(17, 24, 39, 0.95)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "rgba(59, 130, 246, 0.8)",
        borderWidth: 2,
        cornerRadius: 12,
        padding: 12,
        displayColors: true,
        boxPadding: 6,
      },
    },
    elements: {
      point: {
        hoverBorderWidth: 3,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          padding: 8,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
          drawBorder: false,
        },
        border: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          padding: 8,
        },
      },
    },
    interaction: {
      intersect: false,
      mode: "index" as const,
    },
  }

  return (
    <div className="w-full">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="flex bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl p-1.5 shadow-inner">
          <button
            onClick={() => setTimeRange("30days")}
            className={`px-4 py-2 text-sm rounded-lg transition-all duration-200 font-medium ${
              timeRange === "30days"
                ? "bg-white text-blue-600 shadow-lg transform scale-105"
                : "text-gray-600 hover:text-gray-800 hover:bg-white/50"
            }`}
          >
            30 วัน
          </button>
          <button
            onClick={() => setTimeRange("12months")}
            className={`px-4 py-2 text-sm rounded-lg transition-all duration-200 font-medium ${
              timeRange === "12months"
                ? "bg-white text-blue-600 shadow-lg transform scale-105"
                : "text-gray-600 hover:text-gray-800 hover:bg-white/50"
            }`}
          >
            12 เดือน
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-64 mb-6 p-2 bg-gradient-to-br from-gray-50/50 to-white rounded-xl">
        <Line 
          data={timeRange === "30days" ? dailyData : monthlyData} 
          options={options} 
        />
      </div>

      {/* Metrics Summary */}
      <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
        <div className="relative bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-100 hover:shadow-md transition-all duration-200 group">
          <div className="absolute top-2 right-2 w-8 h-8 bg-green-200/50 rounded-full"></div>
          <div className="relative z-10">
            <p className="text-sm font-medium text-gray-600 mb-1">เวลาเซสชันเฉลี่ย</p>
            <p className="text-2xl font-bold text-gray-800 mb-2">45 นาที</p>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <p className="text-xs text-green-700 font-semibold">+8.2% จากเดือนที่แล้ว</p>
            </div>
          </div>
        </div>
        <div className="relative bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100 hover:shadow-md transition-all duration-200 group">
          <div className="absolute top-2 right-2 w-8 h-8 bg-blue-200/50 rounded-full"></div>
          <div className="relative z-10">
            <p className="text-sm font-medium text-gray-600 mb-1">อัตราการกลับมาใช้</p>
            <p className="text-2xl font-bold text-gray-800 mb-2">68%</p>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <p className="text-xs text-blue-700 font-semibold">+5.1% จากเดือนที่แล้ว</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
