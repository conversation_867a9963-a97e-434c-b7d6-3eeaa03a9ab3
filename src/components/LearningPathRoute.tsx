"use client"

import { useEffect, useState, useRef, useCallback } from "react"
import { useRouter } from "next/navigation"
import { Clock, CheckCircle, Search, Target, TrendingUp, Route, ArrowLeft } from "lucide-react"
import LearningPathsCard from "./learming/LearningPathCard"
import { profileService, UserPathway, PathwaysDashboardResponse } from "@/hook/profileService"

const LearningPathsRoute = () => {
  const router = useRouter()
  const [userPathways, setUserPathways] = useState<UserPathway[]>([])
  const [filteredPathways, setFilteredPathways] = useState<UserPathway[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    items_per_page: 0, // Set to 0 or remove if not used in UI
    total_items: 0,
    has_next: false,
    has_prev: false,
  })
  const [page, setPage] = useState(1)
  const [overviewPath, setOverviewPath] = useState<null | {
    total_path: number;
    total_path_completed: number;
    total_path_in_progress: number;
    total_avg_time: number;
  }>(null)
  const [isFetchingMore, setIsFetchingMore] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [showPlaceholders, setShowPlaceholders] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const observer = useRef<IntersectionObserver | null>(null)
  const sentinelRef = useRef<HTMLDivElement>(null)

  // Fetch pathways for the current page
  useEffect(() => {
    const fetchPathways = async () => {
      setLoading(true)
      try {
        // Only pass page, not items_per_page
        const response: PathwaysDashboardResponse = await profileService.getUserPathwaysDashboard(page)
        const pathways = response && Array.isArray(response.data) ? response.data : []
        setUserPathways(pathways)
        setFilteredPathways(pathways)
        setPagination({
          current_page: response.pagination.current_page,
          total_pages: response.pagination.total_pages,
          items_per_page: response.pagination.items_per_page,
          total_items: response.pagination.total_items,
          has_next: response.pagination.has_next,
          has_prev: response.pagination.has_prev,
        })
      } catch (error) {
        setUserPathways([])
        setFilteredPathways([])
        setPagination({
          current_page: 1,
          total_pages: 1,
          items_per_page: 0, // Set to 0 or remove if not used in UI
          total_items: 0,
          has_next: false,
          has_prev: false,
        })
      } finally {
        setLoading(false)
      }
    }
    fetchPathways()
  }, [page])

  // Fetch overview path data
  useEffect(() => {
    profileService.getUserOverviewPath()
      .then(setOverviewPath)
      .catch(() => setOverviewPath(null))
  }, [])

  // Filter pathways based on search term (client-side)
  useEffect(() => {
    if (!searchTerm) {
      setFilteredPathways(userPathways)
      return
    }
    const filtered = userPathways.filter(
      (pathway) =>
        pathway.path_slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (pathway.path_description || "").toLowerCase().includes(searchTerm.toLowerCase()),
    )
    setFilteredPathways(filtered)
  }, [userPathways, searchTerm])  // Infinite scroll: fetch next page when sentinel is visible
  const fetchMore = useCallback(async () => {
    if (isFetchingMore || loading || !pagination.has_next) return
    setIsFetchingMore(true)
    setShowPlaceholders(true)
    setHasError(false)

    // Start timer for minimum 2 seconds
    const startTime = Date.now()

    try {
      const nextPage = pagination.current_page + 1
      const response: PathwaysDashboardResponse = await profileService.getUserPathwaysDashboard(nextPage)
      const newPathways = response && Array.isArray(response.data) ? response.data : []

      // Ensure minimum 2 seconds have passed before showing data
      const elapsedTime = Date.now() - startTime
      const remainingTime = Math.max(0, 500 - elapsedTime)

      setTimeout(() => {
        setUserPathways((prev) => [...prev, ...newPathways])
        setFilteredPathways((prev) => [...prev, ...newPathways])
        setPagination({
          current_page: response.pagination.current_page,
          total_pages: response.pagination.total_pages,
          items_per_page: response.pagination.items_per_page,
          total_items: response.pagination.total_items,
          has_next: response.pagination.has_next,
          has_prev: response.pagination.has_prev,
        })
        setShowPlaceholders(false)
        setIsFetchingMore(false)
      }, remainingTime)
    } catch (error) {
      setHasError(true)
      console.error('Failed to fetch more pathways:', error)

      // Ensure minimum 2 seconds have passed before hiding placeholders on error
      const elapsedTime = Date.now() - startTime
      const remainingTime = Math.max(0, 2000 - elapsedTime)

      setTimeout(() => {
        setShowPlaceholders(false)
        setIsFetchingMore(false)
      }, remainingTime)
    }
  }, [isFetchingMore, loading, pagination])

  useEffect(() => {
    if (!sentinelRef.current) return
    if (observer.current) observer.current.disconnect()

    observer.current = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && pagination.has_next && !isFetchingMore && !showPlaceholders) {
          fetchMore()
        }
      },
      {
        root: null,
        rootMargin: '100px', // Start loading 100px before reaching the sentinel
        threshold: 0.1
      }
    )

    observer.current.observe(sentinelRef.current)
    return () => {
      if (observer.current) observer.current.disconnect()
    }
  }, [fetchMore, pagination.has_next, isFetchingMore, showPlaceholders])

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f9fafb] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดเส้นทางการเรียน...</p>
        </div>
      </div>
    )
  }

  // Calculate statistics
  const completedPathsCount = userPathways.filter((p) => (p.path_processing || 0) === 100).length
  const inProgressPathsCount = userPathways.filter((p) => (p.path_processing || 0) > 0 && (p.path_processing || 0) < 100).length
  const averageHours =
    userPathways.length > 0
      ? Math.round(userPathways.reduce((acc, p) => acc + (p.path_duration || 0), 0) / userPathways.length)
      : 0

  return (
    <div className="min-h-screen bg-[#f9fafb] w-full">
      <div className="flex w-full">
        {/* Main Content */}
        <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
          <div className="w-full p-6">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center mb-2">
                <button
                  onClick={() => router.push("/profile/dashboard")}
                  className="mr-2 p-1 rounded-full hover:bg-gray-200"
                >
                  <ArrowLeft size={20} />
                </button>
                <h1 className="text-2xl font-bold text-gray-800">เส้นทางการเรียนเพื่อรับใบประกาศนียบัตร</h1>
              </div>
              <p className="text-gray-600">เลือกเส้นทางการเรียนที่เหมาะสมกับคุณ เพื่อพัฒนาความรู้และรับใบประกาศนียบัตรในสาขาที่สนใจ</p>
            </div>
            {/* Search */}
            <div className="mb-8 bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="text"
                      placeholder="ค้นหาเส้นทางการเรียน..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#008268] focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {overviewPath ? (
                <>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-[#008268]/10 rounded-lg">
                        <Route className="h-6 w-6 text-[#008268]" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">เส้นทางทั้งหมด</p>
                        <p className="text-2xl font-bold text-gray-900">{overviewPath.total_path}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-green-100 rounded-lg">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">เส้นทางที่เรียนจบ</p>
                        <p className="text-2xl font-bold text-gray-900">{overviewPath.total_path_completed}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <TrendingUp className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">กำลังเรียน</p>
                        <p className="text-2xl font-bold text-gray-900">{overviewPath.total_path_in_progress}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-orange-100 rounded-lg">
                        <Clock className="h-6 w-6 text-orange-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">เวลาเฉลี่ย</p>
                        <p className="text-2xl font-bold text-gray-900">{overviewPath.total_avg_time} ชม.</p>
                      </div>
                    </div>
                  </div>
                </>
              ) : null}
            </div>
            {/* Pathway Cards */}
            {filteredPathways.length === 0 ? (
              <div className="text-center py-16">
                <div className="mb-6">
                  <Target size={64} className="mx-auto text-gray-300" />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">ไม่พบเส้นทางการเรียนที่ตรงกับการค้นหา</h3>
                <p className="text-gray-500 mb-6">ลองปรับเปลี่ยนคำค้นหาเพื่อดูเส้นทางการเรียนอื่นๆ</p>
                <button
                  onClick={() => setSearchTerm("")}
                  className="px-6 py-3 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
                >
                  ล้างการค้นหา
                </button>
              </div>
            ) : (<>
              <LearningPathsCard
                pathways={filteredPathways}
                accordionMode={false}
                defaultExpanded={[]}
                isLoadingMore={showPlaceholders}
                placeholderCount={1}
              />

              {/* Loading States and End Indicators */}
              {pagination.has_next && (
                <div ref={sentinelRef} className="h-4" />
              )}

              {isFetchingMore && !showPlaceholders && (
                <div className="flex flex-col items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#008268] border-t-transparent mb-3"></div>
                  <p className="text-gray-600 text-sm">กำลังโหลดเส้นทางการเรียนเพิ่มเติม...</p>
                </div>
              )}                {hasError && pagination.has_next && !showPlaceholders && (
                <div className="flex flex-col items-center py-8">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-600 text-sm mb-3">เกิดข้อผิดพลาดในการโหลดข้อมูล</p>
                    <button
                      onClick={fetchMore}
                      className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 transition-colors"
                    >
                      ลองใหม่
                    </button>
                  </div>
                </div>
              )}                {!pagination.has_next && filteredPathways.length > 0 && !isFetchingMore && !showPlaceholders && (
                <div className="flex flex-col items-center py-8">
                  <div className="bg-gray-100 rounded-full p-3 mb-3">
                    <CheckCircle className="h-6 w-6 text-gray-500" />
                  </div>
                  <p className="text-gray-500 text-sm">คุณได้ดูเส้นทางการเรียนทั้งหมดแล้ว</p>
                  <p className="text-gray-400 text-xs mt-1">รวม {pagination.total_items} เส้นทางการเรียน</p>
                </div>
              )}
            </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LearningPathsRoute
