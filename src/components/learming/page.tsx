"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { getUserDisplayCourse, GetUserDisplayCourseResponse } from "@/hook/displaycourseService"
import HrawersLearming from "./hrawers"
import LearmingContent from "./content"
import { hasFinalExam } from "@/data/finalExamQuestions"
import { finalExamService, FinalExam as FinalExamType } from "@/hook/finalExamService"
import { CheckCircle, X } from "lucide-react"
import { CourseType } from "@/types/courses"

export default function Learming({ slug }: { slug: string }) {
  const router = useRouter()

  // Utility to parse duration strings
  const parseDurationToSeconds = (duration: string): number => {
    try {
      const parts = duration.split(':').map(Number)
      if (parts.length === 2) {
        const [m, s] = parts
        return m * 60 + (s || 0)
      } else if (parts.length === 3) {
        const [h, m, s] = parts
        return h * 3600 + m * 60 + (s || 0)
      }
    } catch {}
    return 0
  }

  const formatSecondsToTime = (sec: number): string => {
    const h = Math.floor(sec / 3600)
    const m = Math.floor((sec % 3600) / 60)
    const s = sec % 60
    if (h) return `${h}:${m.toString().padStart(2,'0')}:${s.toString().padStart(2,'0')}`
    return `${m}:${s.toString().padStart(2,'0')}`
  }

  const [courses, setCourses] = useState<CourseType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [menuOpen, setMenuOpen] = useState(false)
  const [selectId, setSelectId] = useState<{ lessonId: string; contentId: string }>({ lessonId: "", contentId: "" })
  const [userProgress, setUserProgress] = useState({ completedContents: [] as string[], completedLessons: [] as string[], allLessonsCompleted: false })
  const [userId, setUserId] = useState("")

  const [hasFinalExamState, setHasFinalExamState] = useState(false)
  const [finalExamData, setFinalExamData] = useState<FinalExamType | null>(null)
  const [loadingFinalExam, setLoadingFinalExam] = useState(false)
  const [examLockState, setExamLockState] = useState(true)
  const [showUnlockExamPopup, setShowUnlockExamPopup] = useState(false)
  const [examUnlockPopupShown, setExamUnlockPopupShown] = useState(false)
  const [isContentUpdating, setIsContentUpdating] = useState(false)

  const getStorageKey = (key: string) => `${key}_${userId}`

  // Map API response
  const mapApiResponseToCourseType = (api: GetUserDisplayCourseResponse): CourseType => {
    const totalTime = api.course_lessons.reduce((sum, l) => sum + l.lesson_content.reduce((ts, c) => ts + parseDurationToSeconds(c.content_lesson_duration), 0), 0)
    return {
      id: api.course_slug,
      name: api.course_name,
      description: api.course_description,
      instruction: api.course_description,
      level: "กลาง",
      time: totalTime,
      status: "active",
      certify: api.course_exam.has_exam,
      coverImage: "",
      difficulty: "กลาง",
      title: api.course_name,
      flexibility: { duration: "ตามความสะดวก", description: "เรียนผ่านวิดีโอคอร์ส สามารถเรียนได้ตามสะดวก" },
      teacher: { name: api.course_lecturer.lecturer_name, description: api.course_lecturer.lecturer_position, avatar: "" },
      lesson: api.course_lessons.map((l, li) => ({
        id: `lesson_${li+1}`,
        name: l.lesson_name,
        description: l.lesson_name,
        duration: l.lesson_duration,
        time: l.lesson_content.reduce((ts, c) => ts + parseDurationToSeconds(c.content_lesson_duration), 0),
        is_locked: l.is_locked,
        content: l.lesson_content.map((c) => {
          const isPdf = c.url?.includes('.pdf') || c.url?.endsWith('.txt')
          const isVideo = c.url?.includes('youtube.com') || c.url?.includes('vimeo.com') || c.url?.includes('.mp4')
          const typecontent = isPdf ? 'text' : isVideo ? 'video' : 'text'
          return { id: c.content_slug, name: c.content_lesson_name, typecontent, details: c.url, time: parseDurationToSeconds(c.content_lesson_duration), duration: `${c.content_lesson_duration} นาที`, is_locked: c.is_locked }
        })
      }))
    }
  }

  const loadCourse = async () => {
    setLoading(true)
    try {
      const apiData = await getUserDisplayCourse(slug)
      const course = mapApiResponseToCourseType(apiData)
      setCourses(course)
      setHasFinalExamState(apiData.course_exam.has_exam)
      setExamLockState(apiData.course_exam.is_locked)
      // select first unlocked content
      let first: {lessonId:string;contentId:string} | null = null
      for(const l of course.lesson) {
        if(l.is_locked) continue
        for(const c of l.content) {
          if(!c.is_locked) { first = {lessonId:l.id, contentId:c.id}; break }
        }
        if(first) break
      }
      if(!first && course.lesson[0]?.content[0]) first = {lessonId:course.lesson[0].id,contentId:course.lesson[0].content[0].id}
      first && setSelectId(first)
      // load final exam
      await loadFinalExamData(slug)
      // initialize progress
      initProgress(course, userId)
    } catch(err:any) {
      console.error(err)
      setError(err.message || 'โหลดข้อมูลล้มเหลว')
    } finally {
      setLoading(false)
    }
  }

  const loadFinalExamData = async(courseId:string) => {
    setLoadingFinalExam(true)
    try {
      const list = await finalExamService.getFinalExamsByCourse(courseId)
      if(list.length){ setFinalExamData(list[0]); setHasFinalExamState(true) }
      else { setHasFinalExamState(hasFinalExam(courseId)) }
    } catch(err){ console.warn(err); setHasFinalExamState(hasFinalExam(courseId)) }
    finally{ setLoadingFinalExam(false) }
  }

  const initProgress = (course:CourseType, uid:string) => {
    if(!uid) return
    try{
      const comp = JSON.parse(localStorage.getItem(getStorageKey('completedContents'))||'[]')
      const less = JSON.parse(localStorage.getItem(getStorageKey('completedLessons'))||'[]')
      const all = course.lesson.every(l=> l.content.every(c=> comp.includes(c.id)))
      setUserProgress({completedContents:comp,completedLessons:less,allLessonsCompleted:all})
      const popKey = getStorageKey('examUnlockPopupShown')
      const shown = localStorage.getItem(popKey)==='true'
      setExamUnlockPopupShown(shown)
      if(all && hasFinalExamState && !examLockState && !shown){ setShowUnlockExamPopup(true); localStorage.setItem(popKey,'true') }
    }catch{}
  }

  useEffect(()=>{
    const id = typeof window!=='undefined'? localStorage.getItem('userId') || (()=>{ const nid=`user_${Date.now()}`; localStorage.setItem('userId',nid); return nid })() : ''
    setUserId(id)
  },[])

  useEffect(()=>{ if(userId) loadCourse() },[slug,userId])

  useEffect(()=>{
    const interval = setInterval(async()=>{
      if(slug && userId) await loadCourse()
    },30000)
    return()=> clearInterval(interval)
  },[slug,userId])

  const handleContentComplete = ({contentId,completed}:{contentId:string;completed:boolean})=>{
    if(!completed||!courses||!userId) return
    const isVideo = courses.lesson.find(l=>l.id===selectId.lessonId)?.content.find(c=>c.id===contentId)?.typecontent==='video'
    setUserProgress(prev=>{
      if(prev.completedContents.includes(contentId)) return prev
      const newComp=[...prev.completedContents,contentId]
      const newLess = prev.completedLessons
      const l = courses.lesson.find(l=>l.id===selectId.lessonId)
      if(l && l.content.every(c=> newComp.includes(c.id)) && !newLess.includes(selectId.lessonId)) newLess.push(selectId.lessonId)
      localStorage.setItem(getStorageKey('completedContents'),JSON.stringify(newComp))
      localStorage.setItem(getStorageKey('completedLessons'),JSON.stringify(newLess))
      const all = courses.lesson.every(les=> les.content.every(c=> newComp.includes(c.id)))
      if(all && hasFinalExamState && !examLockState && !examUnlockPopupShown){ setShowUnlockExamPopup(true); setExamUnlockPopupShown(true); localStorage.setItem(getStorageKey('examUnlockPopupShown_'+slug),'true') }
      if(isVideo) setTimeout(()=> window.location.reload(),2000)
      return {completedContents:newComp,completedLessons:newLess,allLessonsCompleted:all}
    })
  }

  const goToNextContent = ()=>{
    if(!courses) return
    const li = courses.lesson.findIndex(l=>l.id===selectId.lessonId)
    if(li<0) return
    const lesson=courses.lesson[li]
    const ci= lesson.content.findIndex(c=>c.id===selectId.contentId)
    // next in same
    for(let i=ci+1;i<lesson.content.length;i++){ if(!lesson.content[i].is_locked){ setSelectId({lessonId:lesson.id,contentId:lesson.content[i].id}); return }}
    // next lesson
    for(let j=li+1;j<courses.lesson.length;j++){
      const nl=courses.lesson[j]
      if(nl.is_locked) continue
      for(const c of nl.content){ if(!c.is_locked){ setSelectId({lessonId:nl.id,contentId:c.id}); return }}
    }
    if(userProgress.allLessonsCompleted && hasFinalExamState && !examLockState) setSelectId({lessonId:'final',contentId:'final-exam'})
  }

  const closeUnlockExamPopup = ()=>{ setShowUnlockExamPopup(false); localStorage.setItem(getStorageKey('examUnlockPopupShown_'+slug),'true') }
  const goToFinalExam = ()=>{ router.push(`/learning/${slug}/final-exam`); closeUnlockExamPopup() }

  if(loading) return <div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div></div>
  if(error) return <div className="flex flex-col items-center justify-center min-h-screen"><h1 className="text-2xl font-bold text-red-600 mb-4">เกิดข้อผิดพลาด</h1><p className="text-gray-600 mb-6">{error}</p><button onClick={()=>window.location.reload()} className="px-6 py-2 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] transition-colors">ลองใหม่</button></div>
  if(!courses) return <div className="flex flex-col items-center justify-center min-h-screen"><h1 className="text-2xl font-bold text-gray-800 mb-4">ไม่พบคอร์สที่คุณต้องการ</h1><p className="text-gray-600 mb-6">คอร์สนี้อาจถูกลบหรือย้ายไปที่อื่น</p><a href="/courses" className="px-6 py-2 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] transition-colors">กลับไปหน้าคอร์ส</a></div>

  // Main render
  return (
    <div className="min-h-screen bg-white">
      {isContentUpdating && <div className="fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-md shadow-lg flex items-center gap-2"><div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div><span className="text-sm">กำลังอัปเดตเนื้อหา...</span></div>}

      <HrawersLearming courses={courses} select={{ selectId, setSelectId }} menuOpen={{ opened: menuOpen, onMenuOpen: setMenuOpen }} userProgress={userProgress} hasFinalExam={hasFinalExamState} examIsLocked={examLockState} userId={userId} />

      <LearmingContent content={courses.lesson.find(l=>l.id===selectId.lessonId)?.content.find(c=>c.id===selectId.contentId) || null} course={courses} lesson={courses.lesson.find(l=>l.id===selectId.lessonId) || null} menuOpen={{ opened: menuOpen, onMenuOpen: setMenuOpen }} selectId={selectId} onComplete={handleContentComplete} goToNextContent={goToNextContent} userId={userId} />

      {showUnlockExamPopup && <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"><div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative"><button onClick={closeUnlockExamPopup} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"><X size={20}/></button><div className="flex flex-col items-center text-center"><div className="w-16 h-16 rounded-full bg-[#E6E9F5] flex items-center justify-center mb-4"><CheckCircle size={32} className="text-[#293D97]"/></div><h3 className="text-xl font-bold text-gray-900 mb-2">ยินดีด้วย! คุณได้ปลดล็อกข้อสอบท้ายบทแล้ว</h3><p className="text-gray-600 mb-6">คุณได้ทำบทเรียนครบทุกบทแล้ว ตอนนี้คุณสามารถทำข้อสอบท้ายบทเพื่อทดสอบความรู้ของคุณได้แล้ว</p><div className="flex gap-4"><button onClick={closeUnlockExamPopup} className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">ทำภายหลัง</button><button onClick={goToFinalExam} className="px-4 py-2 bg-[#293D97] text-white rounded-md hover:bg-opacity-90">ทำข้อสอบตอนนี้</button></div></div></div></div>}

      {/* Debug Panel omitted for brevity in production */}
    </div>
  )
}
