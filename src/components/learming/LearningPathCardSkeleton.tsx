"use client"

import React from "react"

interface LearningPathCardSkeletonProps {
    count?: number
}

const LearningPathCardSkeleton: React.FC<LearningPathCardSkeletonProps> = ({ count = 2 }) => {
    return (
        <>
            {Array.from({ length: count }).map((_, index) => (
                <div
                    key={`skeleton-${index}`}
                    className="mb-6 bg-white rounded-lg shadow-sm border animate-pulse"
                    style={{
                        breakInside: "avoid",
                        pageBreakInside: "avoid",
                    }}
                >
                    {/* Pathway Header Skeleton */}
                    <div className="p-4 border-b border-gray-100">
                        <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        {/* Icon skeleton */}
                                        <div className="w-7 h-7 bg-gray-200 rounded-lg mr-3"></div>
                                        {/* Title skeleton */}
                                        <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
                                    </div>
                                    <div className="flex items-center">
                                        {/* Arrow skeleton */}
                                        <div className="w-4 h-4 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                                {/* Description skeleton */}
                                <div className="space-y-2 mb-3">
                                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                </div>
                                {/* Tags skeleton */}
                                <div className="flex items-center gap-2 mb-3 flex-wrap">
                                    <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                                    <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                                </div>
                            </div>
                            <div className="ml-3">
                                {/* Certificate/Lock button skeleton */}
                                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                            </div>
                        </div>

                        {/* Progress Section Skeleton */}
                        <div className="mb-3">
                            <div className="flex justify-between text-sm mb-2">
                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                                <div className="h-4 bg-gray-200 rounded w-24"></div>
                            </div>
                            {/* Progress bar skeleton */}
                            <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div className="h-full bg-gray-300 rounded-full w-1/3"></div>
                            </div>
                            <div className="flex justify-between items-center mt-2">
                                <div className="h-3 bg-gray-200 rounded w-16"></div>
                                <div className="h-4 bg-gray-200 rounded w-8"></div>
                            </div>
                        </div>
                    </div>
                </div>
            ))}
        </>
    )
}

export default LearningPathCardSkeleton
