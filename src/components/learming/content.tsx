"use client"

import { useEffect, useState, useRef } from "react"
import { Menu, CheckCircle } from "lucide-react"
import VideoWithQuestion from "./video"
import FinalExam from "@/components/learming/final-exam"
import { getFinalExamData } from "@/data/finalExamQuestions" // แก้ไข import path
import { completeContentProgress } from "@/hook/displaycourseService"

interface Course {
  id: string
  title: string
  description: string
  image: string
  lessons: Lesson[]
}

interface Lesson {
  id: string
  title: string
  description: string
  contents: Content[]
}

interface Content {
  id: string
  title: string
  description: string
  type: string
  url: string
}

interface LearmingContentProps {
  content?: any
  menuOpen?: {
    opened: boolean
    onMenuOpen: (menuOpen: boolean) => void
  }
  course?: any
  lesson?: any
  courses?: any
  selectId?: {
    lessonId: string
    contentId: string
  }
  onComplete?: (result: { contentId: string; completed: boolean }) => void
  goToNextContent?: () => void
  userId?: string
}

const LearmingContent = ({
  content,
  menuOpen,
  course,
  lesson,
  courses,
  selectId,
  onComplete,
  goToNextContent,
  userId = "",
}: LearmingContentProps) => {
  const [contentCompleted, setContentCompleted] = useState(false)
  const [showCompletedBadge, setShowCompletedBadge] = useState(false)
  const [readingTime, setReadingTime] = useState(0)
  const [scrolledToBottom, setScrolledToBottom] = useState(false)
  const textContentRef = useRef<HTMLDivElement>(null)

  // ฟังก์ชันสำหรับสร้าง key ที่ใช้ใน localStorage โดยรวม userId
  const getStorageKey = (key: string) => {
    return `${key}_${userId}`
  }

  // ตรวจสอบว่าเป็นข้อสอบท้ายบทหรือไม่
  const isFinalExam = selectId?.lessonId === "final" && selectId?.contentId === "final-exam"

  // ดึงข้อมูลข้อสอบท้ายบทตาม courseId
  const finalExamData = isFinalExam && course?.id ? getFinalExamData(course.id) : null

  // ตรวจสอบว่าเนื้อหานี้ถูกทำเสร็จแล้วหรือยัง
  useEffect(() => {
    if (content?.id && typeof window !== "undefined" && userId) {
      try {
        const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")
        const isCompleted = completedContents.includes(content.id)
        setContentCompleted(isCompleted)
        setShowCompletedBadge(isCompleted)
      } catch (e) {
        console.error("Error reading from localStorage:", e)
      }
    }
  }, [content?.id, userId])

  // บันทึกว่าเนื้อหานี้ถูกทำเสร็จแล้ว
  const markContentAsCompleted = () => {
    if (!content?.id || contentCompleted || !userId) return

    try {
      const completedContents = JSON.parse(localStorage.getItem(getStorageKey("completedContents")) || "[]")
      if (!completedContents.includes(content.id)) {
        completedContents.push(content.id)
        localStorage.setItem(getStorageKey("completedContents"), JSON.stringify(completedContents))
        setContentCompleted(true)
        setShowCompletedBadge(true)

        // แจ้ง parent component ทันทีว่าเนื้อหานี้เสร็จแล้ว
        if (onComplete) {
          console.log("Marking content as completed:", content.id)
          onComplete({ contentId: content.id, completed: true })
        }
      }
    } catch (e) {
      console.error("Error writing to localStorage:", e)
    }
  }

  // ตรวจสอบการเลื่อนลงมาถึงด้านล่างของเนื้อหา (สำหรับ text)
  const handleScroll = () => {
    if (!textContentRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = textContentRef.current
    const isBottom = scrollTop + clientHeight >= scrollHeight - 50 // ใกล้ถึงด้านล่าง 50px

    if (isBottom && !scrolledToBottom) {
      setScrolledToBottom(true)
      // เพิ่มเวลาอ่านเมื่อเลื่อนถึงด้านล่าง
      setReadingTime((prev) => prev + 10)
    }
  }

  // ตรวจสอบเวลาอ่าน
  useEffect(() => {
    let timer: NodeJS.Timeout

    // สำหรับเนื้อหาประเภท text
    if (content?.typecontent === "text" && !contentCompleted) {
      timer = setInterval(() => {
        setReadingTime((prev) => prev + 1)
      }, 1000)
    }

    return () => {
      if (timer) clearInterval(timer)
    }
  }, [content?.typecontent, contentCompleted])

  // มาร์กว่าผ่านเมื่ออ่านนานพอหรือเลื่อนถึงด้านล่าง
  useEffect(() => {
    // สำหรับเนื้อหาประเภท text
    if (content?.typecontent === "text" && !contentCompleted) {
      // ถ้าอ่านนานกว่า 30 วินาที หรือเลื่อนถึงด้านล่าง
      if (readingTime >= 30 || scrolledToBottom) {
        markContentAsCompleted()
      }
    }
  }, [readingTime, scrolledToBottom, content?.typecontent, contentCompleted])

  useEffect(() => {
    console.log("Content Component - Course:", course)
    console.log("Content Component - Lesson:", lesson)
    console.log("Content Component - Content:", content)
    console.log("Is Final Exam:", isFinalExam)
    console.log("Final Exam Data:", finalExamData)
  }, [content, course, lesson, isFinalExam, finalExamData])

  // ฟังก์ชันเมื่อข้อสอบท้ายบทเสร็จสิ้น
  const handleFinalExamComplete = (passed: boolean, score: number) => {
    console.log("Final Exam Completed:", { passed, score })

    // บันทึกผลการสอบลงใน localStorage
    if (typeof window !== "undefined" && course?.id && userId) {
      try {
        const examResults = JSON.parse(localStorage.getItem(getStorageKey("examResults")) || "{}")
        examResults[course.id] = { passed, score, date: new Date().toISOString() }
        localStorage.setItem(getStorageKey("examResults"), JSON.stringify(examResults))
      } catch (e) {
        console.error("Error saving exam results:", e)
      }
    }

    // แจ้งว่าเสร็จสิ้นการทำข้อสอบ
    if (onComplete) {
      onComplete({ contentId: "final-exam", completed: true })
    }
  }

  // ถ้าเป็นข้อสอบท้ายบทและมีข้อมูลข้อสอบ
  if (isFinalExam && finalExamData) {
    // ตรวจสอบว่าข้อสอบถูกล็อคหรือไม่
    if (content?.is_locked) {
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-50">
          <div className="text-center p-8">
            <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mb-4 mx-auto">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m4-6V7a2 2 0 10-4 0v4m4 0a2 2 0 11-4 0m4 0h2a2 2 0 100-4h-2m-2 0V7a2 2 0 114 0v4" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">ข้อสอบท้ายบทถูกล็อค</h2>
            <p className="text-gray-600 mb-4">
              ข้อสอบท้ายบทยังไม่พร้อมใช้งาน 
            </p>
          </div>
          
          {/* Menu Button */}
          <div
            className={`fixed top-3 left-4 z-20 bg-white w-10 h-10 flex items-center justify-center rounded-full shadow-lg cursor-pointer transition-transform ${menuOpen?.opened ? "rotate-180" : ""}`}
            onClick={() => menuOpen?.onMenuOpen(!menuOpen.opened)}
          >
            <Menu size={20} className="text-gray-700" />
          </div>
        </div>
      )
    }

    // แปลงข้อมูลให้ตรงกับ Type ที่ FinalExam component ต้องการ
    const transformedExamData = {
      id: finalExamData.id,
      name: finalExamData.name,
      description: finalExamData.description,
      questions: finalExamData.questions
        ? finalExamData.questions.map((q) => ({
          id: q.id,
          title: q.title,
          type: q.type,
          content: q.content || "", // แปลง undefined เป็น empty string
          imageUrl: q.imageUrl,
          choices: q.choices
            ? q.choices.map((c) => ({
              id: c.id,
              content: c.content,
              type: c.type,
              isCorrect: c.isCorrect,
              imageUrl: c.imageUrl,
            }))
            : [],
        }))
        : [],
      passingScore: finalExamData.passingScore,
      timeLimit: finalExamData.timeLimit,
    }

    return (
      <div className="w-full h-full">
        <FinalExam examData={transformedExamData} onComplete={handleFinalExamComplete} />

        {/* Menu Button */}
        <div
          className={`fixed top-3 left-4 z-20 bg-white w-10 h-10 flex items-center justify-center rounded-full shadow-lg cursor-pointer transition-transform ${menuOpen?.opened ? "rotate-180" : ""}`}
          onClick={() => menuOpen?.onMenuOpen(!menuOpen.opened)}
        >
          <Menu size={20} className="text-gray-700" />
        </div>
      </div>
    )
  }

  useEffect(() => {
    console.log("Content Component - Course:", course)
  }, [course])

  const renderContent = () => {
    if (!content) return null

    console.log("Rendering content:", content)
    console.log("Content type:", content.typecontent)

    switch (content.typecontent) {
      case "video":
        console.log("Rendering video with props:", {
          videoUrl: content.details,
          courseId: course?.id,
          lessonId: lesson?.id,
          contentId: content.id,
        })

        // ส่งข้อมูลคอร์ส บทเรียน และเนื้อหาไปให้ VideoWithQuestion
        return (
          <VideoWithQuestion
            videoUrl={content.details}
            contentSlug={content.id} // ส่ง slug เพื่อดึงข้อมูลควิซ
            onComplete={async (result) => {
              console.log("Content completed:", result)
              // ส่งข้อมูลไปยัง parent component หรือเก็บในฐานข้อมูล
              await completeContentProgress({
                content_lesson_slug: content.id,
              }).then(() => {
                setContentCompleted(true)
                setShowCompletedBadge(true)
                
                // Refresh the page immediately after video completion
                if (typeof window !== "undefined") {
                  window.location.reload()
                }
              })

              if (onComplete) {
                onComplete(result)
              }
            }}
          />
        )
      case "pdf":
        return (
          <div className="relative">
            {/* แสดงสถานะการผ่าน */}
            {/* ซ่อนปุ่ม "ผ่านแล้ว" ตามคำขอ */}

            <iframe src={`/e-med/${content.details}`} className="hidden md:flex w-full h-screen" />
            <div className="md:hidden flex flex-col items-center bg-white justify-center min-h-screen p-6">
              <a
                href={`/e-med/${content.details}`}
                target="_blank"
                className="btn text-white bg-[#2FBCC1] border-0 flex items-center gap-2 px-6 py-3 mb-6"
                rel="noreferrer"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Download PDF
              </a>

              {!contentCompleted && (
                <button
                  onClick={markContentAsCompleted}
                  className="px-6 py-3 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] flex items-center"
                >
                  <CheckCircle size={18} className="mr-2" />
                  ฉันได้อ่านแล้ว
                </button>
              )}
            </div>

            {/* ปุ่มมาร์กว่าผ่านสำหรับ PDF บนเดสก์ท็อป */}
            {!contentCompleted && (
              <div className="fixed bottom-6 right-6 z-10">
                <button
                  onClick={markContentAsCompleted}
                  className="px-6 py-3 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] shadow-lg flex items-center"
                >
                  <CheckCircle size={18} className="mr-2" />
                  ฉันได้อ่านแล้ว
                </button>
              </div>
            )}
          </div>
        )
      case "text":
        console.log("Rendering text content with props:", {
          contentDetails: content.details,
          isPdf: content.details?.includes('.pdf')
        });

        // ตรวจสอบว่า details เป็น URL ของ PDF หรือไม่
        const isPdfUrl = content.details?.includes('.pdf');

        // ถ้าเป็น PDF URL ให้แสดงด้วย iframe
        if (isPdfUrl) {
          return (
            <div className="relative">
              <iframe
                src={content.details}
                className="hidden md:flex w-full h-screen"
                title={content.name}
              />
              <div className="md:hidden flex flex-col items-center bg-white justify-center min-h-screen p-6">
                <a
                  href={content.details}
                  target="_blank"
                  className="btn text-white bg-[#2FBCC1] border-0 flex items-center gap-2 px-6 py-3 mb-6"
                  rel="noreferrer"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  Download PDF
                </a>

                {!contentCompleted && (
                  <button
                    onClick={markContentAsCompleted}
                    className="px-6 py-3 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] flex items-center"
                  >
                    <CheckCircle size={18} className="mr-2" />
                    ฉันได้อ่านแล้ว
                  </button>
                )}
              </div>

              {/* ปุ่มมาร์กว่าผ่านสำหรับ PDF บนเดสก์ท็อป */}
              {!contentCompleted && (
                <div className="fixed bottom-6 right-6 z-10">
                  <button
                    onClick={markContentAsCompleted}
                    className="px-6 py-3 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] shadow-lg flex items-center"
                  >
                    <CheckCircle size={18} className="mr-2" />
                    ฉันได้อ่านแล้ว
                  </button>
                </div>
              )}
            </div>
          );
        }

        // ถ้าไม่ใช่ PDF ให้แสดงเป็นข้อความธรรมดา
        return (
          <div className="relative h-full">
            {/* แสดงสถานะการผ่าน */}
            <div ref={textContentRef} className="h-full overflow-y-auto bg-white p-6" onScroll={handleScroll}>
              <h1 className="text-2xl font-bold mb-4">{content.name}</h1>
              <div className="prose max-w-none">
                {/* ลองตรวจสอบว่าเป็น HTML หรือไม่ */}
                {content.details && content.details.includes('<') && content.details.includes('>') ? (
                  <div dangerouslySetInnerHTML={{ __html: content.details }} />
                ) : (
                  <p className="whitespace-pre-wrap">{content.details}</p>
                )}
              </div>

              {/* ปุ่มมาร์กว่าผ่านสำหรับเนื้อหาประเภท text */}
              {!contentCompleted && (
                <div className="mt-8 flex justify-center">
                  <button
                    onClick={markContentAsCompleted}
                    className="px-6 py-3 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58] flex items-center"
                  >
                    <CheckCircle size={18} className="mr-2" />
                    ฉันได้อ่านแล้ว
                  </button>
                </div>
              )}
            </div>
          </div>
        )
      default:
        return <div>ไม่รองรับประเภทเนื้อหานี้: {content.typecontent}</div>
    }
  }

  return (
    <div>
      {renderContent()}
      {/* Menu Button */}
      <div
        className={`fixed top-3 left-4 z-20 bg-white w-10 h-10 flex items-center justify-center rounded-full shadow-lg cursor-pointer transition-transform ${menuOpen?.opened ? "rotate-180" : ""}`}
        onClick={() => menuOpen?.onMenuOpen(!menuOpen.opened)}
      >
        {/* Animated Hamburger Icon */}
        <div className={`transition-transform ${menuOpen?.opened ? "open" : ""}`}>
          <Menu size={20} className="text-gray-700" />
        </div>
      </div>
    </div>
  )
}

export default LearmingContent
