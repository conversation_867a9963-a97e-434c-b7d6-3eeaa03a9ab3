"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { BookO<PERSON>, Clock, Route, Award, FileText } from "lucide-react"
import { learningPathsData } from "@/data/learningPaths"
import CertificateGenerator from "@/components/learming/CourseCertificate"
import { getMedicalUsersData } from "@/data/allUsers"

interface CourseCardProps {
  id: string
  name: string
  description?: string
  teacherName?: string
  coverImage: string
  progress: number
  completedLessons: number
  totalLessons: number
  duration: number
  status: "completed" | "in_progress" | "not_started"
  level?: string
  certify?: boolean
}

const getCoursePaths = (courseId: string) => {
  try {
    if (!learningPathsData || !Array.isArray(learningPathsData)) {
      return []
    }
    return learningPathsData.filter((path) => {
      if (!path || !path.courseIds || !Array.isArray(path.courseIds)) {
        return false
      }
      return path.courseIds.includes(courseId)
    })
  } catch (error) {
    console.error("Error getting course paths:", error)
    return []
  }
}

const getProgressColor = (progress: number): string => {
  if (progress < 30) return "bg-red-500"
  if (progress < 70) return "bg-orange-500"
  return "bg-green-500"
}

const translateStatus = (status: string): string => {
  switch (status) {
    case "completed":
      return "เรียนจบแล้ว"
    case "in_progress":
      return "กำลังเรียน"
    case "not_started":
      return "ยังไม่ได้เริ่ม"
    default:
      return status
  }
}

const getStatusBadgeColor = (status: string): string => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800"
    case "in_progress":
      return "bg-blue-100 text-blue-800"
    case "not_started":
      return "bg-gray-100 text-gray-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const ProgressCard: React.FC<CourseCardProps> = ({
  id,
  name,
  description,
  teacherName,
  coverImage,
  progress,
  completedLessons,
  totalLessons,
  duration,
  status,
  level = "เบื้องต้น",
  certify = false,
}) => {
  const [showCertificate, setShowCertificate] = useState(false)

  const getCurrentUser = () => {
    const userId = localStorage.getItem("userId")
    if (!userId) return null
    return getMedicalUsersData.find((user) => user.id === userId)
  }

  const handleViewCertificate = () => {
    setShowCertificate(true)
  }

  const user = getCurrentUser()
  const userName = user ? `${user.firstname} ${user.lastname}` : "ผู้ใช้"

  const durationHours = Math.floor(duration / 3600)
  const progressColor = getProgressColor(progress)
  const statusBadgeColor = getStatusBadgeColor(status)
  const coursePaths = getCoursePaths(id)

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm flex flex-col h-auto min-h-[350px] overflow-hidden">
        <div className="relative h-36 min-h-[9rem]">
          <Image
            src={
              coverImage ||
              `/placeholder.svg?height=144&width=384&query=medical+course+${encodeURIComponent(name) || "/placeholder.svg"}`
            }
            alt={name}
            fill
            className="object-cover"
            onError={(e) => {
              console.error(`Image failed to load for course ${id}:`, {
                src: coverImage,
                error: e
              })
            }}
            onLoad={() => {
              if (process.env.NODE_ENV !== "production") {
                console.log(`Image loaded successfully for course ${id}:`, coverImage?.substring(0, 100))
              }
            }}
          />
          <div className="absolute top-2 right-2">
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadgeColor}`}>
              {translateStatus(status)}
            </span>
          </div>
        </div>

        <div className="p-4 flex flex-col flex-grow overflow-hidden">
          <h3 className="font-bold text-gray-800 line-clamp-1 text-sm md:text-base leading-tight mb-2 flex items-center gap-2">
            {name}
            {certify && <Award size={16} className="text-yellow-500 flex-shrink-0" />}
          </h3>

          {coursePaths.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {coursePaths.slice(0, 2).map((path) => (
                <span
                  key={path.id}
                  className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded-full border border-blue-200"
                >
                  {path.name}
                </span>
              ))}
              {coursePaths.length > 2 && (
                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full border border-gray-200">
                  +{coursePaths.length - 2} <Route size={14} className="inline-block" />
                </span>
              )}
            </div>
          )}

          {description && <p className="text-gray-600 text-xs mb-3 line-clamp-2 leading-relaxed">{description}</p>}

          <div className="mb-3">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>ความก้าวหน้า</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full ${progressColor} rounded-full transition-all duration-300`}
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          <div className="text-xs text-gray-500 mb-4 flex items-center justify-between">
            <span className="whitespace-normal">
              {completedLessons} / {totalLessons} <BookOpen size={14} className="inline-block mr-1" />
            </span>

            <div className="flex items-center gap-1">
              <div className="flex gap-0.5">
                {["เบื้องต้น", "ปานกลาง", "ยาก"].map((l, i) => {
                  let circleColor = "bg-gray-200"
                  if (level === "เบื้องต้น") {
                    if (i === 0) circleColor = "bg-green-500"
                  } else if (level === "ปานกลาง") {
                    if (i === 0) circleColor = "bg-[#ffce66]"
                    if (i === 1) circleColor = "bg-[#FFB211]"
                  } else if (level === "ยาก") {
                    if (i === 0) circleColor = "bg-red-200"
                    if (i === 1) circleColor = "bg-red-300"
                    if (i === 2) circleColor = "bg-red-500"
                  }
                  return <div key={l} className={`w-2 h-2 rounded-full ${circleColor}`} />
                })}
              </div>
              <span className="text-xs text-gray-600 ml-1">{level}</span>
            </div>

            <span className="flex items-center ml-1">
              <Clock size={12} className="mr-1 flex-shrink-0" />
              {durationHours} ชั่วโมง
            </span>
          </div>

          <div className="mt-auto">
            {status === "completed" ? (
              <div className="flex gap-2">
                <Link
                  href={`/courses/${id}`}
                  className="flex-[3] text-center bg-[#008067] text-white py-2 rounded-md text-sm hover:bg-[#006e58] transition-colors"
                >
                  ทบทวนบทเรียน
                </Link>
                {certify && (
                  <button
                    onClick={handleViewCertificate}
                    className="flex-[1] flex items-center justify-center bg-[#5A69AF] text-white py-2 rounded-md text-sm hover:bg-[#4A5A9F] transition-colors"
                  >
                    <FileText size={16} />
                  </button>
                )}
              </div>
            ) : (
              <Link
                href={`/courses/${id}`}
                className="block w-full text-center bg-[#5A69AF] text-white py-2 rounded-md text-sm hover:bg-[#4A5A9F] transition-colors"
              >
                เรียนต่อ
              </Link>
            )}
          </div>
        </div>
      </div>

      {showCertificate && (
        <CertificateGenerator
          userName={userName}
          courseName={name}
          completionDate={new Date()}
          isOpen={showCertificate}
          onClose={() => setShowCertificate(false)}
        />
      )}
    </>
  )
}

export default ProgressCard
