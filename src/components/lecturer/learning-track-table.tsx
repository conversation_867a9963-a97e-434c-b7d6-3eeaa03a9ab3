"use client"

import React, { useEffect, useState } from "react"
import { ChevronLeft, ChevronRight, Search, MoreHorizontal, ChevronsUpDown } from "lucide-react"
import { useRouter } from "next/navigation"
import { lecturerService, TrackLearningRow, TrackLearningResponse } from "@/hook/lecturerService"

interface LearningTrackTableRow {
  id: string
  courseName: string
  pathNames: string[]
  assignedCount: number
  courseStatus: boolean
  courseSlug: string
}

export default function LearningTrackTable() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("")
  const [rows, setRows] = useState<LearningTrackTableRow[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 10,
    has_next: false,
    has_prev: false
  })

  const fetchTrackLearningData = async (page: number = 1, search: string = "") => {
    try {
      setLoading(true)
      setError(null)

      const response = await lecturerService.getTrackLearning(page, search)

      // Transform API data to match component interface
      const transformedRows: LearningTrackTableRow[] = response.data.map((item, index) => ({
        id: item.course_slug || `course-${index}`,
        courseName: item.course_name,
        pathNames: item.path_names || [],
        assignedCount: item.assigned_count,
        courseStatus: item.course_status,
        courseSlug: item.course_slug
      }))

      setRows(transformedRows)
      setPagination(response.pagination)
      setCurrentPage(page)
    } catch (error) {
      console.error('Error fetching track learning data:', error)
      setError('ไม่สามารถโหลดข้อมูลได้ กรุณาลองใหม่อีกครั้ง')
      setRows([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTrackLearningData(1, searchQuery)
  }, [])

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchTrackLearningData(1, searchQuery)
      } else {
        setCurrentPage(1)
        fetchTrackLearningData(1, searchQuery)
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.total_pages) {
      fetchTrackLearningData(newPage, searchQuery)
    }
  }

  const filteredRows = rows.filter(
    (row) =>
      row.courseName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      row.pathNames.some((p: string) => p.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // ฟังก์ชันเรียงลำดับ
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // ฟังก์ชัน sort จริง
  const sortedRows = [...filteredRows].sort((a, b) => {
    if (!sortField) return 0
    if (sortField === "assignedCount") {
      return sortDirection === "asc" ? a.assignedCount - b.assignedCount : b.assignedCount - a.assignedCount
    }
    if (sortField === "courseStatus") {
      // true > false
      return sortDirection === "asc"
        ? Number(a.courseStatus) - Number(b.courseStatus)
        : Number(b.courseStatus) - Number(a.courseStatus)
    }
    if (sortField === "courseName") {
      return sortDirection === "asc"
        ? a.courseName.localeCompare(b.courseName)
        : b.courseName.localeCompare(a.courseName)
    }
    return 0
  })

  return (
    <div className="w-full">
      {/* ส่วนค้นหาและปุ่มเพิ่ม (style เดิม) */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหาคอร์สหรือเส้นทาง..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>
      </div>
      {/* ตารางแบบใหม่ */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ชื่อคอร์ส</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">เส้นทาง</th>
              <th
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
                onClick={() => handleSort("assignedCount")}
              >
                <div className="flex items-center justify-center gap-1">
                  จำนวนนักเรียนที่ถูก assign
                  <ChevronsUpDown className={`h-4 w-4 ${sortField === "assignedCount" ? "text-[#008268]" : "text-gray-400"}`} />
                </div>
              </th>
              <th
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
                onClick={() => handleSort("courseStatus")}
              >
                <div className="flex items-center justify-center gap-1">
                  สถานะคอร์ส
                  <ChevronsUpDown className={`h-4 w-4 ${sortField === "courseStatus" ? "text-[#008268]" : "text-gray-400"}`} />
                </div>
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr><td colSpan={5} className="text-center py-8 text-gray-500">กำลังโหลด...</td></tr>
            ) : error ? (
              <tr><td colSpan={5} className="text-center py-8 text-red-500">{error}</td></tr>
            ) : sortedRows.length === 0 ? (
              <tr><td colSpan={5} className="text-center py-8 text-gray-500">ไม่พบข้อมูล</td></tr>
            ) : (
              sortedRows.map((row) => (
                <tr
                  key={row.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    router.push(`/lecturer/track-learning/view/${row.courseSlug || row.id}`)
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-gray-900 font-medium">{row.courseName}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-gray-900">
                    <div className="flex flex-wrap gap-1">
                      {row.pathNames.map((p: string, idx: number) => (
                        <span key={`${p}-${idx}`} className="bg-[#e6f4f1] text-[#008268] px-2 py-0.5 rounded-full text-xs font-medium border border-[#b2dfdb]">{p}</span>
                      ))}
                      {row.pathNames.length === 0 && (
                        <span className="text-gray-400 text-xs">ไม่ได้อยู่ในเส้นทางใดๆ</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center text-gray-900">{row.assignedCount}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span
                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${row.courseStatus ? "bg-green-100 text-green-800" : "bg-red-100 text-red-700"}`}
                    >
                      {row.courseStatus ? "เผยแพร่" : "ไม่เผยแพร่"}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                    <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-700" />
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
        <div className="text-sm text-gray-500">
          แสดง {Math.min(pagination.items_per_page, pagination.total_items)} จาก {pagination.total_items} รายการ
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            หน้า {pagination.current_page} จาก {pagination.total_pages}
          </span>
          <div className="flex gap-1">
            <button
              onClick={() => handlePageChange(pagination.current_page - 1)}
              disabled={!pagination.has_prev || loading}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => handlePageChange(pagination.current_page + 1)}
              disabled={!pagination.has_next || loading}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
