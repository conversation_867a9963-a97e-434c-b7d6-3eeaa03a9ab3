"use client"

import React, { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, ArrowLeft, ChevronLeft, ChevronRight, BookOpen } from "lucide-react"
import { examTrackDataByExamId } from "@/data/exam-track-data"
import { getFinalExamById } from "@/data/finalExamQuestions"
import { getStudentExamAnswers } from "@/data/mockExamAnswers"

interface ExamResultProps {
  examId: string
  studentId: string
  attemptNumber?: number // Optional attempt number to view specific attempt
  onBack?: () => void
}

const ExamResult: React.FC<ExamResultProps> = ({ examId, studentId, attemptNumber, onBack }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0)

  // Get exam data
  const examTrackData = examTrackDataByExamId[examId]
  const examData = getFinalExamById(examId)
  const student = examTrackData?.students.find(s => s.id === studentId)

  if (!examTrackData || !examData || !student || !attemptNumber) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-8 text-center">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-800 mb-2">ไม่พบข้อมูลการสอบ</h2>
          <p className="text-gray-600 mb-6">ไม่สามารถค้นหาข้อมูลการสอบของนักเรียนคนนี้ได้</p>
          <button
            onClick={onBack}
            className="px-6 py-3 bg-[#008268] text-white rounded-xl hover:bg-[#006854] transition-colors duration-200"
          >
            กลับไปหน้าก่อนหน้า
          </button>
        </div>
      </div>
    )
  }

  // Show review mode directly
  const currentQuestionData = examData.questions[currentQuestion]
  const studentAnswers = getStudentExamAnswers(examId, studentId, attemptNumber)
  const userAnswers = studentAnswers[currentQuestionData.id] || []

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="relative bg-gradient-to-r from-[#008268] via-[#00a685] to-[#00b894] rounded-3xl p-6 sm:p-8 mb-8 text-white overflow-hidden shadow-2xl">
          <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2">ทบทวนคำตอบ</h1>
                <p className="text-base sm:text-lg opacity-90 font-medium">{examData.name}</p>
                <p className="text-sm sm:text-base opacity-80 mt-1">{student.name} - ครั้งที่ {attemptNumber}</p>
              </div>
              <button
                onClick={onBack}
                className="bg-white/20 backdrop-blur-sm px-6 py-3 rounded-2xl text-white hover:bg-white/30 transition-all duration-200 border border-white/30"
              >
                <ArrowLeft className="w-5 h-5 inline mr-2" />
                ปิด
              </button>
            </div>
          </div>
        </div>

        {/* Question navigation */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 mb-6">
          <div className="overflow-x-auto">
            <div className="flex gap-2 min-w-max pb-2">
              {examData.questions.map((q, index) => {
                const qUserAnswers = studentAnswers[q.id] || []
                const qCorrectAnswers = q.choices.filter((c) => c.isCorrect).map((c) => c.id)
                const isCorrect =
                  qUserAnswers.length === qCorrectAnswers.length &&
                  qUserAnswers.every((id) => qCorrectAnswers.includes(id))

                return (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestion(index)}
                    className={`flex-shrink-0 w-12 h-12 rounded-2xl flex items-center justify-center font-semibold transition-all duration-200 ${
                      currentQuestion === index
                        ? "bg-[#008268] text-white shadow-lg scale-110"
                        : isCorrect
                          ? "bg-green-100 text-green-700 border-2 border-green-500 hover:bg-green-200"
                          : "bg-red-100 text-red-700 border-2 border-red-500 hover:bg-red-200"
                    }`}
                  >
                    {index + 1}
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mb-6">
          <button
            onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
            disabled={currentQuestion === 0}
            className="px-6 py-3 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white transition-all duration-200"
          >
            <ChevronLeft className="w-5 h-5 inline mr-2" />
            ก่อนหน้า
          </button>
          <button
            onClick={() => setCurrentQuestion(Math.min(examData.questions.length - 1, currentQuestion + 1))}
            disabled={currentQuestion === examData.questions.length - 1}
            className="px-6 py-3 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white transition-all duration-200"
          >
            ถัดไป
            <ChevronRight className="w-5 h-5 inline ml-2" />
          </button>
        </div>

        {/* Current question */}
        <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 mb-6">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-3 bg-gradient-to-br from-[#008268] to-[#00a685] rounded-2xl">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">ข้อที่ {currentQuestion + 1}</h2>
              <p className="text-gray-600">คำถามและคำตอบของนักเรียน</p>
            </div>
          </div>

          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">{currentQuestionData.content}</h3>
            {currentQuestionData.imageUrl && (
              <img
                src={currentQuestionData.imageUrl}
                alt="Question"
                className="max-w-full h-auto mb-6 rounded-xl shadow-md"
              />
            )}
          </div>

          <div className="space-y-4">
            {currentQuestionData.choices.map((choice) => {
              const isUserAnswer = userAnswers.includes(choice.id)
              const isCorrect = choice.isCorrect
              const showAsCorrect = isCorrect
              const showAsWrong = isUserAnswer && !isCorrect

              return (
                <div
                  key={choice.id}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 ${
                    showAsCorrect
                      ? "bg-green-50 border-green-500 shadow-md"
                      : showAsWrong
                        ? "bg-red-50 border-red-500 shadow-md"
                        : "bg-gray-50 border-gray-200"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 flex-1">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold ${
                          showAsCorrect
                            ? "bg-green-500"
                            : showAsWrong
                              ? "bg-red-500"
                              : "bg-gray-400"
                        }`}
                      >
                        {choice.id}
                      </div>
                      <div className="flex-1">
                        {choice.imageUrl ? (
                          <div className="flex items-center gap-4">
                            <img
                              src={choice.imageUrl}
                              alt={`Choice ${choice.id}`}
                              className="h-24 object-contain rounded-xl"
                            />
                            {choice.content && <span>{choice.content}</span>}
                          </div>
                        ) : (
                          <span>{choice.content}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {isUserAnswer && (
                        <div className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700">
                          คำตอบของนักเรียน
                        </div>
                      )}
                      {isCorrect && (
                        <CheckCircle className="w-6 h-6 text-green-500" />
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ExamResult
