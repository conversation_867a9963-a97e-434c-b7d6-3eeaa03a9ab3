"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Bell, LogOut, ChevronDown, User, Settings, HelpCircle, AlignJustify } from "lucide-react"
import { authAPI, authHelpers, UserMeResponse } from "@/hook/auth"
import { useRouter } from "next/navigation"

interface NavbarLTRProps {
  isCollapsed: boolean
  toggleMobileSidebar?: () => void
  isMobileOpen?: boolean
}

export default function NavbarLTR({ isCollapsed, toggleMobileSidebar, isMobileOpen }: NavbarLTRProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false)
  const [user, setUser] = useState<UserMeResponse | null>(null)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const notificationRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false)
      }
    }
    authAPI.getCurrentUser().then((user) => {
      if (user) setUser(user)
    })
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const notifications = [
    { id: 1, title: "มีผู้ใช้ใหม่ลงทะเบียน", time: "5 นาทีที่แล้ว", read: false },
    { id: 2, title: "มีการลงทะเบียนคอร์สใหม่", time: "1 ชั่วโมงที่แล้ว", read: false },
    { id: 3, title: "อัพเดทระบบเสร็จสมบูรณ์", time: "1 วันที่แล้ว", read: true },
  ]

  const toggleUserDropdown = () => {
    setIsUserDropdownOpen(!isUserDropdownOpen)
  }

  return (
    <>
      {/* Mobile Navigation Bar - Only visible on mobile */}
      <div className="sm:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center justify-between px-4 py-3">
          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileSidebar}
            className="inline-flex items-center p-2 text-[#008067] rounded-lg hover:bg-[#008067]/10 focus:outline-none focus:ring-2 focus:ring-[#008067] transition-colors"
          >
            <span className="sr-only">Open sidebar</span>
            <AlignJustify className="w-6 h-6" />
          </button>
          
          {/* User Dropdown Button */}
          <div className="relative">
            <button
              onClick={toggleUserDropdown}
              className="flex items-center p-2 text-[#008067] rounded-lg hover:bg-[#008067]/10 focus:outline-none focus:ring-2 focus:ring-[#008067] transition-colors"
            >
              {user?.user_picture ? (
                <Image
                  src={user.user_picture}
                  alt={`${user.user_fname} ${user.user_lname}`}
                  width={32}
                  height={32}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <div className="w-8 h-8 bg-[#008067] rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {`${user?.user_fname?.at(0) ?? ''}${user?.user_lname?.at(0) ?? ''}`}
                  </span>
                </div>
              )}
            </button>
            
            {/* User Dropdown Menu */}
            {isUserDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="font-medium text-gray-800">{`${user?.user_fname ?? ''} ${user?.user_lname ?? ''}`}</p>
                  <p className="text-sm text-gray-500">{user?.user_email}</p>
                </div>
                <Link href="/lecturer/profile" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onClick={() => setIsUserDropdownOpen(false)}>
                  <User className="w-4 h-4 mr-3" />
                  โปรไฟล์
                </Link>
                <Link href="/lecturer/settings" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onClick={() => setIsUserDropdownOpen(false)}>
                  <Settings className="w-4 h-4 mr-3" />
                  ตั้งค่า
                </Link>
                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <HelpCircle className="w-4 h-4 mr-3" />
                  ช่วยเหลือ
                </button>
                <hr className="my-1" />
                <button
                  onClick={() => {
                    setIsUserDropdownOpen(false)
                    authHelpers.logout()
                    router.push("/login")
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4 mr-3" />
                  ออกจากระบบ
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 sm:hidden"
          onClick={toggleMobileSidebar}
        />
      )}

      {/* Desktop Navbar - Hidden on mobile */}
      <header
        className={`hidden sm:block fixed top-0 right-0 left-0 md:left-${isCollapsed ? "20" : "64"} h-[7.2vh] bg-white shadow-md z-30 transition-all duration-300 ease-in-out`}
        style={{ left: 0, [isCollapsed ? "paddingLeft" : "paddingLeft"]: isCollapsed ? "5rem" : "16rem" }}
      >
      <div className="flex items-center justify-between h-full px-4 md:px-6">
        <div className="flex items-center">
          {/* สามารถเพิ่ม logo หรือชื่อระบบฝั่งซ้ายได้ */}
        </div>
        <div className="flex items-center space-x-1">
          {/* Notifications */}
          <div className="relative" ref={notificationRef}>
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <h3 className="font-medium text-gray-800">การแจ้งเตือน</h3>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`px-4 py-3 hover:bg-gray-50 border-l-2 ${notification.read ? "border-transparent" : "border-[#008067]"}`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className={`text-sm ${notification.read ? "text-gray-600" : "text-gray-800 font-medium"}`}>
                            {notification.title}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                        </div>
                        {!notification.read && <span className="w-2 h-2 bg-[#008067] rounded-full"></span>}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="px-4 py-2 border-t border-gray-100 text-center">
                  <button className="text-sm text-[#008067] hover:underline">ดูการแจ้งเตือนทั้งหมด</button>
                </div>
              </div>
            )}
          </div>
          {/* User Menu */}
          <div className="relative" ref={userMenuRef}>
            <button
              className="flex items-center space-x-1 p-1.5  rounded-md transition-colors"
              onClick={() => setShowUserMenu(!showUserMenu)}
            >
              {
                user?.user_picture ? (
                  <Image
                    src={user.user_picture}
                    alt={`${user.user_fname} ${user.user_lname}`}
                    width={32}
                    height={32}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#008067] to-[#00997a] flex items-center justify-center text-white">
                    <span className="text-sm font-medium">{`${user?.user_fname?.at(0) ?? ''}${user?.user_lname?.at(0) ?? ''}`}</span>
                  </div>
                )
              }
              <ChevronDown size={16} className="text-gray-900 h-5 w-5" />
            </button>
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="font-medium text-gray-800">{`${user?.user_fname ?? ''} ${user?.user_lname ?? ''}`}</p>
                  <p className="text-sm text-gray-500">{user?.user_email}</p>
                </div>
                <div className="py-1">
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <User size={16} className="text-gray-900" />
                    <span>โปรไฟล์</span>
                  </button>
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Settings size={16} className="text-gray-900" />
                    <span>ตั้งค่า</span>
                  </button>
                  <button className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <HelpCircle size={16} className="text-gray-900" />
                    <span>ช่วยเหลือ</span>
                  </button>
                </div>
                <div className="border-t border-gray-100 pt-1 mt-1">
                  <button
                    className="flex items-center space-x-3 w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-50"
                    onClick={() => {
                      authHelpers.logout()
                      router.push("/login")
                      setShowUserMenu(false)
                    }}
                  >
                    <LogOut size={16} className="text-gray-900" />
                    <span>ออกจากระบบ</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
    </>
  )
}
