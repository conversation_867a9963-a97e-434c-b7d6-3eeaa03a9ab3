"use client"

import React, { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, Search } from "lucide-react"
import { useRouter } from "next/navigation"
import { lecturerExamService, ExamOverview, ExamStatsPagination } from "@/hook/lecturerService"

const PAGE_SIZE = 5

export default function ExamTrackTable() {
  const [search, setSearch] = useState("")
  const [page, setPage] = useState(1)
  const [exams, setExams] = useState<ExamOverview[]>([])
  const [pagination, setPagination] = useState<ExamStatsPagination | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    let ignore = false
    setLoading(true)
    setError(null)
    lecturerExamService.getExamsStats(page, PAGE_SIZE, search)
      .then(res => {
        if (!ignore) {
          setExams(res.exams)
          setPagination(res.pagination)
        }
      })
      .catch(err => {
        if (!ignore) setError("เกิดข้อผิดพลาดในการโหลดข้อมูล")
      })
      .finally(() => { if (!ignore) setLoading(false) })
    return () => { ignore = true }
  }, [page, search])

  // Reset page if search changes
  useEffect(() => { setPage(1) }, [search])

  // Handle exam selection - navigate to detail page
  const handleExamSelect = (exam: ExamOverview) => {
    router.push(`/lecturer/exam-stats/view/${exam.exam_slug}`)
  }

  return (
    <div className="w-full">
      {/* Search bar styled */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหาชุดข้อสอบหรือคอร์ส..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>
      </div>
      {/* ตารางข้อสอบ */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ชื่อชุดข้อสอบ</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">คอร์ส</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">จำนวนนักเรียนที่ทำ</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">สถานะ</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading && (
              <tr><td colSpan={5} className="text-center py-8 text-gray-400">กำลังโหลด...</td></tr>
            )}
            {error && (
              <tr><td colSpan={5} className="text-center py-8 text-red-500">{error}</td></tr>
            )}
            {!loading && !error && exams.length === 0 && (
              <tr><td colSpan={5} className="text-center py-8 text-gray-400">ไม่พบชุดข้อสอบ</td></tr>
            )}
            {exams.map((exam) => (
              <tr
                key={exam.exam_slug}
                className="hover:bg-gray-50 cursor-pointer group"
                onClick={() => handleExamSelect(exam)}
              >
                <td className="px-6 py-4 whitespace-nowrap text-gray-900 font-medium">{exam.exam_name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-gray-900">{exam.course_name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-center text-gray-900">{exam.student_count}</td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${exam.status === 'เปิด' ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-500'}`}>{exam.status}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                  <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-700" />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Pagination styled */}
      {pagination && pagination.total_pages > 1 && (
        <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
          <div className="text-sm text-gray-500">
            เลือก 0 จาก {pagination.total_items} รายการ
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">
              หน้า {pagination.page} จาก {pagination.total_pages}
            </span>
            <div className="flex gap-1">
              <button
                className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => setPage(page - 1)}
                disabled={page === 1 || loading}
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => setPage(page + 1)}
                disabled={page === pagination.total_pages || loading}
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
