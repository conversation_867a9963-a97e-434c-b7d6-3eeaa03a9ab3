

"use client"

import { useState, useEffect } from "react"
import { getCoursesData } from "@/data/allCourses"
import type { CourseType } from "@/types/courses"
import { useParams } from "next/navigation"
import CourseTitle from "@/components/coursecompo/CourseTitle"
import CourseDetails from "../coursecompo/CourseDetail"

export default function CourseDetailPage() {
  const params = useParams()
  const [course, setCourse] = useState<CourseType | null>(null)

  useEffect(() => {
    if (params && params.id) {
      const selectedCourse = getCoursesData.find((course) => course.id === params.id)
      setCourse(selectedCourse || null)
    }
  }, [params])

  if (!course) {
    return (
      <main className="min-h-screen bg-[#F0FCFF] flex items-center justify-center">
        <p className="text-lg text-gray-800">Loading course details...</p>
      </main>
    )
  }

  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <CourseTitle
        name={course.name}
        teacher={course.teacher}
        courseId={course.id}
        coverImage={course.coverImage ?? ""} // Ensure string type
      />

      {/* Course Details */}
      <div className="max-w-5xl mx-auto -mt-16 relative z-10 bg-white shadow-lg rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x">
          <div className="p-6">
            <h3 className="font-semibold text-xl mb-1">{course.lesson.length} Modules</h3>
            <p className="text-sm text-gray-600">Explore in-depth topics and fundamentals</p>
          </div>

          <div className="p-6">
            <h3 className="font-semibold text-xl mb-1">{course.level}</h3>
            <p className="text-sm text-gray-600">Recommended Experience</p>
          </div>

          <div className="p-6">
            <h3 className="font-semibold text-xl mb-1">ตารางเวลาที่ยืดหยุ่น</h3>
            <p className="text-sm text-gray-600">{course.flexibility.duration}</p>
            {course.flexibility.description && (
              <p className="text-sm text-gray-600 mt-1">{course.flexibility.description}</p>
            )}
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="max-w-5xl mx-auto px-4 py-12">
        <div className="space-y-4">
          {course.lesson.map((lesson) => (
            <div key={lesson.id} className="border rounded-lg overflow-hidden">
              <div className="p-6 bg-gray-50 flex items-center justify-between cursor-pointer">
                <div>
                  <h3 className="font-semibold text-lg">{lesson.name}</h3>
                  <p className="text-sm text-gray-600">{lesson.description}</p>
                </div>
                <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>

              <div className="border-t">
                {lesson.content.map((content) => (
                  <div
                    key={content.id}
                    className="p-4 flex items-center space-x-3 border-b last:border-b-0 hover:bg-gray-50"
                  >
                    {content.typecontent === "video" ? (
                      <svg className="w-5 h-5 text-[#164A7E]" fill="currentColor" viewBox="0 0 512 512">
                        <path d="M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c-7.6 4.2-12.3 12.3-12.3 20.9V344c0 8.7 4.7 16.7 12.3 20.9s16.8 4.1 24.3-.5l144-88c7.1-4.4 11.5-12.1 11.5-20.5s-4.4-16.1-11.5-20.5l-144-88c-7.4-4.5-16.7-4.7-24.3-.5z" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 384 512">
                        <path d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm64 288c0 4.4-3.6 8-8 8H104c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h176c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H104c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h176c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H104c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h176c4.4 0 8 3.6 8 8v16z" />
                      </svg>
                    )}
                    <span className="text-sm">{content.name}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </main>
  )
}

