import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

interface Lesson {
  id: string;
  name: string;
  description: string;
  content: { id: string; typecontent: string; name: string }[];
}

interface LessonDetailProps {
  lessons: Lesson[];
}

const LessonDetail = ({ lessons }: LessonDetailProps) => {
  const [openIndexes, setOpenIndexes] = useState<Set<number>>(new Set());

  const toggleLesson = (index: number) => {
    setOpenIndexes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index); 
      } else {
        newSet.add(index); 
      }
      return newSet;
    });
  };

  return (
    <div className="space-y-2 mt-10">
      {lessons.map((lesson, index) => (
        <div key={lesson.id} className="border rounded-lg shadow-sm lg:w-[72.8vh] ipad-pro:w-[52vh] ipad-mini-landscape:-mt-6 ipad-pro-landscape:mt-2 ipad-air-landscape:-mt-6  ipad-mini-landscape:w-[90vh] ipad-air-landscape:w-[87vh] ipad-pro-landscape:w-[72.82vh]">

          {/* Lesson Header */}
          <button
            onClick={() => toggleLesson(index)}
            className="w-full px-6 py-4 flex items-start justify-between hover:bg-gray-50 rounded-lg transition-colors"
          >
            <div className="flex-1 text-left">
              <h3 className="font-semibold text-lg md:text-xl">{lesson.name}</h3>
              <div className="text-gray-600 text-md md:text-lg mt-1 ml-1 line-clamp-3">{lesson.description}</div>
            </div>
            <div className="flex-shrink-0 ml-4 mt-5">
              {openIndexes.has(index) ? (
                <ChevronUp className="h-5 w-5 text-emerald-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-emerald-600" />
              )}
            </div>
          </button>

          {/* Lesson Content */}
          {openIndexes.has(index) && (
            <div className="px-6 pb-4">
              {lesson.content.map((content) => (
                <div
                  key={content.id}
                  className="p-4 flex items-center space-x-3 border-b last:border-b-0 hover:rounded-lg hover:bg-gray-50"
                >
                  {content.typecontent === "Video" ? (
                    <svg className="w-5 h-5 text-[#008268] flex-shrink-0" fill="currentColor" viewBox="0 0 512 512">
                      <path d="M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c-7.6 4.2-12.3 12.3-12.3 20.9V344c0 8.7 4.7 16.7 12.3 20.9s16.8 4.1 24.3-.5l144-88c7.1-4.4 11.5-12.1 11.5-20.5s-4.4-16.1-11.5-20.5l-144-88c-7.4-4.5-16.7-4.7-24.3-.5z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 text-[#008268] flex-shrink-0" fill="currentColor" viewBox="0 0 384 512">
                      <path d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm64 288c0 4.4-3.6 8-8 8H104c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h176c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H104c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h176c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H104c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h176c4.4 0 8 3.6 8 8v16z" />
                    </svg>
                  )}
                  <span className="text-md md:text-lg text-gray-700">{content.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default LessonDetail;
