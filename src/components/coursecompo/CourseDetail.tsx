import LessonDetail from "./LessonDetail"
import Sidebar from "./sidebar"
import Image from "next/image"

interface Lesson {
  id: string
  name: string
  description: string
  content: { id: string; typecontent: string; name: string }[]
}

interface CourseDetailsProps {
  courseId: string
  description: string
  instruction: string
  modules: {
    title: string
    description: string
  }[]
  lessons: Lesson[]
  certify?: boolean
  summary: summary
}

interface summary {
  lesson_count: number
  exam_count: number
  video_count: number
}

export default function CourseDetails({
  courseId,
  description,
  instruction,
  modules,
  lessons,
  certify = false,
  summary
}: CourseDetailsProps) {
  return (
    <div className="max-w-5xl mx-auto px-4 py-8  ">
      {/* Main Content and Sidebar Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-3" >
          {/* About Section */}
          <section className="mb-12" id="">
            <h2 className="text-2xl font-bold mb-4">เกี่ยวกับคอร์สเรียน</h2>
            <p className="text-gray-700 text-lg whitespace-pre-line">{description}</p>
          </section>

          {/* Modules Section */}
          <section id="instruction" className="mb-12">
            <h2 className="text-2xl font-bold mb-4">คำแนะนำในการเรียน</h2>
            <p className="text-gray-700 mb-6 text-lg">{instruction}</p>

            <div className="">
              <div className="flex flex-col lg:flex-row gap-6">
                <div className="flex-[3] -mt-[5vh] ipad-pro:mt-4 lg:mt-0">
                  <LessonDetail lessons={lessons} />
                </div>
                <div className="flex-[1] min-w-[250px] lg:mt-[4vh]">
                  <Sidebar
                    lessonsCount={summary.lesson_count || 0}
                    videosCount={summary.video_count || 0}
                    questionsCount={summary.exam_count || 0}
                  />
                </div>
              </div>
            </div>

            {/* Certificate Section - แสดงเฉพาะเมื่อ certify เป็น true */}
            {certify && (
              <>
                <Image
                  src="/e-med/img/certificate.png"
                  width={240}
                  height={100}
                  alt="Certificate"
                  className="shadow-xl drop-shadow-md md:hidden mt-10 mx-auto"
                />
                <div className="md:mt-[5vh] -mt-[1vh] p-6 border lg:w-[100vh] md:h-[11vh] ipad-air-landscape:h-[17vh] ipad-pro-landscape:w-[99vh] ipad-mini-landscape:w-[129vh] ipad-mini-landscape:h-[15vh] ipad-pro-landscape:h-[14vh] ipad-air-landscape:w-[121vh] lg:h-[12vh] ipad-pro:w-[72.3vh] rounded-lg bg-white flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-semibold mb-2 lg:mt-1">ได้รับประกาศนียบัตร</h3>
                    <p className="text-md text-gray-600">สามารถเพิ่มข้อมูลรับรองนี้ในโปรไฟล์ ประวัติย่อ หรือ CV ของคุณได้</p>
                    <p className="text-md text-gray-600">แชร์บนโซเชียลมีเดียและระบุในรีวิวผลงานของคุณ</p>
                  </div>
                  {/* Certificate Image */}
                  <div className="flex items-center">
                    <Image
                      src="/e-med/img/certificate.png"
                      width={240}
                      height={100}
                      alt="Certificate"
                      className="shadow-xl drop-shadow-md ipad-pro:w-[22vh] ipad-pro:h-[14vh] md:w-[22vh] ipad-air-landscape:w-[32vh] ipad-air-landscape:h-[20vh] ipad-pro-landscape:w-[27vh] ipad-pro-landscape:h-[17vh] ipad-mini-landscape:h-[18vh] ipad-mini-landscape:w-[28vh] hidden md:flex"
                    />
                  </div>
                </div>
              </>
            )}
          </section>
        </div>
      </div>
    </div>
  )
}
