
import React from 'react';
import { CourseType } from "@/types/courses";

interface CourseDetailsCardProps {
  course: CourseType;
  level: string;
}

const CourseDetailsCard: React.FC<CourseDetailsCardProps> = ({ course, level }) => {
  return (
    <div className="max-w-5xl ipad-pro:mt-1 mx-auto -mt-16 relative z-10 bg-white shadow-lg ipad-pro:rounded-none lg:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x">
        <div className="p-6">
          <h3 className="font-semibold lg:text-xl sm:text-md mb-1 underline">
            {course.lesson.length} {course.lesson.length > 1 ? "บทเรียน" : "บทเรียน"}
          </h3>
          <p className="lg:text-sm sm:text-sm text-gray-600">
            สำรวจคอร์สในบทเรียนของเนื้อหาที่หลากหลายและน่าสนใจ
          </p>
        </div>

        <div className="p-6">
          <div className="flex items-center ">
            <h3 className="font-semibold lg:text-xl sm:text-md mb-1">ระดับ{course.level}</h3>
            <div className="flex gap-0.5 lg:ml-2 ml-2">
              {["เบื้องต้น", "ปานกลาง", "ยาก"].map((l, i) => {
                let circleColor = "bg-gray-200";
                if (level === "เบื้องต้น") {
                  if (i === 0) circleColor = "bg-green-500";
                } else if (level === "ปานกลาง") {
                  if (i === 0) circleColor = "bg-[#ffce66]";
                  if (i === 1) circleColor = "bg-[#FFB211]";
                } else if (level === "ยาก") {
                  if (i === 0) circleColor = "bg-red-200";
                  if (i === 1) circleColor = "bg-red-300";
                  if (i === 2) circleColor = "bg-red-500";
                }
                return <div key={l} className={`w-2 h-2 rounded-full ${circleColor}`} />;
              })}
            </div>
          </div>
          <p className="text-sm text-gray-600">{
            level === "เบื้องต้น"
              ? "เหมาะสำหรับผู้เริ่มต้น"
              : level === "ปานกลาง"
                ? "เหมาะสำหรับผู้มีพื้นฐาน"
                : "เหมาะสำหรับผู้มีประสบการณ์"
          }</p>
        </div>

        <div className="p-6">
          <h3 className="font-semibold lg:text-xl sm:text-md mb-1">ตารางเวลาที่ยืดหยุ่น</h3>
          <p className="text-sm text-gray-600">{course.flexibility.duration}</p>
          {course.flexibility.description && (
            <p className="text-sm text-gray-600 mt-1">{course.flexibility.description}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourseDetailsCard;
