import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtDecode } from 'jwt-decode'

// Define user roles
type UserRole = "student" | "lecturer" | "admin"

// JWT payload interface
interface JWTPayload {
    role: UserRole
    name: string
    email: string
    exp: number
    iat: number
}

// Protected routes configuration
const PROTECTED_ROUTES = {
    admin: ['/admin'],
    lecturer: ['/lecturer', '/admin/courses', '/admin/quiz', '/admin/exams'],
    student: ['/profile', '/courses', '/learning']
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/verify-otp'
]

// Function to check if route is public
function isPublicRoute(pathname: string): boolean {
    return PUBLIC_ROUTES.some(route => pathname.startsWith(route))
}

// Function to check if user has access to route
function hasAccess(userRole: UserRole, pathname: string): boolean {
    // Admin has access to all routes
    if (userRole === 'admin') {
        return true
    }

    // Check if lecturer has access to specific routes
    if (userRole === 'lecturer') {
        return PROTECTED_ROUTES.lecturer.some(route => pathname.startsWith(route))
    }

    // Students cannot access admin or lecturer routes
    if (userRole === 'student') {
        return !pathname.startsWith('/admin') && !pathname.startsWith('/lecturer')
    }

    return false
}

// Function to get token from request
function getTokenFromRequest(request: NextRequest): string | null {
    // Try to get token from cookie first
    const tokenFromCookie = request.cookies.get('access_token')?.value
    if (tokenFromCookie) {
        return tokenFromCookie
    }

    // Try to get token from Authorization header
    const authHeader = request.headers.get('authorization')
    if (authHeader?.startsWith('Bearer ')) {
        return authHeader.substring(7)
    }

    return null
}

// Function to validate and decode JWT token
function validateToken(token: string): JWTPayload | null {
    try {
        const decoded = jwtDecode<JWTPayload>(token)

        // Check if token is expired
        const currentTime = Math.floor(Date.now() / 1000)
        if (decoded.exp < currentTime) {
            return null
        }

        return decoded
    } catch (error) {
        console.error('Invalid token:', error)
        return null
    }
}

export function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl

    console.log('Middleware running for:', pathname)

    // Skip middleware for static files and API routes
    if (
        pathname.startsWith('/_next/') ||
        pathname.startsWith('/api/') ||
        pathname.startsWith('/static/') ||
        pathname.includes('.')
    ) {
        return NextResponse.next()
    }

    // Allow access to public routes
    if (isPublicRoute(pathname)) {
        console.log('Public route, allowing access:', pathname)
        return NextResponse.next()
    }

    // Get token from request
    const token = getTokenFromRequest(request)

    if (!token) {
        // Redirect to login if no token
        const loginUrl = new URL('/e-med/login', request.url)
        // loginUrl.searchParams.set('redirect', pathname)
        return NextResponse.redirect(loginUrl)
    }

    // Validate token
    const payload = validateToken(token)

    if (!payload) {
        // Redirect to login if token is invalid
        const loginUrl = new URL('/e-med/login', request.url)
        // loginUrl.searchParams.set('redirect', pathname)
        return NextResponse.redirect(loginUrl)
    }

    // Check if user has access to the route
    if (!hasAccess(payload.role, pathname)) {
        // Redirect to appropriate dashboard based on role
        console.log('User role:', payload.role)
        let redirectPath = '/e-med/profile/dashboard' // default for students

        if (payload.role === 'admin') {
            redirectPath = '/e-med/admin'
        } 
        if (payload.role === 'lecturer') {
            redirectPath = '/e-med/lecturer' // or lecturer-specific dashboard
        }

        const redirectUrl = new URL(redirectPath, request.url)
        return NextResponse.redirect(redirectUrl)
    }

    // Add user info to request headers for use in components
    const response = NextResponse.next()

    return response
}

// Configure which paths the middleware should run on
export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public files (images, etc.)
         */
        '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*|public).*)',
    ],
}