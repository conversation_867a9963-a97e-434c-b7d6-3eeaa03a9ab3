

// บทบาทของผู้ใช้
export type UserRole = "student" | "lecturer" | "admin"

// สถานะของผู้ใช้
export type UserStatus = "active" | "inactive"

// โครงสร้างข้อมูลผู้ใช้
export interface MedicalUser {
  id: string
  firstname: string
  lastname: string
  email: string
  position: string
  lastLogin: Date
  status: boolean
  role: string
  password: string
  profileImage?: string
  
}

// โครงสร้างข้อมูลสำหรับการเข้าสู่ระบบ
export interface LoginFormData {
  email: string
  password: string
  rememberMe?: boolean
}

// โครงสร้างข้อมูลสำหรับการรีเซ็ตรหัสผ่าน
export interface ResetPasswordFormData {
  email: string
  otp: string
  password: string
  confirmPassword: string
}

// โครงสร้างข้อมูลสำหรับการแก้ไขผู้ใช้
export interface UserFormData {
  id?: string
  firstname: string
  lastname: string
  email: string
  position: string
  role: number
  password: string
  confirmPassword: string
  status: number
  profileImage: string | null
  lastLogin?: Date
}

// โครงสร้างข้อมูลสำหรับการเปลี่ยนรหัสผ่าน
export interface PasswordChangeData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}
