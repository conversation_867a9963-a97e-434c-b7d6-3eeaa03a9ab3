import type { Question } from "./quizzes"

// โครงสร้างข้อมูลข้อสอบท้ายบท
export interface FinalExam {
  examSlug: string              // Changed from 'id' to match backend
  examName: string              // Changed from 'title' to match backend  
  examDescription?: string      // Changed from 'description' to match backend
  courseSlug: string           // Changed from 'courseId' to match backend
  questions: Question[]
  passingScore: number
  examTime?: number            // Changed from 'timeLimit' to match backend (in minutes)
  examStatus?: boolean         // Added to match backend
}

// โครงสร้างข้อมูลการส่งคำตอบข้อสอบ (for submission)
export interface ExamSubmission {
  answers: {
    questionSlug: string              // Using slug instead of ID
    selectedChoiceSlug: string[]      // Now array for both single/multiple choice
  }[]
}

// โครงสร้างข้อมูลผลลัพธ์การทำข้อสอบท้ายบท
export interface ExamResult {
  score: number                // Total score achieved
  total: number               // Total possible score
  passed: boolean             // Whether user passed
  details: {
    questionSlug: string      // Using slug instead of ID
    correct: boolean          // Whether this question was answered correctly
    userAnswers: string[]     // All choices user selected (array)
    correctAnswers: string[]  // All correct choices for this question
  }[]
}

// โครงสร้างข้อมูลบันทึกผลการสอบ (for exam records)
export interface ExamRecord {
  id: number
  score: number
  submitAt: Date              // When the exam was submitted
  examId: number              // Foreign key to exam
  userId: number              // Foreign key to user
  exam?: FinalExam           // Optional populated exam data
  user?: any                 // Optional populated user data
}