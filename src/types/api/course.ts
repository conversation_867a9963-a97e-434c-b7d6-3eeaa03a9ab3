export interface CreateCourseRequest {
    title: string
    lecturer: string // lecturer slug
    description: string
    moduleDescription: string
    difficulty: string
    duration: number // hours
    status: boolean // true for published, false for draft/archived
    coverImage?: string | null
    certify: boolean
    lessons: Lesson[]
}

export interface Lesson {
    id?: string
    name: string
    description: string
    time: number
    content: Content[]
}

export interface Content {
    id?: string
    name: string
    typecontent: string
    details: string
    time: number
}

export interface CreateCourseResponse {
    message: string
    courseId?: string
}
