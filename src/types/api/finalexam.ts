// Final Exam API Types

export interface FinalExamChoice {
  id?: string
  slug?: string
  content: string
  type: 'text' | 'image'
  isCorrect: boolean
  imageUrl?: string
}

export interface FinalExamQuestion {
  id?: string
  slug?: string
  title: string
  type: 'text' | 'image' | 'text_image' | 'video'
  content: string
  imageUrl?: string
  choices: FinalExamChoice[]
}

export interface FinalExam {
  id?: string
  slug?: string
  examSlug?: string // for compatibility with new API response
  name: string
  description: string
  courseId: string
  questions: FinalExamQuestion[]
  passingScore: number
  timeLimit: number
}

export interface CreateFinalExamRequest {
  name: string
  description: string
  courseId: string
  questions: FinalExamQuestion[]
  passingScore: number
  timeLimit: number
}

export interface CreateFinalExamResponse {
  message: string
  exam_id: string
}

export interface UpdateFinalExamResponse {
  message: string
}

export interface DeleteFinalExamResponse {
  message: string
}

// Exam submission types
export interface ExamAnswerSubmission {
  answers: Array<{
    questionSlug: string
    selectedChoiceSlug?: string
    choiceSlugs?: string[]
  }>
}

export interface ExamQuestionResult {
  questionSlug: string
  correct: boolean
  userAnswers: string[]
  correctAnswers: string[]
}

export interface ExamCheckResult {
  score: number
  total: number
  passed: boolean
  details: ExamQuestionResult[]
}
