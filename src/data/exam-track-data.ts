export interface ExamAttempt {
  attemptNumber: number;
  score: number;
  totalScore: number;
  status: string;
  timeSpent: string;
  submittedAt: string;
}

export interface StudentExamStats {
  id: string;
  name: string;
  position: string;
  status: 'ผ่าน' | 'ไม่ผ่าน';
  bestScore: number;
  attempts: ExamAttempt[];
}

export interface ExamTrackData {
  examName: string;
  students: StudentExamStats[];
}

// ข้อมูลสถิติการสอบสำหรับแต่ละข้อสอบ
export const examTrackDataByExamId: Record<string, ExamTrackData> = {
  'e1': {
    examName: "แบบทดสอบก่อนเรียน - การปฐมพยาบาลเบื้องต้น",
    students: [
      {
        id: "1",
        name: "นางสาวสมหญิง ใจดี",
        position: "พยาบาลวิชาชีพ",
        status: "ผ่าน",
        bestScore: 95,
        attempts: [
          {
            attemptNumber: 1,
            score: 85,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "25 นาที",
            submittedAt: "28 ธ.ค. 2567 09:30"
          },
          {
            attemptNumber: 2,
            score: 95,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "22 นาที",
            submittedAt: "28 ธ.ค. 2567 14:15"
          }
        ]
      },
      {
        id: "2",
        name: "นายสมชาย รักเรียน",
        position: "พยาบาลประจำการ",
        status: "ผ่าน",
        bestScore: 88,
        attempts: [
          {
            attemptNumber: 1,
            score: 88,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "30 นาที",
            submittedAt: "28 ธ.ค. 2567 10:45"
          }
        ]
      },
      {
        id: "3",
        name: "นางสาวมาลี สุขใส",
        position: "พยาบาลหัวหน้าแผนก",
        status: "ไม่ผ่าน",
        bestScore: 65,
        attempts: [
          {
            attemptNumber: 1,
            score: 58,
            totalScore: 100,
            status: "ไม่ผ่าน",
            timeSpent: "35 นาที",
            submittedAt: "28 ธ.ค. 2567 11:20"
          },
          {
            attemptNumber: 2,
            score: 65,
            totalScore: 100,
            status: "ไม่ผ่าน",
            timeSpent: "32 นาที",
            submittedAt: "28 ธ.ค. 2567 15:45"
          }
        ]
      },
      {
        id: "4",
        name: "นายอนุชา เก่งมาก",
        position: "พยาบาลวิชาชีพ",
        status: "ผ่าน",
        bestScore: 92,
        attempts: [
          {
            attemptNumber: 1,
            score: 78,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "28 นาที",
            submittedAt: "28 ธ.ค. 2567 13:00"
          },
          {
            attemptNumber: 2,
            score: 92,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "24 นาที",
            submittedAt: "28 ธ.ค. 2567 16:30"
          }
        ]
      }
    ]
  },
  'e2': {
    examName: "แบบทดสอบหลังเรียน - การปฐมพยาบาลเบื้องต้น",
    students: [
      {
        id: "1",
        name: "นางสาวสมหญิง ใจดี",
        position: "พยาบาลวิชาชีพ",
        status: "ผ่าน",
        bestScore: 98,
        attempts: [
          {
            attemptNumber: 1,
            score: 98,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "20 นาที",
            submittedAt: "30 ธ.ค. 2567 09:30"
          }
        ]
      },
      {
        id: "2",
        name: "นายสมชาย รักเรียน",
        position: "พยาบาลประจำการ",
        status: "ผ่าน",
        bestScore: 95,
        attempts: [
          {
            attemptNumber: 1,
            score: 95,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "23 นาที",
            submittedAt: "30 ธ.ค. 2567 10:15"
          }
        ]
      },
      {
        id: "3",
        name: "นางสาวมาลี สุขใส",
        position: "พยาบาลหัวหน้าแผนก",
        status: "ผ่าน",
        bestScore: 85,
        attempts: [
          {
            attemptNumber: 1,
            score: 72,
            totalScore: 100,
            status: "ไม่ผ่าน",
            timeSpent: "35 นาที",
            submittedAt: "30 ธ.ค. 2567 11:00"
          },
          {
            attemptNumber: 2,
            score: 85,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "28 นาที",
            submittedAt: "30 ธ.ค. 2567 14:30"
          }
        ]
      }
    ]
  },
  'e3': {
    examName: "แบบทดสอบความรู้ - การดูแลผู้ป่วยเบาหวาน",
    students: [
      {
        id: "1",
        name: "นางสาวดาวใส จันทร์เพ็ญ",
        position: "พยาบาลวิชาชีพ",
        status: "ผ่าน",
        bestScore: 88,
        attempts: [
          {
            attemptNumber: 1,
            score: 88,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "27 นาที",
            submittedAt: "2 ม.ค. 2568 09:00"
          }
        ]
      },
      {
        id: "2",
        name: "นายประยุทธ วิทยาศาสตร์",
        position: "พยาบาลประจำการ",
        status: "ผ่าน",
        bestScore: 91,
        attempts: [
          {
            attemptNumber: 1,
            score: 91,
            totalScore: 100,
            status: "ผ่าน",
            timeSpent: "24 นาที",
            submittedAt: "2 ม.ค. 2568 10:30"
          }
        ]
      }
    ]
  }
}

export const mockExamTrackData = examTrackDataByExamId['e1']
