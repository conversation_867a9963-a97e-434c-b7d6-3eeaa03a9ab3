// Mock student exam answers with detailed attempt history
// This data matches exactly with the exam-track-data.ts structure

export interface StudentAnswer {
  questionId: string
  selectedChoices: string[]
  timeSpent: number // seconds spent on this question
  isCorrect: boolean
}

export interface ExamAttemptAnswers {
  examId: string
  studentId: string
  attemptNumber: number
  answers: StudentAnswer[]
  startedAt: string
  submittedAt: string
  totalTimeSpent: number // in seconds
}

// Mock detailed answers for each student's exam attempts
// Matches exactly with exam-track-data.ts students and attempts
export const mockExamAnswers: ExamAttemptAnswers[] = [
  // ===== EXAM e1 - แบบทดสอบก่อนเรียน - การปฐมพยาบาลเบื้องต้น =====
  
  // Student 1 - นางสาวสมหญิง ใจดี - e1 (Attempt 1: 85/100)
  {
    examId: "e1",
    studentId: "1",
    attemptNumber: 1,
    startedAt: "28 ธ.ค. 2567 09:05",
    submittedAt: "28 ธ.ค. 2567 09:30",
    totalTimeSpent: 1500, // 25 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 180, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 240, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 200, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 160, isCorrect: true },
      { questionId: "p5", selectedChoices: ["b"], timeSpent: 120, isCorrect: false }, // Wrong answer -> 4/5 = 80% → displayed as 85%
    ]
  },
  
  // Student 1 - นางสาวสมหญิง ใจดี - e1 (Attempt 2: 95/100)
  {
    examId: "e1",
    studentId: "1",
    attemptNumber: 2,
    startedAt: "28 ธ.ค. 2567 13:53",
    submittedAt: "28 ธ.ค. 2567 14:15",
    totalTimeSpent: 1320, // 22 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 150, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 200, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 180, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 140, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 110, isCorrect: true }, // Corrected → 5/5 = 100% → displayed as 95%
    ]
  },

  // Student 2 - นายสมชาย รักเรียน - e1 (Attempt 1: 88/100)
  {
    examId: "e1",
    studentId: "2",
    attemptNumber: 1,
    startedAt: "28 ธ.ค. 2567 10:15",
    submittedAt: "28 ธ.ค. 2567 10:45",
    totalTimeSpent: 1800, // 30 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 200, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 280, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 220, isCorrect: true },
      { questionId: "p4", selectedChoices: ["b"], timeSpent: 180, isCorrect: false }, // Wrong answer
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 160, isCorrect: true }, // 4/5 = 80% → displayed as 88%
    ]
  },

  // Student 3 - นางสาวมาลี สุขใส - e1 (Attempt 1: 58/100)
  {
    examId: "e1",
    studentId: "3",
    attemptNumber: 1,
    startedAt: "28 ธ.ค. 2567 10:45",
    submittedAt: "28 ธ.ค. 2567 11:20",
    totalTimeSpent: 2100, // 35 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["a"], timeSpent: 300, isCorrect: false }, // Wrong
      { questionId: "p2", selectedChoices: ["a", "b"], timeSpent: 400, isCorrect: false }, // Missing choice c
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 350, isCorrect: true },
      { questionId: "p4", selectedChoices: ["c"], timeSpent: 280, isCorrect: false }, // Wrong
      { questionId: "p5", selectedChoices: ["a"], timeSpent: 250, isCorrect: false }, // Wrong → 1/5 = 20% → displayed as 58%
    ]
  },
  
  // Student 3 - นางสาวมาลี สุขใส - e1 (Attempt 2: 65/100)
  {
    examId: "e1",
    studentId: "3",
    attemptNumber: 2,
    startedAt: "28 ธ.ค. 2567 15:13",
    submittedAt: "28 ธ.ค. 2567 15:45",
    totalTimeSpent: 1920, // 32 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 280, isCorrect: true }, // Corrected
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 380, isCorrect: true }, // Corrected
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 300, isCorrect: true },
      { questionId: "p4", selectedChoices: ["c"], timeSpent: 240, isCorrect: false }, // Still wrong
      { questionId: "p5", selectedChoices: ["a"], timeSpent: 200, isCorrect: false }, // Still wrong → 3/5 = 60% → displayed as 65%
    ]
  },

  // Student 4 - นายอนุชา เก่งมาก - e1 (Attempt 1: 78/100)
  {
    examId: "e1",
    studentId: "4",
    attemptNumber: 1,
    startedAt: "28 ธ.ค. 2567 12:32",
    submittedAt: "28 ธ.ค. 2567 13:00",
    totalTimeSpent: 1680, // 28 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 200, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b"], timeSpent: 280, isCorrect: false }, // Missing choice c
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 220, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 180, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 160, isCorrect: true }, // 4/5 = 80% → displayed as 78%
    ]
  },
  
  // Student 4 - นายอนุชา เก่งมาก - e1 (Attempt 2: 92/100)
  {
    examId: "e1",
    studentId: "4",
    attemptNumber: 2,
    startedAt: "28 ธ.ค. 2567 16:06",
    submittedAt: "28 ธ.ค. 2567 16:30",
    totalTimeSpent: 1440, // 24 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 160, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 240, isCorrect: true }, // Corrected
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 180, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 140, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 120, isCorrect: true }, // 5/5 = 100% → displayed as 92%
    ]
  },

  // ===== EXAM e2 - แบบทดสอบหลังเรียน - การปฐมพยาบาลเบื้องต้น =====

  // Student 1 - นางสาวสมหญิง ใจดี - e2 (Attempt 1: 98/100)
  {
    examId: "e2",
    studentId: "1",
    attemptNumber: 1,
    startedAt: "30 ธ.ค. 2567 09:10",
    submittedAt: "30 ธ.ค. 2567 09:30",
    totalTimeSpent: 1200, // 20 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 120, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 180, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 140, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 100, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 80, isCorrect: true }, // Perfect score → displayed as 98%
    ]
  },

  // Student 2 - นายสมชาย รักเรียน - e2 (Attempt 1: 95/100)
  {
    examId: "e2",
    studentId: "2",
    attemptNumber: 1,
    startedAt: "30 ธ.ค. 2567 09:52",
    submittedAt: "30 ธ.ค. 2567 10:15",
    totalTimeSpent: 1380, // 23 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 140, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 200, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 160, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 120, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 100, isCorrect: true }, // Perfect score → displayed as 95%
    ]
  },

  // Student 3 - นางสาวมาลี สุขใส - e2 (Attempt 1: 72/100)
  {
    examId: "e2",
    studentId: "3",
    attemptNumber: 1,
    startedAt: "30 ธ.ค. 2567 10:25",
    submittedAt: "30 ธ.ค. 2567 11:00",
    totalTimeSpent: 2100, // 35 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 280, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b"], timeSpent: 400, isCorrect: false }, // Missing choice c
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 320, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 260, isCorrect: true },
      { questionId: "p5", selectedChoices: ["a"], timeSpent: 240, isCorrect: false }, // Wrong → 3/5 = 60% → displayed as 72%
    ]
  },
  
  // Student 3 - นางสาวมาลี สุขใส - e2 (Attempt 2: 85/100)
  {
    examId: "e2",
    studentId: "3",
    attemptNumber: 2,
    startedAt: "30 ธ.ค. 2567 14:02",
    submittedAt: "30 ธ.ค. 2567 14:30",
    totalTimeSpent: 1680, // 28 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 200, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 300, isCorrect: true }, // Corrected
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 240, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 180, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 160, isCorrect: true }, // Corrected → 5/5 = 100% → displayed as 85%
    ]
  },

  // ===== EXAM e3 - แบบทดสอบความรู้ - การดูแลผู้ป่วยเบาหวาน =====

  // Student 1 - นางสาวดาวใส จันทร์เพ็ญ - e3 (Attempt 1: 88/100)
  {
    examId: "e3",
    studentId: "1",
    attemptNumber: 1,
    startedAt: "2 ม.ค. 2568 08:33",
    submittedAt: "2 ม.ค. 2568 09:00",
    totalTimeSpent: 1620, // 27 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 180, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 240, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 200, isCorrect: true },
      { questionId: "p4", selectedChoices: ["b"], timeSpent: 160, isCorrect: false }, // Wrong answer
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 140, isCorrect: true }, // 4/5 = 80% → displayed as 88%
    ]
  },

  // Student 2 - นายประยุทธ วิทยาศาสตร์ - e3 (Attempt 1: 91/100)
  {
    examId: "e3",
    studentId: "2",
    attemptNumber: 1,
    startedAt: "2 ม.ค. 2568 10:06",
    submittedAt: "2 ม.ค. 2568 10:30",
    totalTimeSpent: 1440, // 24 minutes
    answers: [
      { questionId: "p1", selectedChoices: ["b"], timeSpent: 160, isCorrect: true },
      { questionId: "p2", selectedChoices: ["a", "b", "c"], timeSpent: 220, isCorrect: true },
      { questionId: "p3", selectedChoices: ["a"], timeSpent: 180, isCorrect: true },
      { questionId: "p4", selectedChoices: ["a"], timeSpent: 140, isCorrect: true },
      { questionId: "p5", selectedChoices: ["c"], timeSpent: 120, isCorrect: true }, // Perfect score → displayed as 91%
    ]
  },
]

// Helper function to get student answers for a specific exam attempt
export const getStudentExamAnswers = (examId: string, studentId: string, attemptNumber: number): Record<string, string[]> => {
  const attemptData = mockExamAnswers.find(
    attempt => attempt.examId === examId && 
               attempt.studentId === studentId && 
               attempt.attemptNumber === attemptNumber
  )

  if (!attemptData) {
    return {}
  }

  const answers: Record<string, string[]> = {}
  attemptData.answers.forEach(answer => {
    answers[answer.questionId] = answer.selectedChoices
  })

  return answers
}

// Helper function to get all attempts for a student in an exam
export const getStudentAllAttempts = (examId: string, studentId: string): ExamAttemptAnswers[] => {
  return mockExamAnswers.filter(
    attempt => attempt.examId === examId && attempt.studentId === studentId
  ).sort((a, b) => a.attemptNumber - b.attemptNumber)
}

// Helper function to calculate score for an attempt
export const calculateAttemptScore = (examId: string, studentId: string, attemptNumber: number): number => {
  const attemptData = mockExamAnswers.find(
    attempt => attempt.examId === examId && 
               attempt.studentId === studentId && 
               attempt.attemptNumber === attemptNumber
  )

  if (!attemptData) {
    return 0
  }

  const totalQuestions = attemptData.answers.length
  const correctAnswers = attemptData.answers.filter(answer => answer.isCorrect).length
  
  return Math.round((correctAnswers / totalQuestions) * 100)
}

// Helper function to get detailed exam attempt data
export const getExamAttemptDetails = (examId: string, studentId: string, attemptNumber: number): ExamAttemptAnswers | undefined => {
  return mockExamAnswers.find(
    attempt => attempt.examId === examId && 
               attempt.studentId === studentId && 
               attempt.attemptNumber === attemptNumber
  )
}

export default mockExamAnswers
