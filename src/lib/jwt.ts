// JWT utility functions for token parsing and user data extraction

interface JWTPayload {
  slug?: string;
  user_id?: string;
  username?: string;
  email?: string;
  exp?: number;
  iat?: number;
  [key: string]: any;
}

/**
 * Decode JWT token without verification (client-side only for data extraction)
 * Note: This should only be used for non-sensitive data extraction
 * @param token JWT token string
 * @returns Decoded payload or null if invalid
 */
export const decodeJWTPayload = (token: string): JWTPayload | null => {
  try {
    // JWT has 3 parts separated by dots: header.payload.signature
    const parts = token.split('.');
    
    if (parts.length !== 3) {
      console.error('Invalid JWT token format');
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];
    
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - (payload.length % 4)) % 4);
    
    // Decode base64
    const decodedPayload = atob(paddedPayload);
    
    // Parse JSON
    const parsedPayload = JSON.parse(decodedPayload) as JWTPayload;
    
    return parsedPayload;
  } catch (error) {
    console.error('Failed to decode JWT token:', error);
    return null;
  }
};

/**
 * Get auth token from cookie
 * @returns JWT token string or null
 */
export const getAuthTokenFromCookie = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  const tokenMatch = document.cookie.match(/(?:^|;\s*)access_token=([^;]+)/);
  return tokenMatch ? decodeURIComponent(tokenMatch[1]) : null;
};

/**
 * Extract user slug from JWT token stored in cookie
 * @returns User slug or null if not found
 */
export const getUserSlugFromToken = (): string | null => {
  const token = getAuthTokenFromCookie();
  
  if (!token) {
    console.warn('No auth token found in cookie');
    return null;
  }

  const payload = decodeJWTPayload(token);
  
  if (!payload) {
    console.warn('Failed to decode JWT payload');
    return null;
  }

  // Try different possible field names for slug
  const userSlug = payload.slug || payload.user_slug || payload.username;
  
  if (!userSlug) {
    console.warn('No slug field found in JWT payload. Available fields:', Object.keys(payload));
    return null;
  }

  return userSlug;
};

/**
 * Check if JWT token is expired
 * @param token JWT token string
 * @returns true if expired, false if valid, null if cannot determine
 */
export const isTokenExpired = (token: string): boolean | null => {
  const payload = decodeJWTPayload(token);
  
  if (!payload || !payload.exp) {
    return null; // Cannot determine expiration
  }

  // JWT exp is in seconds, Date.now() is in milliseconds
  const currentTime = Math.floor(Date.now() / 1000);
  
  return payload.exp < currentTime;
};

/**
 * Get user data from JWT token
 * @returns User data object or null
 */
export const getUserDataFromToken = (): JWTPayload | null => {
  const token = getAuthTokenFromCookie();
  
  if (!token) {
    return null;
  }

  return decodeJWTPayload(token);
};
