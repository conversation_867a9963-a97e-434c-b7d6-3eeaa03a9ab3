import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from "axios"

// API utility functions for making authenticated requests
const API_BASE_URL = '/e-med/api' // Use Next.js API proxy to avoid CORS issues

// Create axios instance with base configuration
const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: false, // Disable credentials to avoid CORS issues with proxy
})

// Get auth token from cookie (optimized)
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null
  
  const tokenMatch = document.cookie.match(/(?:^|;\s*)access_token=([^;]+)/)
  return tokenMatch ? decodeURIComponent(tokenMatch[1]) : null
}

// Request interceptor to add auth token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = getAuthToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for centralized error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    const apiError = new ApiError(
      (error.response?.data as any)?.error || error.message || 'Network error occurred',
      error.response?.status,
      error.response?.data
    )
    return Promise.reject(apiError)
  }
)

// Generic API request function using optimized axios instance
export const apiRequest = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {}
): Promise<T> => {
  try {
    const response = await axiosInstance.request<T>({
      url: endpoint,
      ...options,
    })
    return response.data
  } catch (error) {
    // Re-throw ApiError from interceptor or create new one
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Unexpected error occurred')
  }
}

// Convenience methods for different HTTP verbs
export const api = {
  get: <T>(endpoint: string, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { method: 'GET', ...config }),

  post: <T>(endpoint: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { method: 'POST', data, ...config }),

  put: <T>(endpoint: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { method: 'PUT', data, ...config }),

  patch: <T>(endpoint: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { method: 'PATCH', data, ...config }),

  delete: <T>(endpoint: string, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { method: 'DELETE', ...config }),
}

// Enhanced error handling utility
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
    
    // Maintains proper stack trace for debugging
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError)
    }
  }
}

// Export axios instance for advanced usage
export { axiosInstance }
