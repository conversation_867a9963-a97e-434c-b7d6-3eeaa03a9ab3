package models_request

type AssignPathType struct {
	PathID       uint `json:"path_id"`       // ID of the path
	LearningType bool `json:"learning_type"` // Assuming this is a boolean indicating the type of path
}

type AssignPathRequest struct {
	PathID   uint             `json:"path_id" binding:"required"`   // ID of the path
	UserSlug string           `json:"user_slug" binding:"required"` // ID of the user
	Assign   []AssignPathType `json:"assign"`                       // Assuming this is a boolean indicating the type of path
}

type AssignPathDeleteRequest struct {
	UserSlug string `json:"user_slug" binding:"required"` // ID of the user
	PathIDs  []uint `json:"path_ids" binding:"required"`  // IDs of the paths to be deleted
}
