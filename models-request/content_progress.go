package models_request

type ContentProgressRequest struct {
	ContentLessonSlug string `json:"content_lesson_slug" binding:"required"`
}

type ContentProgressResponse struct {
	ContentLessonID   uint   `json:"content_lesson_id"`
	ContentLessonSlug string `json:"content_lesson_slug"`
	UserID            uint   `json:"user_id"`
	UserSlug          string `json:"user_slug"`
	IsCompleted       bool   `json:"is_completed"`
	CompletedAt       string `json:"completed_at,omitempty"`
}
