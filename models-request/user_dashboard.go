package models_request

type UserCourse struct {
	CourseSlug        string       `json:"course_slug"`
	CourseImage       string       `json:"course_image"`
	CourseGraduated   bool         `json:"course_graduated"`
	CourseName        string       `json:"course_name"`
	CoursePath        []PathCourse `json:"course_path"`
	CourseDescription string       `json:"course_description"`
	CourseProcessing  int          `json:"course_processing"`
	CourseAmount      int          `json:"course_amount"`
	CourseLessoned    int          `json:"course_lessoned"`
	CourseDifficulty  string       `json:"course_difficulty"`
	CourseDuration    int          `json:"course_duration"`
}

type PathCourse struct {
	LessonSlug string `json:"lesson_slug"`
	LessonName string `json:"lesson_name"`
}
