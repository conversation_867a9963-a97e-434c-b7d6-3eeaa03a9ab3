package models_request

type TrackLearningRow struct {
	CourseName    string   `json:"course_name"`
	PathNames     []string `json:"path_names"`
	AssignedCount int      `json:"assigned_count"`
	CourseStatus  bool     `json:"course_status"`
	CourseSlug    string   `json:"course_slug"`
}

type TrackLearningResponse struct {
	Data       []TrackLearningRow `json:"data"`
	Pagination PaginationMeta     `json:"pagination"`
}

type PaginationMeta struct {
	CurrentPage  int  `json:"current_page"`
	TotalPages   int  `json:"total_pages"`
	TotalItems   int  `json:"total_items"`
	ItemsPerPage int  `json:"items_per_page"`
	HasNext      bool `json:"has_next"`
	HasPrev      bool `json:"has_prev"`
}

// New Track Learning Detail API Models
type TrackLearningDetailRequest struct {
	CourseSlug string `json:"course_slug,omitempty"`
	Search     string `json:"search,omitempty"`
	Status     string `json:"status,omitempty"` // "completed", "not-completed", or empty for all
	Page       int    `json:"page,omitempty"`
	Limit      int    `json:"limit,omitempty"`
}

type StudentTrackingDetail struct {
	UserSlug                string `json:"user_slug"`
	Name                    string `json:"name"`
	FirstName               string `json:"first_name"`
	LastName                string `json:"last_name"`
	Position                string `json:"position"`
	Avatar                  string `json:"avatar"`
	Progress                int    `json:"progress"`                   // Progress percentage (0-100)
	LastFinishContentLesson string `json:"last_finish_content_lesson"` // Last finished content lesson name
	LastLogin               string `json:"last_login"`                 // Last login timestamp
	Status                  string `json:"status"`                     // "completed", "not-completed", "not-started"
	CompletedLessons        int    `json:"completed_lessons"`
	TotalLessons            int    `json:"total_lessons"`
}

type CourseTrackingOverview struct {
	CourseID           uint   `json:"course_id"`
	CourseSlug         string `json:"course_slug"`
	CourseName         string `json:"course_name"`
	CourseDescription  string `json:"course_description"`
	TotalStudents      int    `json:"total_students"`
	CompletedStudents  int    `json:"completed_students"`
	InProgressStudents int    `json:"in_progress_students"`
	NotStartedStudents int    `json:"not_started_students"`
}

type TrackLearningDetailResponse struct {
	CourseOverview CourseTrackingOverview  `json:"course_overview"`
	Students       []StudentTrackingDetail `json:"students"`
	Pagination     PaginationInfo          `json:"pagination"`
}

type TrackLearningCoursesResponse struct {
	Courses []CourseTrackingOverview `json:"courses"`
}

type PaginationInfo struct {
	Page        int  `json:"page"`
	Limit       int  `json:"limit"`
	TotalItems  int  `json:"total_items"`
	TotalPages  int  `json:"total_pages"`
	HasNext     bool `json:"has_next"`
	HasPrevious bool `json:"has_previous"`
}
