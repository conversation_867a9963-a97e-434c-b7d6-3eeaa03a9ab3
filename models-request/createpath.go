package models_request

type Path struct {
	IdCourse  string `json:"course_id"`
	PathOrder int    `json:"path_order"`
}

type CreatePathRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      bool   `json:"status"` // Assuming status is a boolean indicating active/inactive
	Path        []Path `json:"path"`   // Assuming Path is a struct that contains path details
}

type Course struct {
	CourseID   string `json:"course_id"`
	CourseName string `json:"course_name"`
}

type LearningPathResponse struct {
	Description string   `json:"description"`
	ID          int      `json:"id"`
	Name        string   `json:"name"`
	Path        []Course `json:"path"`
	Status      bool     `json:"status"`
}

type LearningPathResponseTable struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      bool   `json:"status"`
	Amount      int    `json:"amount"` // Assuming this is the number of courses in the path
}
