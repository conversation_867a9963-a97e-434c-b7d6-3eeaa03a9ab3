package models_request

type QuizRequest struct {
	QuizName        string     `json:"quiz_name"`
	QuizDescription string     `json:"quiz_description"`
	QuizNumber      int        `json:"quiz_number"`
	QuizTime        float32    `json:"quiz_time"`
	QuizStatus      bool       `json:"quiz_status"`
	ContentLessonID string     `json:"content_lesson_id"`
	Question        []Question `json:"question"`
}

type Question struct {
	Question      string   `json:"question"`
	QuestionImage string   `json:"question_image,omitempty"`
	Detail        string   `json:"detail"`
	TimeInsert    int      `json:"time_insert"`
	QuestionType  uint     `json:"question_type"`
	Slug          string   `json:"slug"`
	Choice        []Choice `json:"choice"`
}

type Choice struct {
	Choice      string `json:"choice"`
	ChoiceImage string `json:"choice_image,omitempty"`
	IsCorrect   bool   `json:"is_correct"`
	ChoiceType  uint   `json:"choice_type"`
	Slug        string `json:"slug"`
}
