package models_request

type Courses struct {
	CourseSlug        string    `json:"course_slug"`
	CourseName        string    `json:"course_name"`
	CourseDescription string    `json:"course_description"`
	CourseLecturer    Lecturer  `json:"course_lecturer"`
	CourseLessons     []Lessons `json:"course_lessons"`
	CourseExam        Exam      `json:"course_exam"`
}

type Lecturer struct {
	LecturerName     string `json:"lecturer_name"`
	LecturerPosition string `json:"lecturer_position"`
}

type Lessons struct {
	LessonName     string    `json:"lesson_name"`
	LessonDuration string    `json:"lesson_duration"`
	LessonContent  []Content `json:"lesson_content"`
	IsLocked       bool      `json:"is_locked"`
}

type Content struct {
	ContentSlug           string `json:"content_slug"`
	ContentLessonName     string `json:"content_lesson_name"`
	ContentLessonDuration string `json:"content_lesson_duration"`
	URL                   string `json:"url"`
	IsLocked              bool   `json:"is_locked"`
}

type Exam struct {
	HasExam      bool   `json:"has_exam"`
	ExamDuration string `json:"exam_duration"`
	ExamSlug     string `json:"exam_slug"`
	IsLocked     bool   `json:"is_locked"`
}
