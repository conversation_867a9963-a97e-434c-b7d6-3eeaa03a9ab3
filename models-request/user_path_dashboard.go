package models_request

type UserPathDashboard struct {
	PathName           string           `json:"path_name"` // Name of the path
	PathSlug           string           `json:"path_slug"`
	PathDescription    string           `json:"path_description"`
	PathAmount         int              `json:"path_amount"`
	PathCourseLessoned int              `json:"path_lessoned"`
	PathDuration       int              `json:"path_duration"`
	PathProcessing     int              `json:"path_processing"`
	Courses            []UserPathCourse `json:"courses"` // List of courses in the path
}

type UserPathCourse struct {
	LessonSlug       string `json:"lesson_slug"`
	LessonName       string `json:"lesson_name"`
	LessonProgress   int    `json:"lesson_progress"`   // Percentage of lesson progress
	LessonDuration   int    `json:"lesson_duration"`   // Duration of the lesson in minutes
	LessonDifficulty string `json:"lesson_difficulty"` // Difficulty level of the lesson
	LessonHasExam    bool   `json:"lesson_has_exam"`   // Whether the lesson has an exam
	LessonExamined   bool   `json:"lesson_examined"`   // Whether the lesson has been examined
}
