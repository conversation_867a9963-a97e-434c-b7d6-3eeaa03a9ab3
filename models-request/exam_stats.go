package models_request

import "time"

// ExamStatsRequest represents the request parameters for exam statistics
type ExamStatsRequest struct {
	Search string `json:"search,omitempty"`
	Page   int    `json:"page,omitempty"`
	Limit  int    `json:"limit,omitempty"`
}

// ExamOverview represents exam information with statistics
type ExamOverview struct {
	ExamSlug       string    `json:"exam_slug"`
	ExamName       string    `json:"exam_name"`
	CourseSlug     string    `json:"course_slug"`
	CourseName     string    `json:"course_name"`
	Status         string    `json:"status"`
	StudentCount   int       `json:"student_count"`
	SubmittedCount int       `json:"submitted_count"`
	PassedCount    int       `json:"passed_count"`
	FailedCount    int       `json:"failed_count"`
	AverageScore   float64   `json:"average_score"`
	MaxScore       int       `json:"max_score"`
	PassingScore   int       `json:"passing_score"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ExamStatsResponse represents the response for exam statistics list
type ExamStatsResponse struct {
	Exams      []ExamOverview `json:"exams"`
	Pagination PaginationInfo `json:"pagination"`
}

// ExamDetailStats represents detailed statistics for a specific exam
type ExamDetailStats struct {
	ExamSlug       string              `json:"exam_slug"`
	ExamName       string              `json:"exam_name"`
	CourseSlug     string              `json:"course_slug"`
	CourseName     string              `json:"course_name"`
	Status         string              `json:"status"`
	TotalQuestions int                 `json:"total_questions"`
	MaxScore       int                 `json:"max_score"`
	PassingScore   int                 `json:"passing_score"`
	ExamDuration   int                 `json:"exam_duration"` // in minutes
	Statistics     ExamStatistics      `json:"statistics"`
	Students       []StudentExamResult `json:"students"`
	Pagination     PaginationInfo      `json:"pagination"`
}

// ExamStatistics represents overall exam statistics
type ExamStatistics struct {
	TotalStudents  int     `json:"total_students"`
	SubmittedCount int     `json:"submitted_count"`
	PassedCount    int     `json:"passed_count"`
	FailedCount    int     `json:"failed_count"`
	NotTakenCount  int     `json:"not_taken_count"`
	AverageScore   float64 `json:"average_score"`
	HighestScore   int     `json:"highest_score"`
	LowestScore    int     `json:"lowest_score"`
	PassRate       float64 `json:"pass_rate"`
	CompletionRate float64 `json:"completion_rate"`
}

// StudentExamResult represents individual student exam result
type StudentExamResult struct {
	UserSlug      string     `json:"user_slug"`
	Name          string     `json:"name"`
	FirstName     string     `json:"first_name"`
	LastName      string     `json:"last_name"`
	Position      string     `json:"position"`
	Avatar        string     `json:"avatar"`
	Score         *int       `json:"score"`
	MaxScore      int        `json:"max_score"`
	Percentage    *float64   `json:"percentage"`
	Status        string     `json:"status"` // "passed", "failed", "not_taken"
	AttemptCount  int        `json:"attempt_count"`
	LastAttemptAt *time.Time `json:"last_attempt_at"`
	CompletedAt   *time.Time `json:"completed_at"`
	TimeSpent     *int       `json:"time_spent"` // in seconds
	IsPassed      bool       `json:"is_passed"`
}

// ExamDetailRequest represents request parameters for exam detail stats
type ExamDetailRequest struct {
	ExamSlug string `json:"exam_slug"`
	Search   string `json:"search,omitempty"`
	Status   string `json:"status,omitempty"` // "passed", "failed", "not_taken"
	Page     int    `json:"page,omitempty"`
	Limit    int    `json:"limit,omitempty"`
}

// ExamAttempt represents a single exam attempt
type ExamAttempt struct {
	AttemptNumber int    `json:"attempt_number"`
	Score         int    `json:"score"`
	TotalScore    int    `json:"total_score"`
	Status        string `json:"status"` // "ผ่าน", "ไม่ผ่าน"
	TimeSpent     string `json:"time_spent"`
	SubmittedAt   string `json:"submitted_at"`
}

// StudentExamStats represents student exam statistics with attempts
type StudentExamStats struct {
	ID        string        `json:"id"`
	Name      string        `json:"name"`
	Position  string        `json:"position"`
	Status    string        `json:"status"` // "ผ่าน", "ไม่ผ่าน"
	BestScore int           `json:"best_score"`
	Attempts  []ExamAttempt `json:"attempts"`
}

// ExamTrackData represents exam tracking data with students and their attempts
type ExamTrackData struct {
	ExamName string             `json:"exam_name"`
	Students []StudentExamStats `json:"students"`
}
