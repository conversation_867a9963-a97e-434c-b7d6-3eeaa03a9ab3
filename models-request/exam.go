package models_request

// FinalExamRequest matches frontend interface
type FinalExamRequest struct {
	Name         string            `json:"name" binding:"required"`
	Description  string            `json:"description"`
	CourseId     string            `json:"courseId" binding:"required"` // Using course slug
	PassingScore int               `json:"passingScore" binding:"required"`
	TimeLimit    int               `json:"timeLimit" binding:"required"` // in minutes
	Questions    []ExamQuestionReq `json:"questions" binding:"required"`
}

type ExamQuestionReq struct {
	Title    string          `json:"title" binding:"required"`
	Type     string          `json:"type"`     // "text", "image", "text_image", "video"
	Content  string          `json:"content"`  // Question content/description
	ImageUrl string          `json:"imageUrl"` // Optional image URL
	Choices  []ExamChoiceReq `json:"choices" binding:"required"`
}

type ExamChoiceReq struct {
	Content   string `json:"content" binding:"required"`
	Type      string `json:"type"`      // "text", "image"
	IsCorrect bool   `json:"isCorrect"`
	ImageUrl  string `json:"imageUrl"`  // Optional image URL for choice
}

// Legacy ExamRequest (keeping for backward compatibility)
type ExamRequest struct {
	ExamName        string         `json:"exam_name"`
	ExamDescription string         `json:"exam_description"`
	ExamTime        int            `json:"exam_time"`
	ExamStatus      bool           `json:"exam_status"`
	PassingScore    int            `json:"passing_score"`
	ExamSlug        string         `json:"exam_slug"`
	CourseSlug      string         `json:"course_slug"`
	Questions       []ExamQuestion `json:"questions"`
}

type ExamQuestion struct {
	Question      string       `json:"question"`
	QuestionImage string       `json:"question_image,omitempty"` // Assuming this is a base64 encoded string
	Detail        string       `json:"detail"`
	TimeInsert    int          `json:"time_insert"`
	QuestionType  uint         `json:"question_type"`
	Choices       []ExamChoice `json:"choices"`
	Slug          string       `json:"question_slug"`
}

type ExamChoice struct {
	Choice      string `json:"choice"`
	ChoiceImage string `json:"choice_image,omitempty"` // Assuming this is a base64 encoded string
	IsCorrect   bool   `json:"is_correct"`
	ChoiceType  uint   `json:"choice_type"`
	Slug        string `json:"choice_slug"`
}
