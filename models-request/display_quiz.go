package models_request

type ContentDisplay struct {
	ContentSlug        string                `json:"content_slug"`
	ContentType        string                `json:"content_type"`
	ContentName        string                `json:"content_name"`
	ContentDescription string                `json:"content_description"`
	QuizQuestions      []QuizQuestionDisplay `json:"quiz_questions"`
}

type QuizQuestionDisplay struct {
	QuestionSlug    string              `json:"quiz_slug"`
	QuestionTitle   string              `json:"quiz_title"`
	QuestionDetails string              `json:"quiz_details"`
	QuestionImage   string              `json:"quiz_image"`
	QuestionTime    string              `json:"quiz_time"`
	QuestionType    string              `json:"quiz_type"`
	QuestionChoices []QuizChoiceDisplay `json:"quiz_choices"`
}

type QuizChoiceDisplay struct {
	ChoiceSlug  string `json:"choice_slug"`
	Choice      string `json:"choice"`
	ChoiceImage string `json:"choice_image"`
	ChoiceType  uint   `json:"choice_type"`
}

type QuizAnswerSubmission struct {
	QuestionSlug string   `json:"question_slug" binding:"required"`
	ChoiceSlug   []string `json:"choice_slug" binding:"required"`
}

// Quiz submission response structures
type QuizCorrectResponse struct {
	QuestionSlug string   `json:"question_slug" binding:"required"`
	CorrectSlugs []string `json:"correct_slugs"`
	IsCorrect    bool     `json:"is_correct"`
}

// Overall response structure for quiz submission
type QuizSubmissionResponse struct {
	Results []QuizCorrectResponse `json:"results"`
}
