package routes

import (
	api_documents "med-api/api-documents" // ✅ เพิ่ม import นี้
	backend_routes "med-api/backend-routes"
	"med-api/middleware"

	"github.com/canvas-tech-horizon/notelink"
)

func SetupRoutes(app *notelink.ApiNote) {
	backend_routes.LoginRoutesApi(app) //SignUP's API docs routes

	// ✅ เพิ่ม Reset Password routes (ก่อน AuthMiddleware)
	api_documents.NoteRouteRequestOTP(app)
	api_documents.NoteRouteVerifyOTP(app)
	api_documents.NoteRouteResetPassword(app)

	app.Fiber().Use(middleware.AuthMiddleware())

	//app.Use(middleware.AuthMiddleware()) //Auth middleware for all routes below this line
	backend_routes.AuthProtectedRoutesApi(app) //Protected auth routes (like /me)
	backend_routes.UserApiRoutes(app)          //User's API docs routes
	backend_routes.CourseApiRoutes(app)        //Course's API docs routes
	//	backend_routes.QuestionApiRoutes(app)      //Question's API docs routes
	backend_routes.QuizApiRoutes(app)             // Quiz's API docs routes
	backend_routes.QuizQuestionApiRoutes(app)     // Quiz Question Display's API docs routes
	backend_routes.PathApiRoutes(app)             // Path's API docs routes
	backend_routes.AssignPathApiRoutes(app)       // Assign Path's API docs routes
	backend_routes.ExamApiRoutes(app)             // Exam's API docs routes
	backend_routes.ExamStatsApiRoutes(app)        // Exam Statistics's API docs routes
	backend_routes.CertificatesApiRoutes(app)     // Certificates API docs routes
	backend_routes.UserCourseApiRoutes(app)       // User Course API docs routes
	backend_routes.UserCourseDashboardRoutes(app) // User Course Dashboard's API docs routes
	backend_routes.UserPathDashboardRoutes(app)   // User Path Dashboard's API docs routes
	backend_routes.OverviewDashboardRoutes(app)   // Overview Dashboard's API docs routes
	backend_routes.CourseDisplayApiRoutes(app)    // Course Display's API docs routes
	backend_routes.ContentProgressApiRoutes(app)  // Content Progress's API docs routes
	backend_routes.TrackLearningApiRoutes(app)    // Track Learning's API docs routes
}
