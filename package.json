{"name": "e-med", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "start-network": "node network.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/noto-sans-thai": "^5.2.6", "@fortawesome/fontawesome-free": "^6.7.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/material": "^6.4.8", "@mui/styled-engine-sc": "^6.4.6", "@mui/x-data-grid": "^7.28.0", "@react-three/drei": "^10.4.2", "@react-three/fiber": "^9.1.4", "@tanstack/react-table": "^8.21.2", "@tiptap/core": "^2.23.1", "@tiptap/extension-blockquote": "^2.23.1", "@tiptap/extension-bullet-list": "^2.23.1", "@tiptap/extension-heading": "^2.23.1", "@tiptap/extension-highlight": "^2.23.1", "@tiptap/extension-history": "^2.23.1", "@tiptap/extension-image": "^2.23.1", "@tiptap/extension-link": "^2.23.1", "@tiptap/extension-list-item": "^2.23.1", "@tiptap/extension-ordered-list": "^2.23.1", "@tiptap/extension-placeholder": "^2.23.1", "@tiptap/extension-table": "^2.23.1", "@tiptap/extension-table-cell": "^2.23.1", "@tiptap/extension-table-header": "^2.23.1", "@tiptap/extension-table-row": "^2.23.1", "@tiptap/extension-text-align": "^2.23.1", "@tiptap/extension-underline": "^2.23.1", "@tiptap/pm": "^2.23.1", "@tiptap/react": "^2.23.1", "@tiptap/starter-kit": "^2.23.1", "@types/puppeteer": "^7.0.4", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fabric": "^5.3.0", "framer-motion": "^12.20.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "next": "15.0.3", "puppeteer": "^24.12.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-masonry-css": "^1.0.16", "react-player": "^2.16.0", "styled-components": "^6.1.16", "sweetalert2": "^11.22.1", "three": "^0.178.0", "tiptap-extension-resize-image": "^1.2.2"}, "devDependencies": {"@types/fabric": "^5.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.178.0", "@types/uuid": "^10.0.0", "daisyui": "^4.12.14", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "shadcn-ui": "^0.9.4", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}