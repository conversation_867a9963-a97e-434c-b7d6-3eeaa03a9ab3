package utils

import (
	"fmt"
	"net/smtp"
	"os"
)

func SendOTPEmail(email, otp string) error {
	// Email configuration จาก environment variables
	smtpHost := os.Getenv("SMTP_HOST")
	smtpPort := os.Getenv("SMTP_PORT")
	smtpUser := os.Getenv("SMTP_USER")
	smtpPass := os.Getenv("SMTP_PASS")

	// ตรวจสอบ environment variables
	if smtpHost == "" || smtpPort == "" || smtpUser == "" || smtpPass == "" {
		fmt.Printf("❌ Missing SMTP configuration in .env file\n")
		fmt.Printf("❌ Host: '%s', Port: '%s', User: '%s', Pass: '%s'\n", smtpHost, smtpPort, smtpUser, smtpPass)
		return fmt.Errorf("missing SMTP configuration")
	}

	fmt.Printf("📧 SMTP Config - Host: %s, Port: %s, User: %s\n", smtpHost, smtpPort, smtpUser)
	fmt.Printf("📧 Password length: %d characters\n", len(smtpPass))

	// Email content
	from := smtpUser
	to := []string{email}

	subject := "Subject: Password Reset OTP - Med API\r\n"
	mime := "MIME-version: 1.0;\nContent-Type: text/html; charset=\"UTF-8\";\n\n"

	body := fmt.Sprintf(`
		<!DOCTYPE html>
		<html lang="th">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>รีเซ็ตรหัสผ่าน - โรงพยาบาลสินแพทย์</title>
		</head>
		<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif; background: linear-gradient(135deg, #f0f4f8 0%%, #e8f1f5 50%%, #f2f8fc 100%%);">
			<table width="100%%" cellpadding="0" cellspacing="0" style="background: linear-gradient(135deg, #f0f4f8 0%%, #e8f1f5 50%%, #f2f8fc 100%%); min-height: 100vh; padding: 40px 0;">
				<tr>
					<td align="center" style="padding: 20px;">
						<!-- Main Container with Glass Effect -->
						<table width="520" cellpadding="0" cellspacing="0" style="background: rgba(255, 255, 255, 0.85); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08); overflow: hidden; max-width: 100%%;">
							
							<!-- Glass Header -->
							<tr>
								<td style="background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%%, rgba(25, 118, 210, 0.1) 100%%); padding: 50px 40px 40px; text-align: center; position: relative; overflow: hidden;">
									<!-- Subtle background pattern -->
									<div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30%% 20%%, rgba(46, 125, 50, 0.05) 0%%, transparent 50%%), radial-gradient(circle at 70%% 80%%, rgba(25, 118, 210, 0.05) 0%%, transparent 50%%); pointer-events: none;"></div>
									
									<h1 style="color: #1a1a1a; margin: 0; font-size: 32px; font-weight: 600; letter-spacing: -0.5px; position: relative; z-index: 1;">
										โรงพยาบาลสินแพทย์
									</h1>
									<p style="color: rgba(26, 26, 26, 0.7); margin: 8px 0 0; font-size: 17px; font-weight: 400; position: relative; z-index: 1;">
										ระบบ E-Medical
									</p>
								</td>
							</tr>
							
							<!-- Glass Content -->
							<tr>
								<td style="padding: 40px;">
									<!-- OTP Glass Card -->
									<div style="background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%%, rgba(25, 118, 210, 0.08) 100%%); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px; padding: 40px 32px; margin: 32px 0; text-align: center; box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06); position: relative; overflow: hidden;">
										<!-- Subtle glow -->
										<div style="position: absolute; top: -50%%; left: -50%%; width: 200%%; height: 200%%; background: radial-gradient(circle, rgba(46, 125, 50, 0.03) 0%%, transparent 70%%); pointer-events: none;"></div>
										
										<p style="color: rgba(26, 26, 26, 0.8); margin: 0 0 16px; font-size: 15px; font-weight: 500; position: relative; z-index: 1;">
											รหัส OTP ของคุณ
										</p>
										
										<!-- OTP Number -->
										<div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.4); border-radius: 16px; padding: 24px 32px; margin: 16px 0; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5), 0 2px 8px rgba(0, 0, 0, 0.04); position: relative; z-index: 1;">
											<h1 style="margin: 0; font-size: 42px; font-weight: 700; color: #2e7d32; letter-spacing: 6px; font-family: -apple-system, BlinkMacSystemFont, monospace;">
												%s
											</h1>
										</div>
										
										<!-- Timer -->
										<div style="background: rgba(255, 255, 255, 0.4); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 12px; padding: 12px 20px; margin: 16px auto 0; display: inline-block; position: relative; z-index: 1;">
											<p style="color: rgba(26, 26, 26, 0.8); margin: 0; font-size: 14px; font-weight: 500;">
												⏰ หมดอายุใน 10 นาที
											</p>
										</div>
									</div>
									
									<!-- Security Notice Glass Card -->
									<div style="background: rgba(255, 248, 225, 0.8); backdrop-filter: blur(10px); border: 1px solid rgba(255, 193, 7, 0.2); border-radius: 16px; padding: 24px; margin: 24px 0; box-shadow: 0 4px 16px rgba(255, 193, 7, 0.08);">
										<p style="color: #e65100; margin: 0; font-size: 14px; font-weight: 500; line-height: 1.5;">
											🛡️ <strong>หมายเหตุ:</strong> หากไม่ได้ทำการขอรีเซ็ตรหัสผ่าน กรุณาเพิกเฉยต่ออีเมลนี้
										</p>
									</div>
								</td>
							</tr>
							
							<!-- Glass Footer -->
							<tr>
								<td style="background: rgba(248, 249, 250, 0.8); backdrop-filter: blur(10px); border-top: 1px solid rgba(255, 255, 255, 0.2); padding: 32px; text-align: center;">
									<div style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 16px; padding: 24px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);">
										<h3 style="color: #2e7d32; margin: 0 0 8px; font-size: 20px; font-weight: 600; letter-spacing: -0.2px;">
											โรงพยาบาลสินแพทย์
										</h3>
										<p style="color: rgba(26, 26, 26, 0.7); margin: 0 0 16px; font-size: 14px; font-weight: 400;">
											เทคโนโลยีการแพทย์ที่ทันสมัย เพื่อสุขภาพที่ดีของคุณ
										</p>
										<p style="color: rgba(26, 26, 26, 0.6); margin: 0; font-size: 13px; font-weight: 400;">
											📞 02-XXX-XXXX | 📧 <EMAIL>
										</p>
									</div>
									<p style="color: rgba(26, 26, 26, 0.5); margin: 20px 0 0; font-size: 12px; font-weight: 400;">
										© 2025 โรงพยาบาลสินแพทย์ | ระบบอัตโนมัติ
									</p>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</body>
		</html>
	`, otp)

	message := []byte(subject + mime + body)

	// Gmail SMTP Authentication
	auth := smtp.PlainAuth("", smtpUser, smtpPass, smtpHost)

	// ส่งอีเมล
	err := smtp.SendMail(smtpHost+":"+smtpPort, auth, from, to, message)
	if err != nil {
		fmt.Printf("❌ SMTP Error: %v\n", err)
		return fmt.Errorf("failed to send email: %v", err)
	}

	fmt.Printf("✅ OTP email sent successfully to: %s\n", email)
	return nil
}
