package utils

import (
	"crypto/rand"
	"math/big"
	"med-api/db"
	"med-api/models"
	"time"
)

func GenerateOTP() string {
	// สร้าง OTP 6 หลัก แบบปลอดภัย
	const otpLength = 6
	const digits = "0123456789"

	otp := make([]byte, otpLength)
	for i := range otp {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(digits))))
		otp[i] = digits[num.Int64()]
	}

	return string(otp)
}

// ตรวจสอบ OTP ใน user_infos table
func IsOTPValid(email, otp string) bool {
	var user models.UserInfo
	err := db.DB.Where("email = ? AND otp = ? AND otp_used = false AND otp_expires_at > ?",
		email, otp, time.Now()).First(&user).Error
	return err == nil
}

// ทำเครื่องหมาย OTP ใช้แล้วใน user_infos table
func MarkOTPAsUsed(email, otp string) error {
	return db.DB.Model(&models.UserInfo{}).
		Where("email = ? AND otp = ?", email, otp).
		Update("otp_used", true).Error
}

// เช็คว่า OTP หมดอายุหรือยัง
func IsOTPExpired(email string) bool {
	var user models.UserInfo
	err := db.DB.Where("email = ? AND otp_expires_at < ?",
		email, time.Now()).First(&user).Error
	return err == nil
}

// ลบ OTP ที่หมดอายุ (optional - สำหรับ cleanup)
func CleanupExpiredOTPs() {
	db.DB.Model(&models.UserInfo{}).
		Where("otp_expires_at < ?", time.Now()).
		Updates(map[string]interface{}{
			"otp":            nil,
			"otp_expires_at": nil,
			"otp_used":       false,
		})
}
