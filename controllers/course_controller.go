package controllers

import (
	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	models_request "med-api/models-request"
	"med-api/utils"
	"strconv"
	"strings"

	"gorm.io/gorm"

	"github.com/gofiber/fiber/v2"
)

// Constants for content types
const (
	ContentTypeText  = 1
	ContentTypeVideo = 2
)

// Error messages
const (
	ErrLecturerNotFound      = "Lecturer not found"
	ErrCourseNotFound        = "Course not found"
	ErrCourseSlugRequired    = "Course slug is required"
	ErrFailedToCreateLesson  = "Failed to create lesson"
	ErrFailedToCreateContent = "Failed to create lesson content"
	ErrFailedToFetchLessons  = "Failed to fetch lessons"
	ErrFailedToFetchContent  = "Failed to fetch lesson content"
)

// Helper functions
func parseIntFromString(s string) int {
	if val, err := strconv.Atoi(s); err == nil {
		return val
	}
	return 0
}

func getContentTypeID(contentType string) uint {
	switch strings.ToLower(contentType) {
	case "video":
		return ContentTypeVideo
	default:
		return ContentTypeText
	}
}

func contains(slice []string, value string) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}

func CreateCourse(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var courseRequest models_request.RequestCourse
	if err := c.BodyParser(&courseRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	var user models.UserInfo

	// If user is a lecturer, use their own ID regardless of the request
	if strings.ToLower(userRole) == "lecturer" {
		if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
	} else {
		// For other roles (like admin), allow specifying the lecturer
		if err := db.DB.Where("slug = ?", courseRequest.Lecturer).First(&user).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": ErrLecturerNotFound})
		}
	}

	// Use transaction for data consistency
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create course
	newCourse := models.Courses{
		CourseName:        courseRequest.Title,
		CoursePicture:     []byte(courseRequest.CoverImage),
		CourseDescription: courseRequest.Description,
		CourseDifficulty:  courseRequest.Difficulty,
		CourseInstruction: courseRequest.ModuleDescription,
		CourseDuration:    strconv.Itoa(courseRequest.Duration),
		CourseStatus:      courseRequest.Status,
		CourseCertificate: courseRequest.Certify,
		LecturerID:        user.ID,
		Slug:              utils.GenerateUUIDSlug(),
	}

	if err := tx.Create(&newCourse).Error; err != nil {
		tx.Rollback()
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Create lessons and content
	for _, lessonReq := range courseRequest.Lessons {
		lesson := models.Lessons{
			LessonName:        lessonReq.Name,
			LessonTime:        strconv.Itoa(lessonReq.Time),
			LessonDescription: lessonReq.Description,
			CourseID:          newCourse.ID,
			LessonSlug:        utils.GenerateUUIDSlug(),
			LessonsOrder:      lessonReq.Order,
		}

		if err := tx.Create(&lesson).Error; err != nil {
			tx.Rollback()
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": ErrFailedToCreateLesson + ": " + err.Error()})
		}

		// Create lesson content
		for _, contentReq := range lessonReq.Content {
			content := models.ContentLesson{
				ContentDescription: contentReq.Details,
				ContentLessonName:  contentReq.Name,
				LessonTime:         contentReq.Time,
				LessonID:           lesson.ID,
				ContentSlug:        utils.GenerateUUIDSlug(),
				ContentLessonType:  getContentTypeID(contentReq.Type),
				ContentOrder:       contentReq.Order,
			}

			if err := tx.Create(&content).Error; err != nil {
				tx.Rollback()
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": ErrFailedToCreateContent + ": " + err.Error()})
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Course created successfully"})
}

func GetCourses(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var courses []models.Courses
	query := db.DB.Preload("Lecture.Role")

	// Filter courses if user is a lecturer
	if strings.ToLower(userRole) == "lecturer" {
		// Find the lecturer's ID by their slug
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Where("lecturer_id = ?", lecturer.ID)
	}

	if err := query.Find(&courses).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Pre-calculate counts for all courses in batch queries
	var courseIDs []uint
	for _, course := range courses {
		courseIDs = append(courseIDs, course.ID)
	}

	// Batch count lessons
	type LessonCount struct {
		CourseID uint
		Count    int64
	}
	var lessonCounts []LessonCount
	db.DB.Model(&models.Lessons{}).
		Select("course_id, COUNT(*) as count").
		Where("course_id IN ?", courseIDs).
		Group("course_id").
		Find(&lessonCounts)

	// Batch count students
	type StudentCount struct {
		CourseID uint
		Count    int64
	}
	var studentCounts []StudentCount
	db.DB.Model(&models.CourseStore{}).
		Select("course_id, COUNT(*) as count").
		Where("course_id IN ?", courseIDs).
		Group("course_id").
		Find(&studentCounts)

	// Create maps for quick lookup
	lessonCountMap := make(map[uint]int64)
	for _, lc := range lessonCounts {
		lessonCountMap[lc.CourseID] = lc.Count
	}

	studentCountMap := make(map[uint]int64)
	for _, sc := range studentCounts {
		studentCountMap[sc.CourseID] = sc.Count
	}

	// Build response
	response := make([]models_api.CourseResponse, 0, len(courses))
	for _, course := range courses {
		response = append(response, models_api.CourseResponse{
			Slug:              course.Slug,
			CourseName:        course.CourseName,
			CourseStatus:      course.CourseStatus,
			CourseCertificate: course.CourseCertificate,
			CourseDescription: course.CourseDescription,
			CourseDifficulty:  course.CourseDifficulty,
			CourseDuration:    course.CourseDuration,
			LecturerID:        course.LecturerID,
			Lecturer:          course.Lecture.FirstName + " " + course.Lecture.LastName,
			LessonAmount:      int(lessonCountMap[course.ID]),
			StudentCount:      int(studentCountMap[course.ID]),
		})
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

func GetCourseID(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	slug := c.Params("slug")
	if slug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": ErrCourseSlugRequired})
	}

	var course models.Courses
	query := db.DB.Preload("Lecture.Role").Where("slug = ?", slug)

	// Filter access if user is a lecturer
	if strings.ToLower(userRole) == "lecturer" {
		// Find the lecturer's ID by their slug
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Where("lecturer_id = ?", lecturer.ID)
	}

	if err := query.First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": ErrCourseNotFound})
	}

	// Get lessons with content in a single query
	var lessons []models.Lessons
	if err := db.DB.Where("course_id = ?", course.ID).Order("lessons_order ASC").Find(&lessons).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": ErrFailedToFetchLessons})
	}

	// Get all content for these lessons in one query
	var lessonIDs []uint
	for _, lesson := range lessons {
		lessonIDs = append(lessonIDs, lesson.ID)
	}

	var contents []models.ContentLesson
	if len(lessonIDs) > 0 {
		if err := db.DB.Preload("ContentType").Where("lesson_id IN ?", lessonIDs).Order("content_order ASC").Find(&contents).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": ErrFailedToFetchContent})
		}
	}

	// Group content by lesson ID
	contentByLesson := make(map[uint][]models.ContentLesson)
	for _, content := range contents {
		contentByLesson[content.LessonID] = append(contentByLesson[content.LessonID], content)
	}

	// Build lessons response
	lessonsResponse := make([]map[string]interface{}, 0, len(lessons))
	for _, lesson := range lessons {
		// Build content response for this lesson
		lessonContents := contentByLesson[lesson.ID]
		contentResponse := make([]map[string]interface{}, 0, len(lessonContents))

		for _, content := range lessonContents {
			contentType := "text"
			if content.ContentType.ID == ContentTypeVideo {
				contentType = "video"
			}

			contentResponse = append(contentResponse, map[string]interface{}{
				"slug":        content.ContentSlug,
				"name":        content.ContentLessonName,
				"typecontent": contentType,
				"details":     content.ContentDescription,
				"time":        content.LessonTime,
				"order":       content.ContentOrder,
			})
		}

		lessonsResponse = append(lessonsResponse, map[string]interface{}{
			"slug":        lesson.LessonSlug,
			"name":        lesson.LessonName,
			"description": lesson.LessonDescription,
			"time":        parseIntFromString(lesson.LessonTime),
			"content":     contentResponse,
			"order":       lesson.LessonsOrder,
		})
	}

	// Count students for this course
	var studentCount int64
	db.DB.Model(&models.CourseStore{}).Where("course_id = ?", course.ID).Count(&studentCount)

	// Build complete course response
	response := map[string]interface{}{
		"slug":               course.Slug,
		"course_name":        course.CourseName,
		"course_picture":     string(course.CoursePicture),
		"course_description": course.CourseDescription,
		"course_instruction": course.CourseInstruction,
		"course_difficulty":  course.CourseDifficulty,
		"course_duration":    course.CourseDuration,
		"course_status":      course.CourseStatus,
		"course_certificate": course.CourseCertificate,
		"user_id":            course.LecturerID,
		"lecturer":           course.Lecture.FirstName + " " + course.Lecture.LastName,
		"lecturer_slug":      course.Lecture.Slug,
		"lesson_amount":      len(lessons),
		"student_count":      int(studentCount),
		"lessons":            lessonsResponse,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

func UpdateCourse(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var req models_request.RequestCourse
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	slug := c.Params("slug")
	var course models.Courses
	query := db.DB.Where("slug = ?", slug)

	// Filter access if user is a lecturer
	if strings.ToLower(userRole) == "lecturer" {
		// Find the lecturer's ID by their slug
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Where("lecturer_id = ?", lecturer.ID)
	}

	if err := query.First(&course).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": ErrCourseNotFound})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	// Use transaction for data consistency
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update lecturer if provided and user is not a lecturer
	if req.Lecturer != "" && strings.ToLower(userRole) != "lecturer" {
		var lecturer models.UserInfo
		if err := tx.Where("slug = ?", req.Lecturer).First(&lecturer).Error; err != nil {
			tx.Rollback()
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": ErrLecturerNotFound})
		}
		course.LecturerID = lecturer.ID
	}

	// Update core course fields
	course.CourseName = req.Title
	course.CourseDescription = req.Description
	course.CourseInstruction = req.ModuleDescription
	course.CoursePicture = []byte(req.CoverImage)
	course.CourseDifficulty = req.Difficulty
	course.CourseDuration = strconv.Itoa(req.Duration)
	course.CourseStatus = req.Status
	course.CourseCertificate = req.Certify

	if err := tx.Model(&course).Updates(course).Error; err != nil {
		tx.Rollback()
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	// Collect new lesson slugs for deletion logic
	newLessonSlugs := make([]string, 0, len(req.Lessons))
	for _, lr := range req.Lessons {
		if lr.Slug != "" {
			newLessonSlugs = append(newLessonSlugs, lr.Slug)
		}
	}

	// Delete lessons that are not in the new data
	var oldLessons []models.Lessons
	tx.Where("course_id = ?", course.ID).Find(&oldLessons)
	for _, oldLesson := range oldLessons {
		if oldLesson.LessonSlug != "" && !contains(newLessonSlugs, oldLesson.LessonSlug) {
			// Delete lesson contents first
			tx.Where("lesson_id = ?", oldLesson.ID).Delete(&models.ContentLesson{})
			// Delete the lesson
			tx.Delete(&oldLesson)
		}
	}

	// Upsert lessons and their content
	for _, lr := range req.Lessons {
		var lesson models.Lessons
		isNewLesson := false

		if lr.Slug != "" {
			if err := tx.Where("lesson_slug = ? AND course_id = ?", lr.Slug, course.ID).First(&lesson).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					isNewLesson = true
				}
			}
		} else {
			isNewLesson = true
		}

		// Update lesson fields
		lesson.CourseID = course.ID
		lesson.LessonName = lr.Name
		lesson.LessonTime = strconv.Itoa(lr.Time)
		lesson.LessonDescription = lr.Description
		lesson.LessonsOrder = lr.Order

		if isNewLesson {
			lesson.LessonSlug = utils.GenerateUUIDSlug()
		}

		// Save or create lesson
		if lesson.ID != 0 {
			tx.Model(&lesson).Updates(lesson)
		} else {
			tx.Create(&lesson)
		}

		// Handle lesson content
		newContentSlugs := make([]string, 0, len(lr.Content))
		for _, cr := range lr.Content {
			if cr.Slug != "" {
				newContentSlugs = append(newContentSlugs, cr.Slug)
			}
		}

		// Delete content that are not in the new data
		var oldContents []models.ContentLesson
		tx.Where("lesson_id = ?", lesson.ID).Find(&oldContents)
		for _, oldContent := range oldContents {
			if oldContent.ContentSlug != "" && !contains(newContentSlugs, oldContent.ContentSlug) {
				tx.Delete(&oldContent)
			}
		}

		// Upsert each content item
		for _, cr := range lr.Content {
			var content models.ContentLesson
			isNewContent := false

			if cr.Slug != "" {
				if err := tx.Where("content_slug = ? AND lesson_id = ?", cr.Slug, lesson.ID).First(&content).Error; err != nil {
					if err == gorm.ErrRecordNotFound {
						isNewContent = true
					}
				}
			} else {
				isNewContent = true
			}

			// Update content fields
			content.LessonID = lesson.ID
			content.ContentLessonName = cr.Name
			content.ContentDescription = cr.Details
			content.LessonTime = cr.Time
			content.ContentOrder = cr.Order
			content.ContentLessonType = getContentTypeID(cr.Type)

			if isNewContent {
				content.ContentSlug = utils.GenerateUUIDSlug()
			}

			// Save or create content
			if content.ID != 0 {
				tx.Model(&content).Updates(content)
			} else {
				tx.Create(&content)
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Course updated successfully"})
}

func DeleteCourse(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	slug := c.Params("slug")
	if slug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": ErrCourseSlugRequired})
	}

	var course models.Courses
	query := db.DB.Where("slug = ?", slug)

	// Filter access if user is a lecturer
	if strings.ToLower(userRole) == "lecturer" {
		// Find the lecturer's ID by their slug
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Where("lecturer_id = ?", lecturer.ID)
	}

	if err := query.First(&course).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": ErrCourseNotFound})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	// Use transaction to ensure cascade deletion
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete associated content first
	if err := tx.Where("lesson_id IN (SELECT id FROM lessons WHERE course_id = ?)", course.ID).Delete(&models.ContentLesson{}).Error; err != nil {
		tx.Rollback()
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	// Delete associated lessons
	if err := tx.Where("course_id = ?", course.ID).Delete(&models.Lessons{}).Error; err != nil {
		tx.Rollback()
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	// Delete the course
	if err := tx.Delete(&course).Error; err != nil {
		tx.Rollback()
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	if err := tx.Commit().Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Course deleted successfully"})
}
