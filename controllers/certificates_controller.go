package controllers

import (
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"time"

	"github.com/gofiber/fiber/v2"
)

// GetCertificatesCourseByUserSlug returns course certificates for the authenticated user
func GetCertificatesCourseByUserSlug(c *fiber.Ctx) error {
	userSlug := c.Locals("user_slug").(string)
	courseSlug := c.Params("course_slug") // Get course slug from URL parameters

	// Get user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(404).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	// Get courses by courseSlug
	var courses models.Courses
	query := db.DB
	if courseSlug != "" {
		query = query.Where("slug = ?", courseSlug)
	}
	if err := query.First(&courses).Error; err != nil {
		return c.Status(404).JSON(fiber.Map{
			"error": "Course not found",
		})
	}

	var certificates models_request.CertificateCourse
	// Check if user has completed the course
	isCompleted := checkCourseCompletion(user.ID, courses.ID)
	if !isCompleted {
		return c.Status(403).JSON(fiber.Map{
			"error": "You have not completed this course",
		})
	}
	// Check if user has a certificate for this course
	var cert models.Certificate
	if err := db.DB.Where("user_id = ? AND course_id = ?", user.ID, courses.ID).
		First(&cert).Error; err != nil {
		// If no certificate found, create one
		cert = models.Certificate{
			UserID:          user.ID,
			CourseID:        courses.ID,
			CertificateDate: time.Now().Format("2006-01-02"),
		}
		if err := db.DB.Create(&cert).Error; err != nil {
			return c.Status(500).JSON(fiber.Map{
				"error": "Failed to create certificate",
			})
		}
	}

	// Prepare certificate response
	certificates = models_request.CertificateCourse{
		Username:      user.FirstName + " " + user.LastName,
		CourseName:    courses.CourseName,
		DateCertified: cert.CertificateDate,
	}

	return c.Status(200).JSON(certificates)
}

// GetCertificatesPathByUserSlug returns path certificates for the authenticated user
func GetCertificatesPathByUserSlug(c *fiber.Ctx) error {
	userSlug := c.Locals("user_slug").(string)
	pathSlug := c.Params("path_slug") // Get path slug from URL parameters

	// Get user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(404).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	var pathCertificates models_request.CertificatePath

	// Get paths
	var path models.CoursePath
	query := db.DB

	// If path slug is provided, filter by specific path
	if pathSlug != "" {
		// Note: CoursePath model doesn't have a slug field, so we'll filter by name
		// You may want to add a slug field to CoursePath model for better URL structure
		query = query.Where("slug = ?", pathSlug)
	}

	if err := query.Find(&path).Error; err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch paths",
		})
	}

	// Get all courses in this path
	var courseStores []models.CourseStore
	if err := db.DB.Where("course_path_id = ?", path.ID).
		Preload("Course").Find(&courseStores).Error; err != nil {
	}

	// Check if user has completed all courses in the path
	allCoursesCompleted := true
	var latestCertDate string

	for _, courseStore := range courseStores {
		if !courseStore.Course.CourseCertificate {
			continue // Skip courses that don't offer certificates
		}

		isCompleted := checkCourseCompletion(user.ID, courseStore.CourseID)
		print("Checking course completion for user ID: ", user.ID, " course ID: ", courseStore.CourseID, "\n")
		if !isCompleted {
			allCoursesCompleted = false
			break
		}

		print("isCompleted: ", isCompleted, "\n")

		// Check for existing certificate and get the date
		var cert models.Certificate
		if err := db.DB.Where("user_id = ? AND course_id = ?", user.ID, courseStore.CourseID).
			First(&cert).Error; err == nil {
			if cert.CertificateDate > latestCertDate {
				latestCertDate = cert.CertificateDate
			}
		}
	}

	// If all courses are completed, add path certificate
	if allCoursesCompleted && latestCertDate != "" {
		pathCertificates = models_request.CertificatePath{
			Username:      user.FirstName + " " + user.LastName,
			PathName:      path.Name,
			DateCertified: latestCertDate,
		}
	}

	return c.Status(200).JSON(pathCertificates)
}

// checkCourseCompletion checks if user has completed a course
// This includes checking progress and exam scores if applicable
func checkCourseCompletion(userID, courseID uint) bool {
	// Check if course has an exam
	var exam models.Exam
	hasExam := db.DB.Where("course_id = ?", courseID).First(&exam).Error == nil

	if hasExam {
		print("Course has an exam, checking exam record...\n")
		// If course has exam, check if user passed
		var examRecord models.ExamRecord
		if err := db.DB.Where("user_id = ? AND exam_id = ?", userID, exam.ID).
			First(&examRecord).Error; err != nil {
			return false // No exam record found
		}

		// Check if user passed the exam
		if examRecord.Score < exam.PassingScore {
			return false // Didn't pass the exam
		}
	}

	// Check course progress - user should have completed all lessons
	// Get all lessons in the course
	var lessons []models.Lessons
	if err := db.DB.Where("course_id = ?", courseID).Find(&lessons).Error; nil != err {
		return false
	}

	// print("Checking progress for all lessons...\n")

	// Check if user has progress for all lessons
	for _, lesson := range lessons {
		// Get all content lessons for this lesson
		var contentLessons []models.ContentLesson
		if err := db.DB.Where("lesson_id = ?", lesson.ID).Find(&contentLessons).Error; err != nil {
			continue
		}

		// Check if user has completed all content lessons
		for _, contentLesson := range contentLessons {
			// print("userID: ", userID, " checking content lesson ID: ", contentLesson.ID, "\n")
			var progress models.ContentProgress
			if err := db.DB.Where("user_id = ? AND content_lesson_id = ?", userID, contentLesson.ID).
				First(&progress).Error; err != nil {
				return false // No progress found for this content lesson
			}

			// // Check if lesson is marked as completed
			// if progress.LessonRecord != "completed" {
			// 	return false // Lesson not completed
			// }
		}
	}

	return true // All requirements met
}

// GetCertificatesByType returns certificates based on type with user slug authentication
func GetCertificatesByType(c *fiber.Ctx) error {
	certificateType := c.Query("type") // course or path

	switch certificateType {
	case "course":
		return GetCertificatesCourseByUserSlug(c)
	case "path":
		return GetCertificatesPathByUserSlug(c)
	default:
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid certificate type. Use 'course' or 'path'",
		})
	}
}
