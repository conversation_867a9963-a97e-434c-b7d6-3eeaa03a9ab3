package controllers

import (
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"

	"github.com/gofiber/fiber/v2"
)

// GetOverViewUserPath returns path overview data for authenticated user
// Returns total paths, completed paths, paths in progress, and average completion time
func GetOverViewUserPath(c *fiber.Ctx) error {
	userSlug := c.Locals("user_slug").(string)

	// Fetch user from database using slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get total assigned paths for user
	var totalPath int64
	if err := db.DB.Model(&models.AssignPath{}).Where("user_id = ?", user.ID).Count(&totalPath).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve total paths"})
	}

	// Calculate path completion statistics
	pathCompleted, pathInProgress, avgTime, err := calculatePathStatistics(user.ID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to calculate path statistics"})
	}

	// Create response
	overview := models_request.OverviewPath{
		TotalPath:           int(totalPath),
		TotalPathCompleted:  pathCompleted,
		TotalPathInProgress: pathInProgress,
		TotalAvgTime:        avgTime,
	}

	return c.Status(fiber.StatusOK).JSON(overview)
}

// GetOverViewUserPathBySlug returns path overview data for a specific user by slug
// This endpoint allows fetching overview data for any user by their slug (admin/lecturer use)
func GetOverViewUserPathBySlug(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	if userSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug is required"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get total assigned paths for user
	var totalPath int64
	if err := db.DB.Model(&models.AssignPath{}).Where("user_id = ?", user.ID).Count(&totalPath).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve total paths"})
	}

	// Calculate path completion statistics
	pathCompleted, pathInProgress, avgTime, err := calculatePathStatistics(user.ID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to calculate path statistics"})
	}

	// Create response
	overview := models_request.OverviewPath{
		TotalPath:           int(totalPath),
		TotalPathCompleted:  pathCompleted,
		TotalPathInProgress: pathInProgress,
		TotalAvgTime:        avgTime,
	}

	return c.Status(fiber.StatusOK).JSON(overview)
}

// calculatePathStatistics calculates completed paths, paths in progress, and average completion time
func calculatePathStatistics(userID uint) (int, int, int, error) {
	// Get user's assigned paths
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ?", userID).Find(&assignments).Error; err != nil {
		return 0, 0, 0, err
	}

	if len(assignments) == 0 {
		return 0, 0, 0, nil
	}

	var pathCompleted int
	var pathInProgress int
	var totalDuration int
	var completedPathsWithDuration int

	for _, assignment := range assignments {
		// Get courses in this path
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_path_id = ?", assignment.PathID).
			Preload("Course").Find(&courseStores).Error; err != nil {
			continue
		}

		if len(courseStores) == 0 {
			continue
		}

		// Calculate path completion status
		totalCourses := len(courseStores)
		completedCourses := 0
		pathDurationMinutes := 0
		hasProgress := false

		for _, cs := range courseStores {
			course := cs.Course

			// Get all content lessons for this course
			var totalContents int64
			db.DB.Model(&models.ContentLesson{}).
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ?", course.ID).
				Count(&totalContents)

			if totalContents == 0 {
				continue
			}

			// Get user's progress for this course
			var progressCount int64
			db.DB.Model(&models.ContentProgress{}).
				Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, userID).
				Count(&progressCount)

			if progressCount > 0 {
				hasProgress = true
			}

			// Check if course is completed (100% progress)
			if progressCount >= totalContents {
				completedCourses++
			}

			// Add course duration (assuming it's in minutes, default to 60 if not specified)
			courseDuration := 60
			if course.CourseDuration != "" {
				// You might want to parse this properly based on your duration format
				courseDuration = 60 // Default duration for now
			}
			pathDurationMinutes += courseDuration
		}

		// Determine path status
		if completedCourses == totalCourses && totalCourses > 0 {
			// Path is completed
			pathCompleted++
			totalDuration += pathDurationMinutes
			completedPathsWithDuration++
		} else if hasProgress {
			// Path has some progress but not completed
			pathInProgress++
		}
		// If no progress, path is neither completed nor in progress
	}

	// Calculate average time (in minutes)
	avgTime := 0
	if completedPathsWithDuration > 0 {
		avgTime = totalDuration / completedPathsWithDuration
	}

	return pathCompleted, pathInProgress, avgTime, nil
}
