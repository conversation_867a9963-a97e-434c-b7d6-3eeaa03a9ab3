package controllers

import (
	"med-api/db"
	"med-api/models"

	"github.com/gofiber/fiber/v2"
)

func GetQuestionsByQuizSlug(c *fiber.Ctx) error {
	slug := c.Params("slug")

	var quiz models.Quiz
	if err := db.DB.Where("slug = ?", slug).First(&quiz).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Quiz not found"})
	}

	var questions []models.Question
	if err := db.DB.Where("quiz_id = ?", quiz.ID).Find(&questions).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch questions"})
	}

	return c.Status(fiber.StatusOK).JSON(questions)
}
