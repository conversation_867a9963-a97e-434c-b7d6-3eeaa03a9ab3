package controllers

import (
	"math"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetExamStats returns a list of exams with statistics
func GetExamStats(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	// Get pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "5"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 5
	}

	offset := (page - 1) * limit

	// Get search query parameter
	search := c.Query("search", "")

	// Build base query for exams
	query := db.DB.Model(&models.Exam{}).
		Preload("Course")

	// Role-based filtering
	if userRole == "lecturer" {
		// Find the lecturer's ID by their slug
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Where("courses.lecturer_id = ?", lecturer.ID)
	} else {
		query = query.Preload("Course")
	}

	// Search filter
	if search != "" {
		query = query.Where("(exam_name ILIKE ? OR EXISTS (SELECT 1 FROM courses WHERE courses.id = exams.course_id AND courses.course_name ILIKE ?))",
			"%"+search+"%", "%"+search+"%")
	}

	// Count total items
	var totalItems int64
	if err := query.Count(&totalItems).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to count exams"})
	}

	// Get exams with pagination
	var exams []models.Exam
	if err := query.Order("id DESC").
		Limit(limit).Offset(offset).
		Find(&exams).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch exams"})
	}

	// Build response with statistics
	examOverviews := make([]models_request.ExamOverview, len(exams))
	for i, exam := range exams {
		// Get students assigned to this course
		var studentCount int64
		db.DB.Model(&models.AssignPath{}).
			Joins("JOIN course_stores ON assign_paths.path_id = course_stores.course_path_id").
			Where("course_stores.course_id = ?", exam.CourseID).
			Count(&studentCount)

		// Get exam attempt statistics
		var submittedCount int64
		var passedCount int64
		var failedCount int64
		var averageScore float64

		db.DB.Model(&models.ExamRecord{}).
			Where("exam_id = ?", exam.ID).
			Count(&submittedCount)

		db.DB.Model(&models.ExamRecord{}).
			Where("exam_id = ? AND score >= ?", exam.ID, exam.PassingScore).
			Count(&passedCount)

		db.DB.Model(&models.ExamRecord{}).
			Where("exam_id = ? AND score < ?", exam.ID, exam.PassingScore).
			Count(&failedCount)

		var avgResult struct {
			Avg *float64
		}
		db.DB.Model(&models.ExamRecord{}).
			Select("AVG(score) as avg").
			Where("exam_id = ?", exam.ID).
			Scan(&avgResult)

		if avgResult.Avg != nil {
			averageScore = *avgResult.Avg
		}

		// Determine exam status
		examStatus := "ปิด"
		if exam.ExamStatus {
			examStatus = "เปิด"
		}

		examOverviews[i] = models_request.ExamOverview{
			ExamSlug:       exam.Slug,
			ExamName:       exam.ExamName,
			CourseSlug:     exam.Course.Slug,
			CourseName:     exam.Course.CourseName,
			Status:         examStatus,
			StudentCount:   int(studentCount),
			SubmittedCount: int(submittedCount),
			PassedCount:    int(passedCount),
			FailedCount:    int(failedCount),
			AverageScore:   averageScore,
			MaxScore:       exam.PassingScore, // Assuming max score is stored elsewhere, using passing score for now
			PassingScore:   exam.PassingScore,
			CreatedAt:      time.Now(), // Exam model doesn't have timestamps, using current time
			UpdatedAt:      time.Now(),
		}
	}

	// Calculate pagination
	totalPages := int(math.Ceil(float64(totalItems) / float64(limit)))
	hasNext := page < totalPages
	hasPrevious := page > 1

	pagination := models_request.PaginationInfo{
		Page:        page,
		Limit:       limit,
		TotalItems:  int(totalItems),
		TotalPages:  totalPages,
		HasNext:     hasNext,
		HasPrevious: hasPrevious,
	}

	response := models_request.ExamStatsResponse{
		Exams:      examOverviews,
		Pagination: pagination,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// GetExamDetailStats returns detailed statistics for a specific exam
func GetExamDetailStats(c *fiber.Ctx) error {
	examSlug := c.Params("exam_slug")

	// Get pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// Get filter parameters
	search := c.Query("search", "")
	statusFilter := c.Query("status", "") // "passed", "failed", "not_taken"

	// Get exam details
	var exam models.Exam
	query := db.DB.Preload("Course").Where("slug = ?", examSlug)

	if err := query.First(&exam).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch exam"})
	}

	// Get total questions count
	var totalQuestions int64
	db.DB.Model(&models.ExamQuestion{}).
		Where("exam_id = ?", exam.ID).
		Count(&totalQuestions)

	// Calculate overall exam statistics
	var stats models_request.ExamStatistics

	// Get total students assigned to this course
	var totalStudents int64
	db.DB.Model(&models.AssignPath{}).
		Joins("JOIN course_stores ON assign_paths.path_id = course_stores.course_path_id").
		Where("course_stores.course_id = ?", exam.CourseID).
		Count(&totalStudents)

	stats.TotalStudents = int(totalStudents)

	// Get exam attempt statistics
	var submittedCount int64
	db.DB.Model(&models.ExamRecord{}).
		Where("exam_id = ?", exam.ID).
		Count(&submittedCount)
	stats.SubmittedCount = int(submittedCount)

	var passedCount int64
	db.DB.Model(&models.ExamRecord{}).
		Where("exam_id = ? AND score >= ?", exam.ID, exam.PassingScore).
		Count(&passedCount)
	stats.PassedCount = int(passedCount)

	var failedCount int64
	db.DB.Model(&models.ExamRecord{}).
		Where("exam_id = ? AND score < ?", exam.ID, exam.PassingScore).
		Count(&failedCount)
	stats.FailedCount = int(failedCount)

	stats.NotTakenCount = stats.TotalStudents - stats.SubmittedCount

	// Get score statistics
	var scoreStats struct {
		Avg *float64
		Max *int
		Min *int
	}
	db.DB.Model(&models.ExamRecord{}).
		Select("AVG(score) as avg, MAX(score) as max, MIN(score) as min").
		Where("exam_id = ?", exam.ID).
		Scan(&scoreStats)

	if scoreStats.Avg != nil {
		stats.AverageScore = *scoreStats.Avg
	}
	if scoreStats.Max != nil {
		stats.HighestScore = *scoreStats.Max
	}
	if scoreStats.Min != nil {
		stats.LowestScore = *scoreStats.Min
	}

	// Calculate rates
	if stats.TotalStudents > 0 {
		stats.PassRate = float64(stats.PassedCount) / float64(stats.TotalStudents) * 100
		stats.CompletionRate = float64(stats.SubmittedCount) / float64(stats.TotalStudents) * 100
	}

	// Get all users assigned to this course
	var allUsers []models.UserInfo
	if err := db.DB.Distinct().
		Joins("JOIN assign_paths ON assign_paths.user_id = user_infos.id").
		Joins("JOIN course_stores ON course_stores.course_path_id = assign_paths.path_id").
		Where("course_stores.course_id = ?", exam.CourseID).
		Find(&allUsers).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch students"})
	}

	// Build student results
	var filteredStudents []models_request.StudentExamResult

	for _, user := range allUsers {
		// Apply search filter
		if search != "" {
			searchLower := search
			if !(user.FirstName+" "+user.LastName == searchLower ||
				user.FirstName == searchLower ||
				user.LastName == searchLower) {
				continue
			}
		}

		// Get exam record for this user
		var examRecord models.ExamRecord
		hasRecord := db.DB.Where("exam_id = ? AND user_id = ?", exam.ID, user.ID).First(&examRecord).Error == nil

		var score *int
		var percentage *float64
		var completedAt *time.Time
		status := "not_taken"
		isPassed := false

		if hasRecord {
			score = &examRecord.Score
			completedAt = &examRecord.SubmitAt // Now using actual submission timestamp

			if exam.PassingScore > 0 {
				pct := float64(examRecord.Score) / float64(exam.PassingScore) * 100
				percentage = &pct
			}

			if examRecord.Score >= exam.PassingScore {
				status = "passed"
				isPassed = true
			} else {
				status = "failed"
			}
		}

		// Apply status filter
		if statusFilter != "" && statusFilter != status {
			continue
		}

		// Convert user picture to string
		avatar := ""
		if len(user.Picture) > 0 {
			avatar = string(user.Picture)
		}

		studentResult := models_request.StudentExamResult{
			UserSlug:      user.Slug,
			Name:          user.FirstName + " " + user.LastName,
			FirstName:     user.FirstName,
			LastName:      user.LastName,
			Position:      user.Position,
			Avatar:        avatar,
			Score:         score,
			MaxScore:      exam.PassingScore, // Using passing score as max score
			Percentage:    percentage,
			Status:        status,
			AttemptCount:  1, // Assuming single attempt, adjust if multiple attempts are allowed
			LastAttemptAt: completedAt,
			CompletedAt:   completedAt,
			TimeSpent:     nil, // Not stored in current model
			IsPassed:      isPassed,
		}

		filteredStudents = append(filteredStudents, studentResult)
	}

	// Apply pagination to filtered results
	totalFiltered := len(filteredStudents)
	totalPages := int(math.Ceil(float64(totalFiltered) / float64(limit)))

	start := offset
	end := offset + limit
	if start > totalFiltered {
		start = totalFiltered
	}
	if end > totalFiltered {
		end = totalFiltered
	}

	paginatedStudents := []models_request.StudentExamResult{}
	if start < end {
		paginatedStudents = filteredStudents[start:end]
	}

	// Pagination info
	pagination := models_request.PaginationInfo{
		Page:        page,
		Limit:       limit,
		TotalItems:  totalFiltered,
		TotalPages:  totalPages,
		HasNext:     page < totalPages,
		HasPrevious: page > 1,
	}

	// Determine exam status
	examStatus := "ปิด"
	if exam.ExamStatus {
		examStatus = "เปิด"
	}

	response := models_request.ExamDetailStats{
		ExamSlug:       exam.Slug,
		ExamName:       exam.ExamName,
		CourseSlug:     exam.Course.Slug,
		CourseName:     exam.Course.CourseName,
		Status:         examStatus,
		TotalQuestions: int(totalQuestions),
		MaxScore:       exam.PassingScore, // Using passing score as max score
		PassingScore:   exam.PassingScore,
		ExamDuration:   exam.ExamTime,
		Statistics:     stats,
		Students:       paginatedStudents,
		Pagination:     pagination,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// GetExamTrackingDetails returns detailed exam tracking data with student attempts
func GetExamTrackingDetails(c *fiber.Ctx) error {
	examSlug := c.Params("exam_slug")

	// Get exam details
	var exam models.Exam
	query := db.DB.Preload("Course").Where("slug = ?", examSlug)

	if err := query.First(&exam).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch exam"})
	}

	// Get all users assigned to this course
	var allUsers []models.UserInfo
	if err := db.DB.Distinct().
		Joins("JOIN assign_paths ON assign_paths.user_id = user_infos.id").
		Joins("JOIN course_stores ON course_stores.course_path_id = assign_paths.path_id").
		Where("course_stores.course_id = ?", exam.CourseID).
		Find(&allUsers).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch students"})
	}

	// Build student statistics with attempts
	var students []models_request.StudentExamStats

	for _, user := range allUsers {
		// Get all exam records for this user and exam (in case multiple attempts are allowed)
		var examRecords []models.ExamRecord
		db.DB.Where("exam_id = ? AND user_id = ?", exam.ID, user.ID).
			Order("id ASC").
			Find(&examRecords)

		var attempts []models_request.ExamAttempt
		bestScore := 0
		status := "ไม่ผ่าน"

		// Process each attempt
		for i, record := range examRecords {
			attemptStatus := "ไม่ผ่าน"
			if record.Score >= exam.PassingScore {
				attemptStatus = "ผ่าน"
			}

			if record.Score > bestScore {
				bestScore = record.Score
				if attemptStatus == "ผ่าน" {
					status = "ผ่าน"
				}
			}

			// Mock time spent and submitted date since it's not in the current model
			timeSpent := "25 นาที"
			submittedAt := "28 ธ.ค. 2567 09:30"

			attempt := models_request.ExamAttempt{
				AttemptNumber: i + 1,
				Score:         record.Score,
				TotalScore:    exam.PassingScore, // Using passing score as total
				Status:        attemptStatus,
				TimeSpent:     timeSpent,
				SubmittedAt:   submittedAt,
			}
			attempts = append(attempts, attempt)
		}

		// Always include the student, even if no attempts
		studentStats := models_request.StudentExamStats{
			ID:        user.Slug,
			Name:      user.FirstName + " " + user.LastName,
			Position:  user.Position,
			Status:    status,
			BestScore: bestScore,
			Attempts:  attempts,
		}
		students = append(students, studentStats)
	}

	response := models_request.ExamTrackData{
		ExamName: exam.ExamName,
		Students: students,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}
