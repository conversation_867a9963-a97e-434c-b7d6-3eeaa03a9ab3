package controllers

import (
	"fmt"
	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	"med-api/utils"
	"time"

	"github.com/gofiber/fiber/v2"
)

func RequestOTP(c *fiber.Ctx) error {
	var input models_api.RequestOTPRequest
	if err := c.BodyParser(&input); err != nil {
		fmt.Printf("❌ Body Parser Error: %v\n", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid input"})
	}

	fmt.Printf("📧 Email received: %s\n", input.Email)

	// ตรวจสอบว่า email มีในระบบไหม
	var user models.UserInfo
	if err := db.DB.Where("email = ?", input.Email).First(&user).Error; err != nil {
		fmt.Printf("❌ User not found for email: %s, Error: %v\n", input.Email, err)
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Email not found"})
	}

	fmt.Printf("✅ User found: %s\n", user.Username)

	// สร้าง OTP
	otp := utils.GenerateOTP()
	expiresAt := time.Now().Add(10 * time.Minute)

	fmt.Printf("🔑 Generated OTP: %s\n", otp)

	// อัพเดท OTP ใน user record เดิม
	if err := db.DB.Model(&user).Updates(map[string]interface{}{
		"otp":            otp,
		"otp_expires_at": expiresAt,
		"otp_used":       false,
	}).Error; err != nil {
		fmt.Printf("❌ Database Update Error: %v\n", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Failed to create OTP",
			"details": err.Error(),
		})
	}

	fmt.Printf("✅ OTP record updated successfully\n")

	// ✅ ส่งอีเมล
	fmt.Printf("📤 Sending OTP email to: %s\n", input.Email)
	if err := utils.SendOTPEmail(input.Email, otp); err != nil {
		fmt.Printf("❌ Email failed: %v\n", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "Failed to send email",
			"details": err.Error(),
		})
	}

	fmt.Printf("✅ Email sent successfully\n")
	fmt.Printf("✅ OTP created for %s: %s\n", input.Email, otp)

	return c.Status(fiber.StatusOK).JSON(models_api.OTPResponse{
		Message: "OTP sent to your email successfully",
		Success: true,
	})
}

func VerifyOTP(c *fiber.Ctx) error {
	var input models_api.VerifyOTPRequest
	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid input"})
	}

	fmt.Printf("🔍 Verifying OTP for: %s with OTP: %s\n", input.Email, input.OTP)

	// ตรวจสอบ OTP ใน user table
	var user models.UserInfo
	if err := db.DB.Where("email = ? AND otp = ? AND otp_used = false AND otp_expires_at > ?",
		input.Email, input.OTP, time.Now()).First(&user).Error; err != nil {
		fmt.Printf("❌ OTP verification failed: %v\n", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid or expired OTP"})
	}

	fmt.Printf("✅ OTP verified successfully for: %s\n", input.Email)

	return c.Status(fiber.StatusOK).JSON(models_api.OTPResponse{
		Message: "OTP verified successfully",
		Success: true,
	})
}

func ResetPassword(c *fiber.Ctx) error {
	var input models_api.ResetPasswordRequest
	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid input"})
	}

	// ตรวจสอบรหัสผ่านตรงกันไหม
	if input.NewPassword != input.ConfirmPassword {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Passwords do not match"})
	}

	fmt.Printf("🔄 Resetting password for: %s\n", input.Email)

	// ตรวจสอบ OTP ใน user table
	var user models.UserInfo
	if err := db.DB.Where("email = ? AND otp = ? AND otp_used = false AND otp_expires_at > ?",
		input.Email, input.OTP, time.Now()).First(&user).Error; err != nil {
		fmt.Printf("❌ OTP verification failed for password reset: %v\n", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid or expired OTP"})
	}

	// Hash รหัสผ่านใหม่
	hashedPassword, err := utils.HashPassword(input.NewPassword)
	if err != nil {
		fmt.Printf("❌ Password hashing failed: %v\n", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to hash password"})
	}

	// อัพเดทรหัสผ่านและทำเครื่องหมาย OTP ใช้แล้ว
	if err := db.DB.Model(&user).Updates(map[string]interface{}{
		"password": hashedPassword,
		"otp_used": true,
	}).Error; err != nil {
		fmt.Printf("❌ Password update failed: %v\n", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update password"})
	}

	fmt.Printf("✅ Password reset successful for %s\n", input.Email)

	return c.Status(fiber.StatusOK).JSON(models_api.OTPResponse{
		Message: "Password reset successfully",
		Success: true,
	})
}
