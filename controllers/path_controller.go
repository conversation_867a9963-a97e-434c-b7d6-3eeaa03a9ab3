package controllers

import (
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"med-api/utils"

	"github.com/gofiber/fiber/v2"
)

func CreatePath(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var pathRequest models_request.CreatePathRequest

	if err := c.BodyParser(&pathRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	if pathRequest.Name == "" || pathRequest.Description == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Name, Description, and Status are required"})
	}

	newPath := models.CoursePath{
		Name:            pathRequest.Name,
		PathDescription: pathRequest.Description,
		Slug:            utils.GenerateUUIDSlug(), // Generate a unique slug for the path
		Status:          pathRequest.Status,
	}

	if err := db.DB.Omit("ID").Create(&newPath).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create path"})
	}

	if len(pathRequest.Path) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Path cannot be empty"})
	}

	var courses []models.Courses
	var ids []string
	for _, p := range pathRequest.Path {
		ids = append(ids, p.IdCourse)
	}

	if len(ids) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "No course IDs provided"})
	}

	// Filter courses based on user role
	if userRole == "lecturer" {
		// Lecturers can only use their own courses
		if err := db.DB.Where("slug IN (?) AND lecturer_id = (SELECT id FROM user_infos WHERE slug = ?)", ids, userSlug).Find(&courses).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to find courses"})
		}
	} else {
		// Non-lecturers can use any courses
		if err := db.DB.Where("slug IN (?)", ids).Find(&courses).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to find courses"})
		}
	}

	// Create a CourseStore for each course in the path
	for _, p := range pathRequest.Path {
		var foundCourse *models.Courses
		for _, c := range courses {
			if c.Slug == p.IdCourse {
				foundCourse = &c
				break
			}
		}
		if foundCourse == nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid course ID or access denied", "course_id": p.IdCourse})
		}
		courseStore := models.CourseStore{
			CourseID:     foundCourse.ID,
			CoursePathID: newPath.ID,
			StoreOrder:   p.PathOrder,
		}
		if err := db.DB.Create(&courseStore).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create course store", "course_id": p.IdCourse})
		}
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Path created successfully", "path_id": newPath.ID})
}

// Get all paths with their Path[] data
func GetAllPaths(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var paths []models.CoursePath

	if userRole == "lecturer" {
		// Filter paths that contain courses created by this lecturer
		if err := db.DB.Joins("JOIN course_stores ON course_stores.course_path_id = course_paths.id").
			Joins("JOIN courses ON courses.id = course_stores.course_id").
			Joins("JOIN user_infos ON user_infos.id = courses.lecturer_id").
			Where("user_infos.slug = ?", userSlug).
			Distinct().
			Find(&paths).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get paths"})
		}
	} else {
		// For non-lecturer roles, show all paths
		if err := db.DB.Find(&paths).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get paths"})
		}
	}

	var result []map[string]interface{}
	for _, path := range paths {
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_path_id = ?", path.ID).Preload("Course").Find(&courseStores).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get path data"})
		}
		var pathArr []map[string]interface{}
		for _, cs := range courseStores {
			pathArr = append(pathArr, map[string]interface{}{
				"course_id":   cs.Course.Slug,
				"course_name": cs.Course.CourseName,
				"store_order": cs.StoreOrder,
			})
		}
		result = append(result, map[string]interface{}{
			"id":          path.ID,
			"name":        path.Name,
			"description": path.PathDescription,
			"status":      path.Status,
			"path":        pathArr,
		})
	}
	return c.Status(fiber.StatusOK).JSON(result)
}

func GetPathTable(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var paths []models.CoursePath

	if userRole == "lecturer" {
		// Filter paths that contain courses created by this lecturer
		if err := db.DB.Joins("JOIN course_stores ON course_stores.course_path_id = course_paths.id").
			Joins("JOIN courses ON courses.id = course_stores.course_id").
			Joins("JOIN user_infos ON user_infos.id = courses.lecturer_id").
			Where("user_infos.slug = ?", userSlug).
			Distinct().
			Find(&paths).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get paths"})
		}
	} else {
		// For non-lecturer roles, show all paths
		if err := db.DB.Find(&paths).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get paths"})
		}
	}

	var result []models_request.LearningPathResponseTable
	for _, path := range paths {
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_path_id = ?", path.ID).Preload("Course").Find(&courseStores).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get path data"})
		}
		var pathArr []models_request.Course
		for _, cs := range courseStores {
			pathArr = append(pathArr, models_request.Course{
				CourseID:   cs.Course.Slug,
				CourseName: cs.Course.CourseName,
			})
		}
		result = append(result, models_request.LearningPathResponseTable{
			ID:          int(path.ID),
			Name:        path.Name,
			Description: path.PathDescription,
			Status:      path.Status,
			Amount:      len(pathArr),
		})
	}
	return c.Status(fiber.StatusOK).JSON(result)
}

// Get path by ID with Path[] data
func GetPathByID(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)
	id := c.Params("id")

	var path models.CoursePath

	if userRole == "lecturer" {
		// Check if this path contains courses created by this lecturer
		if err := db.DB.Joins("JOIN course_stores ON course_stores.course_path_id = course_paths.id").
			Joins("JOIN courses ON courses.id = course_stores.course_id").
			Joins("JOIN user_infos ON user_infos.id = courses.lecturer_id").
			Where("course_paths.id = ? AND user_infos.slug = ?", id, userSlug).
			First(&path).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found or access denied"})
		}
	} else {
		// For non-lecturer roles, show any path
		if err := db.DB.First(&path, id).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found"})
		}
	}
	var courseStores []models.CourseStore
	if err := db.DB.Where("course_path_id = ?", path.ID).Preload("Course").Find(&courseStores).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get path data"})
	}
	var pathArr []map[string]interface{}
	for _, cs := range courseStores {
		pathArr = append(pathArr, map[string]interface{}{
			"course_id":   cs.Course.Slug,
			"course_name": cs.Course.CourseName,
			"store_order": cs.StoreOrder,
		})
	}
	// order the path by StoreOrder
	for i := 0; i < len(pathArr)-1; i++ {
		for j := i + 1; j < len(pathArr); j++ {
			if pathArr[i]["store_order"].(int) > pathArr[j]["store_order"].(int) {
				pathArr[i], pathArr[j] = pathArr[j], pathArr[i]
			}
		}
	}
	result := map[string]interface{}{
		"id":          path.ID,
		"name":        path.Name,
		"description": path.PathDescription,
		"status":      path.Status,
		"path":        pathArr,
	}
	return c.Status(fiber.StatusOK).JSON(result)
}

// Update path by ID and its Path[]
func UpdatePathByID(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)
	id := c.Params("id")

	var path models.CoursePath

	if userRole == "lecturer" {
		// Check if this path contains courses created by this lecturer
		if err := db.DB.Joins("JOIN course_stores ON course_stores.course_path_id = course_paths.id").
			Joins("JOIN courses ON courses.id = course_stores.course_id").
			Joins("JOIN user_infos ON user_infos.id = courses.lecturer_id").
			Where("course_paths.id = ? AND user_infos.slug = ?", id, userSlug).
			First(&path).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found or access denied"})
		}
	} else {
		// For non-lecturer roles, allow updating any path
		if err := db.DB.First(&path, id).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found"})
		}
	}

	var updateReq models_request.CreatePathRequest
	if err := c.BodyParser(&updateReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	path.Name = updateReq.Name
	path.PathDescription = updateReq.Description
	path.Status = updateReq.Status

	if err := db.DB.Save(&path).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update path"})
	}

	// Update Path[]: remove old, add new
	if err := db.DB.Where("course_path_id = ?", path.ID).Delete(&models.CourseStore{}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update path data"})
	}
	if len(updateReq.Path) > 0 {
		var courses []models.Courses
		var ids []string
		for _, p := range updateReq.Path {
			ids = append(ids, p.IdCourse)
		}

		// Filter courses based on user role
		if userRole == "lecturer" {
			// Lecturers can only use their own courses
			if err := db.DB.Where("slug IN (?) AND lecturer_id = (SELECT id FROM user_infos WHERE slug = ?)", ids, userSlug).Find(&courses).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to find courses"})
			}
		} else {
			// Non-lecturers can use any courses
			if err := db.DB.Where("slug IN (?)", ids).Find(&courses).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to find courses"})
			}
		}
		for _, p := range updateReq.Path {
			for _, c := range courses {
				if c.Slug == p.IdCourse {
					cs := models.CourseStore{
						CoursePathID: path.ID,
						CourseID:     c.ID,
						StoreOrder:   p.PathOrder,
					}
					db.DB.Create(&cs)
				}
			}
		}
	}

	return GetPathByID(c)
}

// Delete path by ID
func DeletePathByID(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)
	id := c.Params("id")

	var path models.CoursePath

	if userRole == "lecturer" {
		// Check if this path contains courses created by this lecturer
		if err := db.DB.Joins("JOIN course_stores ON course_stores.course_path_id = course_paths.id").
			Joins("JOIN courses ON courses.id = course_stores.course_id").
			Joins("JOIN user_infos ON user_infos.id = courses.lecturer_id").
			Where("course_paths.id = ? AND user_infos.slug = ?", id, userSlug).
			First(&path).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found or access denied"})
		}
	} else {
		// For non-lecturer roles, allow deleting any path
		if err := db.DB.First(&path, id).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found"})
		}
	}
	// Delete related course stores first to avoid foreign key constraint error
	if err := db.DB.Where("course_path_id = ?", id).Delete(&models.CourseStore{}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete related course stores"})
	}
	if err := db.DB.Delete(&path).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete path"})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Path deleted successfully"})
}
