package controllers

import (
	"encoding/base64"
	"errors"
	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	models_request "med-api/models-request"
	"med-api/utils"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/gofiber/fiber/v2"
)

func CreateUsers(c *fiber.Ctx) error {
	var user models_api.CreateUser

	if err := c.BodyParser(&user); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}
	hashedPassword, err := utils.HashPassword(user.Password)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to hash password",
		})
	}

	user.Password = hashedPassword

	// Lower-case names to normalize
	first := strings.ToLower(user.FirstName)

	// Convert LastName to runes so slicing works with Unicode
	lastRunes := []rune(strings.ToLower(user.LastName))

	// Grab up to 4 runes (or fewer if LastName is shorter)
	var lastPart string
	if len(lastRunes) >= 4 {
		lastPart = string(lastRunes[:4])
	} else {
		lastPart = string(lastRunes)
	}

	CreateUser := models.UserInfo{
		Username:  first + " " + lastPart,
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Picture:   []byte(user.Picture),
		Email:     user.Email,
		Position:  user.Position,
		LoginDate: time.Now().Format("2006-01-02 15:04:05"),
		RoleID:    uint(user.Role),
		Slug:      utils.GenerateUUIDSlug(),
	}

	if err := db.DB.Where("email = ?", user.Email).First(&CreateUser).Error; err == nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Email already exists"})
	}

	if err := db.DB.Create(&CreateUser).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Created successful"})
}

func GetUserTable(c *fiber.Ctx) error {
	var users []models.UserInfo
	userRole := c.Locals("role").(string)

	// Filter data based on user role
	query := db.DB.Preload("Role")
	if userRole == "lecturer" {
		// Lecturers can only see other lecturers and students, not admins
		query = query.Joins("JOIN roles ON user_infos.role_id = roles.id").
			Where("roles.name IN ?", []string{"lecturer", "student"})
	}

	if err := query.Find(&users).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	var allUsersResponse []models_request.UserTableRespond
	for _, user := range users {
		allUsersResponse = append(allUsersResponse, models_request.UserTableRespond{
			Slug:      user.Slug,
			FirstName: user.FirstName,
			LastName:  user.LastName,
			Picture:   string(user.Picture),
			Email:     user.Email,
			Position:  user.Position,
			Role:      user.Role.Name,
			Status:    user.Status,
		})
	}

	return c.Status(fiber.StatusOK).JSON(allUsersResponse)
}

func GetRole(c *fiber.Ctx) error {
	var roles []models.Role
	if err := db.DB.Find(&roles).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusOK).JSON(roles)
}

func GetUsers(c *fiber.Ctx) error {
	var users []models.UserInfo
	var allUsersResponse []models_api.AllUsersRespond
	userRole := c.Locals("role").(string)

	// Filter data based on user role
	query := db.DB.Preload("Role")
	if userRole == "lecturer" {
		// Lecturers can only see other lecturers and students, not admins
		query = query.Joins("JOIN roles ON user_infos.role_id = roles.id").
			Where("roles.name IN ?", []string{"lecturer", "student"})
	}

	if err := query.Find(&users).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	for _, user := range users {
		allUsersResponse = append(allUsersResponse, models_api.AllUsersRespond{
			ID:        user.ID,
			Picture:   string(user.Picture),
			FirstName: user.FirstName,
			LastName:  user.LastName,
			Email:     user.Email,
			Position:  user.Position,
			LoginDate: user.LoginDate,
			Status: func(b bool) int {
				if b {
					return 1
				} else {
					return 0
				}
			}(user.Status),
			Role: int(user.Role.ID),
			Slug: user.Slug,
		})
	}

	return c.Status(fiber.StatusOK).JSON(allUsersResponse)
}

func GetUserID(c *fiber.Ctx) error {
	var user models.UserInfo

	slug := c.Params("slug")
	userRole := c.Locals("role").(string)

	// Build query based on user role
	query := db.DB.Preload("Role")
	if userRole == "lecturer" {
		// Lecturers can only access other lecturers and students, not admins
		query = query.Joins("JOIN roles ON user_infos.role_id = roles.id").
			Where("roles.name IN ?", []string{"lecturer", "student"})
	}

	if err := query.Where("slug = ?", slug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Item not found"})
	}

	if userRole == "admin" {
		oneUser := models_api.UserRespond{
			Slug:      user.Slug,
			Picture:   string(user.Picture),
			Password:  user.Password,
			FirstName: user.FirstName,
			LastName:  user.LastName,
			Position:  user.Position,
			Email:     user.Email,
			Role: func(u uint) *int {
				i := int(u)
				return &i
			}(user.Role.ID),
			Status: func(b bool) *int {
				i := 0
				if b {
					i = 1
				}
				return &i
			}(user.Status),
			LoginDate: &user.LoginDate,
		}
		return c.Status(fiber.StatusOK).JSON(oneUser)
	}

	oneUser := models_api.UserRespond{
		Picture:   string(user.Picture),
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
	}
	return c.Status(fiber.StatusOK).JSON(oneUser)
}

func UpdateUser(c *fiber.Ctx) error {
	var user models_api.CreateUser

	slug := c.Params("slug")
	userRole := c.Locals("role").(string)

	if err := c.BodyParser(&user); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Check if lecturer is trying to update an admin user
	if userRole == "lecturer" {
		var targetUser models.UserInfo
		if err := db.DB.Preload("Role").Where("slug = ?", slug).First(&targetUser).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
		}

		// Lecturers cannot update admin users
		if targetUser.Role.Name == "admin" {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "Access denied: Cannot update admin user"})
		}
	}

	hashedPassword, err := utils.HashPassword(user.Password)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to hash password",
		})
	}

	user.Password = hashedPassword

	var pictureStr []byte
	if user.Picture != "" {
		decodedPicture, err := base64.StdEncoding.DecodeString(user.Picture)
		if err != nil {
			pictureStr = []byte(user.Picture) // Fallback to original string if decoding fails
		} else {
			pictureStr = decodedPicture
		}
	}

	updated_user := models.UserInfo{
		Picture:   pictureStr,
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
		Position:  user.Position,
		RoleID:    uint(user.Role),
		Status:    user.Status == 1,
	}

	if err := db.DB.Model(&models.UserInfo{}).Where("slug = ?", slug).Updates(updated_user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update user"})
	}

	if userRole == "admin" {
		oneUser := models_api.UserRespond{
			Picture:   string(updated_user.Picture),
			Password:  updated_user.Password,
			FirstName: updated_user.FirstName,
			LastName:  updated_user.LastName,
			Email:     updated_user.Email,
			Position:  updated_user.Position,
			Role: func(u uint) *int {
				i := int(u)
				return &i
			}(updated_user.RoleID),
			Status: func(b bool) *int {
				i := 0
				if b {
					i = 1
				}
				return &i
			}(updated_user.Status),
			LoginDate: &updated_user.LoginDate,
		}
		return c.Status(fiber.StatusOK).JSON(oneUser)
	}

	oneUser := models_api.UserRespond{
		Picture:   string(updated_user.Picture),
		Password:  updated_user.Password,
		FirstName: updated_user.FirstName,
		LastName:  updated_user.LastName,
		Email:     updated_user.Email,
		Position:  updated_user.Position,
		Role: func(u uint) *int {
			i := int(u)
			return &i
		}(updated_user.RoleID),
	}

	return c.Status(fiber.StatusOK).JSON(oneUser)
}

func DeleteUser(c *fiber.Ctx) error {
	slug := c.Params("slug")
	var user models.UserInfo
	userRole := c.Locals("role").(string)

	// First, find the user by slug
	if err := db.DB.Preload("Role").Where("slug = ?", slug).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "User not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to find user",
		})
	}

	// Check if lecturer is trying to delete an admin user
	if userRole == "lecturer" && user.Role.Name == "admin" {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "Access denied: Cannot delete admin user",
		})
	}

	// Delete the user
	if err := db.DB.Delete(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete user",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "User deleted successfully",
	})
}
