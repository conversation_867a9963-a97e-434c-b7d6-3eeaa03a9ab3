package controllers

import (
	"os"
	"strings"
	"time"

	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	"med-api/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
)

func Signup(c *fiber.Ctx) error {
	var input models_api.SignUpRequest
	// Hash the password
	// Hash the password before saving (optional, but recommended)
	if err := c.Body<PERSON>er(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	hashedPassword, err := utils.HashPassword(input.Password)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to hash password",
		})
	}

	input.Password = hashedPassword

	user := models.UserInfo{
		Username:  input.Username,
		Password:  input.Password,
		FirstName: input.FirstName,
		LastName:  input.LastName,
		Email:     input.Email,
		// LoginDate: time.Now().Format("2006-01-02 15:04:05"),
		RoleID: 1,
		Status: false,
		Slug:   utils.GenerateUUIDSlug(),
	}

	if err := db.DB.Where("username = ?", input.Username).First(&user).Error; err == nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Username already exists"})
	}

	if err := db.DB.Create(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Signup successful"})
}

// Dummy login logic — replace with real user auth if needed
func Login(c *fiber.Ctx) error {
	var creds models_api.LoginRequest
	if err := c.BodyParser(&creds); err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "failed to parse request body"})
	}

	var user models.UserInfo
	// Preload the "Role" relationship to ensure it's included
	if err := db.DB.Preload("Role").Where("email = ?", creds.Email).First(&user).Error; err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "failed to find user"})
	}

	// Compare the provided password with the stored hashed password
	if utils.CheckPassword([]byte(creds.Password), []byte(user.Password)) {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "failed to authenticate user"})
	}

	// Create JWT token with user and role information
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"role":   user.Role.Name, // Access role name
		"f_name": user.FirstName,
		"l_name": user.LastName,
		// "profile":  user.Picture,
		"email":    user.Email,
		"position": user.Position,
		"slug":     user.Slug,
		"exp":      time.Now().Add(24 * time.Hour).Unix(),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Could not generate token"})
	}

	if err := db.DB.Model(&user).Update("LoginDate", time.Now().Format("2006-01-02 15:04:05")).Error; err != nil {
		return c.Status(fiber.StatusNotExtended).JSON(fiber.Map{
			"error": "Invalid Date",
		})
	}

	// Return the token to the client
	return c.Status(fiber.StatusOK).JSON(fiber.Map{"token": "bearer " + tokenString})
}

// GetMe returns the current authenticated user's profile information
func GetMe(c *fiber.Ctx) error {
	// Get the Authorization header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Authorization header required"})
	}
	// Parse the Bearer token
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid Authorization header format"})
	}

	tokenStr := parts[1]

	// Parse and validate the JWT token
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return []byte(os.Getenv("JWT_SECRET")), nil
	})

	if err != nil || !token.Valid {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid or expired token"})
	}

	// Extract claims from the token
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid token claims"})
	}

	// Get user slug from token claims
	userSlug, ok := claims["slug"].(string)
	if !ok {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid user identifier in token"})
	}

	// Fetch user from database using slug
	var user models.UserInfo
	if err := db.DB.Preload("Role").Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Create response with safe user information (excluding password)
	userProfile := models_api.MeResponse{
		Username:  user.Username,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
		Position:  user.Position,
		Picture:   string(user.Picture),
		Role:      user.Role.Name,
	}

	return c.Status(fiber.StatusOK).JSON(userProfile)
}
