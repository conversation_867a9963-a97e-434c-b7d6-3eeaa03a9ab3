package controllers

import (
	"med-api/db"
	"med-api/models"

	"github.com/gofiber/fiber/v2"
)

func GetUserCourseSlug(c *fiber.Ctx) error {
	slug := c.Params("slug")
	var course models.Courses
	if err := db.DB.Where("slug = ?", slug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Course not found"})
	}

	var lecturer models.UserInfo
	if err := db.DB.Where("id = ?", course.LecturerID).First(&lecturer).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	var lessons []models.Lessons
	if err := db.DB.Where("course_id = ?", course.ID).Find(&lessons).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	lessonDetails := make([]map[string]interface{}, 0, len(lessons))
	var totalLessonCount = len(lessons)
	var totalExamCount int64
	var totalVideoCount int64

	// var exams models.Exam
	// Fetch all exam IDs for the course
	var examIDs []uint
	if err := db.DB.Model(&models.Exam{}).Where("course_id = ?", course.ID).Pluck("id", &examIDs).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	// Count total exam questions for all exams in the course
	if len(examIDs) > 0 {
		if err := db.DB.Model(&models.ExamQuestion{}).Where("exam_id = ?", examIDs).Count(&totalExamCount).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
		}
	} else {
		totalExamCount = 0
	}

	for _, lesson := range lessons {
		var contents []models.ContentLesson
		if err := db.DB.Where("lesson_id = ?", lesson.ID).Find(&contents).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
		}

		contentDetails := make([]map[string]interface{}, 0, len(contents))
		for _, content := range contents {
			var contentTypeName string
			if content.ContentLessonType == ContentTypeVideo {
				contentTypeName = "Video"
			} else {
				contentTypeName = "Text"
			}

			contentDetails = append(contentDetails, map[string]interface{}{
				"content_lesson_name": content.ContentLessonName,
				"content_lesson_type": contentTypeName,
			})

			var videoCount int64
			if err := db.DB.Model(&models.ContentLesson{}).Where("id = ? AND content_lesson_type = ?", content.ID, ContentTypeVideo).Count(&videoCount).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
			}
			totalVideoCount += videoCount
		}

		lessonDetails = append(lessonDetails, map[string]interface{}{
			"lesson_description": lesson.LessonDescription,
			"lesson_name":        lesson.LessonName,
			"content":            contentDetails,
		})
	}

	summary := map[string]interface{}{
		"lesson_count": totalLessonCount,
		"exam_count":   totalExamCount,
		"video_count":  totalVideoCount,
	}

	response := map[string]interface{}{
		"slug":               course.Slug,
		"course_name":        course.CourseName,
		"course_picture":     string(course.CoursePicture),
		"course_description": course.CourseDescription,
		"course_instruction": course.CourseInstruction,
		"course_difficulty":  course.CourseDifficulty,
		"course_duration":    course.CourseDuration,
		"course_certificate": course.CourseCertificate,
		"lecturer_id":        course.LecturerID,
		"lecturer_name":      lecturer.FirstName + " " + lecturer.LastName,
		"lecturer_position":  lecturer.Position,
		"lecturer_picture":   string(lecturer.Picture),
		"summary":            summary,
		"lesson_details":     lessonDetails,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}
