package controllers

import (
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"

	"github.com/gofiber/fiber/v2"
)

// MarkContentProgress marks a content lesson as completed for a user
func MarkContentProgress(c *fiber.Ctx) error {
	var request models_request.ContentProgressRequest
	userSlug := c.Locals("user_slug")

	if err := c.BodyParser(&request); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	// Find content lesson by slug
	var contentLesson models.ContentLesson
	if err := db.DB.Where("content_slug = ?", request.ContentLessonSlug).First(&contentLesson).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Content lesson not found",
		})
	}

	// Check if progress already exists
	var existingProgress models.ContentProgress
	if err := db.DB.Where("user_id = ? AND content_lesson_id = ?", user.ID, contentLesson.ID).First(&existingProgress).Error; err == nil {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"message": "Content lesson already completed",
		})
	}

	// Create new progress record
	newProgress := models.ContentProgress{
		ContentLessonID: contentLesson.ID,
		UserID:          user.ID,
	}

	if err := db.DB.Create(&newProgress).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to mark content as completed",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Content lesson marked as completed",
	})
}
