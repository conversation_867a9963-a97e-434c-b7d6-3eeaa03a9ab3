package controllers

import (
	"encoding/base64"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"

	"github.com/gofiber/fiber/v2"
)

// GetUserCourses returns all courses assigned to a user through learning paths with pagination
// The response matches the UserCourse type in user_dashboard.go
// Supports pagination with 4 items per page
func GetUserCoursesDashboard(c *fiber.Ctx) error {
	userSlug := c.Locals("user_slug").(string)
	if userSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug is required"})
	}

	// Get pagination parameters
	page := c.QueryInt("page", 1) // Default to page 1 if not provided
	if page < 1 {
		page = 1
	}
	limit := 4 // Fixed at 4 items per page
	offset := (page - 1) * limit

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get user's assigned paths
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ?", user.ID).Preload("Path").Find(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user assignments"})
	}

	// Collect all course IDs from user's paths
	var courseIDs []uint
	pathMap := make(map[uint]models.CoursePath) // To store path info for each course

	for _, assignment := range assignments {
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_path_id = ?", assignment.PathID).Preload("Course").Find(&courseStores).Error; err != nil {
			continue
		}

		for _, cs := range courseStores {
			courseIDs = append(courseIDs, cs.CourseID)
			pathMap[cs.CourseID] = assignment.Path
		}
	}

	if len(courseIDs) == 0 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"data": []models_request.UserCourse{},
			"pagination": fiber.Map{
				"current_page": page,
				"per_page":     limit,
				"total":        0,
				"total_pages":  0,
			},
		})
	}

	// Count total courses for pagination
	totalCourses := len(courseIDs)
	totalPages := (totalCourses + limit - 1) / limit // Ceiling division

	// Apply pagination to courseIDs
	var paginatedCourseIDs []uint
	if offset < len(courseIDs) {
		end := offset + limit
		if end > len(courseIDs) {
			end = len(courseIDs)
		}
		paginatedCourseIDs = courseIDs[offset:end]
	}

	// Get paginated courses
	var courses []models.Courses
	if len(paginatedCourseIDs) > 0 {
		if err := db.DB.Where("id IN ?", paginatedCourseIDs).Find(&courses).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve courses"})
		}
	}

	// Build response
	var userCourses []models_request.UserCourse
	for _, course := range courses {
		// Get lessons for this course with their order
		var lessons []models.Lessons
		db.DB.Where("course_id = ?", course.ID).Order("lessons_order ASC").Find(&lessons)

		// Count total lessons
		totalLessons := len(lessons)

		// Get user's progress for this course
		var progressCount int64
		db.DB.Model(&models.ContentProgress{}).
			Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
			Count(&progressCount)

		// Build course path (lessons) array
		var coursePath []models_request.PathCourse
		for _, lesson := range lessons {
			coursePath = append(coursePath, models_request.PathCourse{
				LessonSlug: lesson.LessonSlug,
				LessonName: lesson.LessonName,
			})
		}

		// Check if course is completed/graduated
		// A course is graduated if user has completed all lessons
		var totalContents int64
		db.DB.Model(&models.ContentLesson{}).
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Where("lessons.course_id = ?", course.ID).
			Count(&totalContents)

		courseGraduated := totalContents > 0 && progressCount >= totalContents

		// Calculate course processing as a percentage
		var courseProcessing int
		if totalContents > 0 {
			courseProcessing = int((float64(progressCount) / float64(totalContents)) * 100)
		}

		// Convert course picture to base64 if exists
		// var courseImage string
		// if len(course.CoursePicture) > 0 {
		// 	courseImage = base64.StdEncoding.EncodeToString(course.CoursePicture)
		// }

		// Parse course duration to int (assuming it's stored as string in hours)
		courseDuration := 0
		if course.CourseDuration != "" {
			// You might want to implement proper parsing based on your duration format
			// For now, assuming it's a simple integer string
			courseDuration = parseIntFromString(course.CourseDuration)
		}

		userCourse := models_request.UserCourse{
			CourseSlug:        course.Slug,
			CourseImage:       string(course.CoursePicture), // Assuming CoursePicture is a string, otherwise convert to base64
			CourseGraduated:   courseGraduated,
			CourseName:        course.CourseName,
			CoursePath:        coursePath,
			CourseDescription: course.CourseDescription,
			CourseProcessing:  courseProcessing,
			CourseAmount:      totalLessons,
			CourseLessoned:    int(progressCount), // Number of completed lessons/contents
			CourseDifficulty:  course.CourseDifficulty,
			CourseDuration:    courseDuration,
		}

		userCourses = append(userCourses, userCourse)
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"data": userCourses,
		"pagination": fiber.Map{
			"current_page": page,
			"per_page":     limit,
			"total":        totalCourses,
			"total_pages":  totalPages,
			"has_next":     page < totalPages,
			"has_prev":     page > 1,
		},
	})
}

// GetUserCourseBySlug returns specific course data for a user
func GetUserCourseBySlug(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	courseSlug := c.Params("course_slug")

	if userSlug == "" || courseSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug and course slug are required"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Find course by slug
	var course models.Courses
	if err := db.DB.Where("slug = ?", courseSlug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Course not found"})
	}

	// Verify user has access to this course through assigned paths
	var hasAccess bool
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ?", user.ID).Find(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to check user access"})
	}

	for _, assignment := range assignments {
		var courseStore models.CourseStore
		if err := db.DB.Where("course_path_id = ? AND course_id = ?", assignment.PathID, course.ID).First(&courseStore).Error; err == nil {
			hasAccess = true
			break
		}
	}

	if !hasAccess {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "User does not have access to this course"})
	}

	// Get lessons for this course
	var lessons []models.Lessons
	db.DB.Where("course_id = ?", course.ID).Order("lessons_order ASC").Find(&lessons)

	// Count total lessons
	totalLessons := len(lessons)

	// Get user's progress for this course
	var progressCount int64
	db.DB.Model(&models.ContentProgress{}).
		Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
		Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
		Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
		Count(&progressCount)

	// Build course path (lessons) array
	var coursePath []models_request.PathCourse
	for _, lesson := range lessons {
		coursePath = append(coursePath, models_request.PathCourse{
			LessonSlug: lesson.LessonSlug,
			LessonName: lesson.LessonName,
		})
	}

	// Check if course is completed/graduated
	var totalContents int64
	db.DB.Model(&models.ContentLesson{}).
		Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
		Where("lessons.course_id = ?", course.ID).
		Count(&totalContents)

	courseGraduated := totalContents > 0 && progressCount >= totalContents

	// Calculate course processing as a percentage
	var courseProcessing int
	if totalContents > 0 {
		courseProcessing = int((float64(progressCount) / float64(totalContents)) * 100)
	}

	// Convert course picture to base64 if exists
	var courseImage string
	if len(course.CoursePicture) > 0 {
		courseImage = base64.StdEncoding.EncodeToString(course.CoursePicture)
	}

	// Parse course duration to int
	courseDuration := 0
	if course.CourseDuration != "" {
		courseDuration = parseIntFromString(course.CourseDuration)
	}

	userCourse := models_request.UserCourse{
		CourseSlug:        course.Slug,
		CourseImage:       courseImage,
		CourseGraduated:   courseGraduated,
		CourseName:        course.CourseName,
		CoursePath:        coursePath,
		CourseDescription: course.CourseDescription,
		CourseProcessing:  courseProcessing,
		CourseAmount:      totalLessons,
		CourseLessoned:    int(progressCount),
		CourseDifficulty:  course.CourseDifficulty,
		CourseDuration:    courseDuration,
	}

	return c.Status(fiber.StatusOK).JSON(userCourse)
}

// GetUserCourseProgress returns detailed progress information for a user's course
func GetUserCourseProgress(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	courseSlug := c.Params("course_slug")

	if userSlug == "" || courseSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug and course slug are required"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Find course by slug
	var course models.Courses
	if err := db.DB.Where("slug = ?", courseSlug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Course not found"})
	}

	// Get detailed progress information
	var progresses []models.ContentProgress
	if err := db.DB.Where("user_id = ?", user.ID).
		Preload("ContentLesson.Lesson").
		Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
		Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
		Where("lessons.course_id = ?", course.ID).
		Find(&progresses).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve progress"})
	}

	// Get all lessons and contents for the course
	var lessons []models.Lessons
	if err := db.DB.Where("course_id = ?", course.ID).
		Preload("ContentLesson").
		Order("lessons_order ASC").
		Find(&lessons).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve lessons"})
	}

	// Build progress response
	type ContentProgressDetail struct {
		ContentSlug  string `json:"content_slug"`
		ContentName  string `json:"content_name"`
		LessonSlug   string `json:"lesson_slug"`
		LessonName   string `json:"lesson_name"`
		IsCompleted  bool   `json:"is_completed"`
		CourseRecord string `json:"course_record"`
		LessonRecord string `json:"lesson_record"`
	}

	type ProgressResponse struct {
		CourseSlug        string                  `json:"course_slug"`
		CourseName        string                  `json:"course_name"`
		TotalContents     int                     `json:"total_contents"`
		CompletedContents int                     `json:"completed_contents"`
		ProgressPercent   float64                 `json:"progress_percent"`
		ContentProgress   []ContentProgressDetail `json:"content_progress"`
	}

	// Create map of completed contents
	completedContents := make(map[uint]models.ContentProgress)
	for _, progress := range progresses {
		completedContents[progress.ContentLessonID] = progress
	}

	var contentProgressDetails []ContentProgressDetail
	totalContents := 0

	for _, lesson := range lessons {
		// Get contents for each lesson - assuming ContentLesson is preloaded
		var contents []models.ContentLesson
		if err := db.DB.Where("lesson_id = ?", lesson.ID).Find(&contents).Error; err != nil {
			continue
		}

		for _, content := range contents {
			totalContents++
			progress, isCompleted := completedContents[content.ID]

			detail := ContentProgressDetail{
				ContentSlug: content.ContentSlug,
				ContentName: content.ContentLessonName,
				LessonSlug:  lesson.LessonSlug,
				LessonName:  lesson.LessonName,
				IsCompleted: isCompleted,
			}

			if isCompleted {
				detail.CourseRecord = progress.CourseRecord
				detail.LessonRecord = progress.LessonRecord
			}

			contentProgressDetails = append(contentProgressDetails, detail)
		}
	}

	completedCount := len(progresses)
	progressPercent := 0.0
	if totalContents > 0 {
		progressPercent = (float64(completedCount) / float64(totalContents)) * 100
	}

	response := ProgressResponse{
		CourseSlug:        course.Slug,
		CourseName:        course.CourseName,
		TotalContents:     totalContents,
		CompletedContents: completedCount,
		ProgressPercent:   progressPercent,
		ContentProgress:   contentProgressDetails,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// UpdateUserProgress updates or creates user progress for a specific content
func UpdateUserProgress(c *fiber.Ctx) error {
	type ProgressRequest struct {
		UserSlug     string `json:"user_slug" binding:"required"`
		ContentSlug  string `json:"content_slug" binding:"required"`
		CourseRecord string `json:"course_record"`
		LessonRecord string `json:"lesson_record"`
	}

	var req ProgressRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", req.UserSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Find content by slug
	var content models.ContentLesson
	if err := db.DB.Where("content_slug = ?", req.ContentSlug).First(&content).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Content not found"})
	}

	// Check if progress already exists
	var progress models.ContentProgress
	err := db.DB.Where("user_id = ? AND content_lesson_id = ?", user.ID, content.ID).First(&progress).Error

	if err != nil {
		// Create new progress
		progress = models.ContentProgress{
			UserID:          user.ID,
			ContentLessonID: content.ID,
			CourseRecord:    req.CourseRecord,
			LessonRecord:    req.LessonRecord,
		}
		if err := db.DB.Create(&progress).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create progress"})
		}
	} else {
		// Update existing progress
		progress.CourseRecord = req.CourseRecord
		progress.LessonRecord = req.LessonRecord
		if err := db.DB.Save(&progress).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update progress"})
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Progress updated successfully",
		"data":    progress,
	})
}
