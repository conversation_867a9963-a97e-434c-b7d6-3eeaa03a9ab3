package controllers

import (
	"fmt"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

// GetCourseBySlug and content details with progress tracking
func GetUserDisplayCourse(c *fiber.Ctx) error {
	slug := c.Params("slug")
	if slug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Course slug is required"})
	}

	// Get user slug from JWT token
	userSlug := c.Locals("user_slug")
	if userSlug == nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "User not authenticated"})
	}

	// Get user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get course by slug with lecturer information
	var course models.Courses
	if err := db.DB.Preload("Lecture").Where("slug = ?", slug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Course not found"})
	}

	// Get lessons for the course
	var lessons []models.Lessons
	if err := db.DB.Where("course_id = ?", course.ID).Order("lessons_order ASC").Find(&lessons).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch lessons"})
	}

	// Prepare lecturer information
	lecturer := models_request.Lecturer{
		LecturerName:     course.Lecture.FirstName + " " + course.Lecture.LastName,
		LecturerPosition: course.Lecture.Position,
	}

	// Prepare lessons with content and progress tracking
	var courseLessons []models_request.Lessons
	for i, lesson := range lessons {
		// Get content for each lesson
		var contents []models.ContentLesson
		if err := db.DB.Preload("ContentType").Where("lesson_id = ?", lesson.ID).Order("content_order ASC").Find(&contents).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch lesson content"})
		}

		// Check if this lesson is locked
		isLessonLocked := false
		if i > 0 {
			// Check if previous lesson is completed
			prevLesson := lessons[i-1]
			var prevLessonContents []models.ContentLesson
			if err := db.DB.Where("lesson_id = ?", prevLesson.ID).Find(&prevLessonContents).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to check previous lesson progress"})
			}

			// Check if all content in previous lesson is completed
			for _, prevContent := range prevLessonContents {
				var progress models.ContentProgress
				if err := db.DB.Where("content_lesson_id = ? AND user_id = ?", prevContent.ID, user.ID).First(&progress).Error; err != nil {
					// Content not completed, lock current lesson
					isLessonLocked = true
					break
				}
			}
		}

		// Prepare content list with progress tracking
		var lessonContents []models_request.Content
		for j, content := range contents {
			isContentLocked := false

			// If lesson is locked, all content is locked
			if isLessonLocked {
				isContentLocked = true
			} else if j > 0 {
				// Check if previous content is completed
				prevContent := contents[j-1]
				var progress models.ContentProgress
				print("Checking progress for content: ", prevContent.ID, " by user: ", user.ID, "\n")
				if err := db.DB.Where("content_lesson_id = ? AND user_id = ?", prevContent.ID, user.ID).First(&progress).Error; err != nil {
					// Previous content not completed, lock current content
					isContentLocked = true
				}
			}

			lessonContents = append(lessonContents, models_request.Content{
				ContentSlug:           content.ContentSlug,
				ContentLessonName:     content.ContentLessonName,
				ContentLessonDuration: convertTimeToString(content.LessonTime),
				URL:                   content.ContentDescription,
				IsLocked:              isContentLocked,
			})
		}

		courseLessons = append(courseLessons, models_request.Lessons{
			LessonName:     lesson.LessonName,
			LessonDuration: convertStringTimeToMinutes(lesson.LessonTime),
			LessonContent:  lessonContents,
			IsLocked:       isLessonLocked,
		})
	}

	// Check if course has exam and if it's accessible
	var exam models.Exam
	var courseExam models_request.Exam
	if err := db.DB.Where("course_id = ?", course.ID).First(&exam).Error; err != nil {
		// No exam found
		courseExam = models_request.Exam{
			HasExam:      false,
			ExamDuration: "",
			ExamSlug:     "",
			IsLocked:     false,
		}
	} else {
		// Exam found - check if all lessons are completed
		isExamLocked := false
		for _, lesson := range lessons {
			var lessonContents []models.ContentLesson
			if err := db.DB.Where("lesson_id = ?", lesson.ID).Find(&lessonContents).Error; err == nil {
				for _, content := range lessonContents {
					var progress models.ContentProgress
					if err := db.DB.Where("content_lesson_id = ? AND user_id = ?", content.ID, user.ID).First(&progress).Error; err != nil {
						// Content not completed, lock exam
						isExamLocked = true
						break
					}
				}
				if isExamLocked {
					break
				}
			}
		}

		courseExam = models_request.Exam{
			HasExam:      true,
			ExamDuration: convertTimeToString(exam.ExamTime),
			ExamSlug:     exam.Slug,
			IsLocked:     isExamLocked,
		}
	}

	// Prepare final response
	response := models_request.Courses{
		CourseSlug:        course.Slug,
		CourseName:        course.CourseName,
		CourseDescription: course.CourseDescription,
		CourseLecturer:    lecturer,
		CourseLessons:     courseLessons,
		CourseExam:        courseExam,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// Helper function to convert time from seconds to minutes and return as string
func convertTimeToString(timeInSeconds int) string {
	minutes := timeInSeconds / 60
	return fmt.Sprintf("%d", minutes)
}

// Helper function to convert string time from seconds to minutes
func convertStringTimeToMinutes(timeString string) string {
	timeInSeconds, err := strconv.Atoi(timeString)
	if err != nil {
		return "0"
	}
	minutes := timeInSeconds / 60
	return fmt.Sprintf("%d", minutes)
}
