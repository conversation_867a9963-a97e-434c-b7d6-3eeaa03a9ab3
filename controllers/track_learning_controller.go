package controllers

import (
	"math"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

// GetTrackLearning returns track learning data for lecturer
// Shows courses with their paths, assigned student count, and course status
func GetTrackLearning(c *fiber.Ctx) error {
	// Get pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	const itemsPerPage = 10
	offset := (page - 1) * itemsPerPage

	// Get search query parameter
	search := c.Query("search", "")

	// Get all courses with their lecturer info
	var courses []models.Courses
	query := db.DB.Preload("Lecture")

	// Apply search filter if provided
	if search != "" {
		query = query.Where("course_name LIKE ?", "%"+search+"%")
	}

	// Get total count for pagination
	var totalCount int64
	if err := query.Model(&models.Courses{}).Count(&totalCount).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count courses",
		})
	}

	// Get courses with pagination
	if err := query.Limit(itemsPerPage).Offset(offset).Find(&courses).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to fetch courses",
		})
	}

	var trackLearningRows []models_request.TrackLearningRow

	for _, course := range courses {
		// Get all paths that contain this course
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_id = ?", course.ID).
			Preload("CoursePath").
			Find(&courseStores).Error; err != nil {
			continue
		}

		// Extract path names
		var pathNames []string
		pathMap := make(map[string]bool) // To avoid duplicate path names
		for _, store := range courseStores {
			if !pathMap[store.CoursePath.Name] {
				pathNames = append(pathNames, store.CoursePath.Name)
				pathMap[store.CoursePath.Name] = true
			}
		}

		// Count assigned students for this course
		// We need to count unique users who are assigned to paths that contain this course
		var assignedCount int64
		if len(courseStores) > 0 {
			// Get all path IDs that contain this course
			var pathIDs []uint
			for _, store := range courseStores {
				pathIDs = append(pathIDs, store.CoursePathID)
			}

			// Count unique users assigned to these paths
			if len(pathIDs) > 0 {
				db.DB.Model(&models.AssignPath{}).
					Where("path_id IN ?", pathIDs).
					Group("user_id").
					Count(&assignedCount)
			}
		}

		// Create track learning row
		trackLearningRow := models_request.TrackLearningRow{
			CourseName:    course.CourseName,
			PathNames:     pathNames,
			AssignedCount: int(assignedCount),
			CourseStatus:  course.CourseStatus,
			CourseSlug:    course.Slug,
		}

		trackLearningRows = append(trackLearningRows, trackLearningRow)
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(itemsPerPage)))

	// Create response
	response := models_request.TrackLearningResponse{
		Data: trackLearningRows,
		Pagination: models_request.PaginationMeta{
			CurrentPage:  page,
			TotalPages:   totalPages,
			TotalItems:   int(totalCount),
			ItemsPerPage: itemsPerPage,
			HasNext:      page < totalPages,
			HasPrev:      page > 1,
		},
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// GetTrackLearningByCourse returns detailed track learning data for a specific course
func GetTrackLearningByCourse(c *fiber.Ctx) error {
	courseID := c.Params("course_id")
	if courseID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Course ID is required",
		})
	}

	// Convert courseID to uint
	id, err := strconv.ParseUint(courseID, 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid course ID",
		})
	}

	// Get course details
	var course models.Courses
	if err := db.DB.Where("id = ?", uint(id)).Preload("Lecture").First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Course not found",
		})
	}

	// Get all paths that contain this course
	var courseStores []models.CourseStore
	if err := db.DB.Where("course_id = ?", course.ID).
		Preload("CoursePath").
		Find(&courseStores).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to fetch course paths",
		})
	}

	// Extract path names
	var pathNames []string
	pathMap := make(map[string]bool) // To avoid duplicate path names
	for _, store := range courseStores {
		if !pathMap[store.CoursePath.Name] {
			pathNames = append(pathNames, store.CoursePath.Name)
			pathMap[store.CoursePath.Name] = true
		}
	}

	// Count assigned students for this course
	var assignedCount int64
	if len(courseStores) > 0 {
		// Get all path IDs that contain this course
		var pathIDs []uint
		for _, store := range courseStores {
			pathIDs = append(pathIDs, store.CoursePathID)
		}

		// Count unique users assigned to these paths
		if len(pathIDs) > 0 {
			db.DB.Model(&models.AssignPath{}).
				Where("path_id IN ?", pathIDs).
				Group("user_id").
				Count(&assignedCount)
		}
	}

	// Create detailed response
	trackLearningRow := models_request.TrackLearningRow{
		CourseName:    course.CourseName,
		PathNames:     pathNames,
		AssignedCount: int(assignedCount),
		CourseStatus:  course.CourseStatus,
		CourseSlug:    course.Slug,
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"data":    trackLearningRow,
	})
}

// GetTrackLearningCourses returns a list of all courses for track learning overview
func GetTrackLearningCourses(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var courses []models.Courses
	query := db.DB.Preload("Lecture")

	// Filter courses based on user role
	if userRole == "lecturer" {
		// Find the lecturer's ID by their slug
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Where("lecturer_id = ?", lecturer.ID)
	}

	if err := query.Find(&courses).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to fetch courses",
		})
	}

	var coursesOverview []models_request.CourseTrackingOverview

	for _, course := range courses {
		// Get all users who are assigned to paths containing this course
		var assignedUsers []models.UserInfo
		if err := db.DB.Distinct().
			Joins("JOIN assign_paths ON assign_paths.user_id = user_infos.id").
			Joins("JOIN course_stores ON course_stores.course_path_id = assign_paths.path_id").
			Where("course_stores.course_id = ?", course.ID).
			Find(&assignedUsers).Error; err != nil {
			continue
		}

		totalStudents := len(assignedUsers)
		completedStudents := 0
		inProgressStudents := 0
		notStartedStudents := 0

		// Calculate progress for each student
		for _, user := range assignedUsers {
			// Get total content lessons for this course
			var totalContents int64
			db.DB.Model(&models.ContentLesson{}).
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ?", course.ID).
				Count(&totalContents)

			if totalContents == 0 {
				notStartedStudents++
				continue
			}

			// Get user's progress for this course
			var progressCount int64
			db.DB.Model(&models.ContentProgress{}).
				Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
				Count(&progressCount)

			progressPercent := int((float64(progressCount) / float64(totalContents)) * 100)

			if progressPercent == 100 {
				completedStudents++
			} else if progressPercent > 0 {
				inProgressStudents++
			} else {
				notStartedStudents++
			}
		}

		courseOverview := models_request.CourseTrackingOverview{
			CourseID:           course.ID,
			CourseSlug:         course.Slug,
			CourseName:         course.CourseName,
			CourseDescription:  course.CourseDescription,
			TotalStudents:      totalStudents,
			CompletedStudents:  completedStudents,
			InProgressStudents: inProgressStudents,
			NotStartedStudents: notStartedStudents,
		}

		coursesOverview = append(coursesOverview, courseOverview)
	}

	response := models_request.TrackLearningCoursesResponse{
		Courses: coursesOverview,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// GetTrackLearningDetail returns detailed student tracking data for a specific course
func GetTrackLearningDetail(c *fiber.Ctx) error {
	courseSlug := c.Params("course_slug")
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	// Get pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "9"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 9
	}

	offset := (page - 1) * limit

	// Get filter parameters
	search := c.Query("search", "")
	statusFilter := c.Query("status", "") // "completed", "not-completed", or empty for all

	// Find course by slug
	var course models.Courses
	query := db.DB.Where("slug = ?", courseSlug)

	// Filter access if user is a lecturer
	if userRole == "lecturer" {
		var lecturer models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&lecturer).Error; err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found"})
		}
		query = query.Where("lecturer_id = ?", lecturer.ID)
	}

	if err := query.First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Course not found or access denied"})
	}

	// Get all users assigned to paths containing this course
	baseQuery := db.DB.Distinct().
		Joins("JOIN assign_paths ON assign_paths.user_id = user_infos.id").
		Joins("JOIN course_stores ON course_stores.course_path_id = assign_paths.path_id").
		Where("course_stores.course_id = ?", course.ID)

	// Apply search filter
	if search != "" {
		baseQuery = baseQuery.Where("(user_infos.first_name LIKE ? OR user_infos.last_name LIKE ?)",
			"%"+search+"%", "%"+search+"%")
	}

	// Get total count for pagination (before applying status filter)
	var totalCount int64
	if err := baseQuery.Model(&models.UserInfo{}).Count(&totalCount).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count students",
		})
	}

	// Get all users first to apply status filtering
	var allUsers []models.UserInfo
	if err := baseQuery.Find(&allUsers).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to fetch students",
		})
	}

	// Calculate progress and filter by status
	var filteredStudents []models_request.StudentTrackingDetail
	totalStudents := 0
	completedStudents := 0
	inProgressStudents := 0
	notStartedStudents := 0

	for _, user := range allUsers {
		// Get total content lessons for this course
		var totalContents int64
		db.DB.Model(&models.ContentLesson{}).
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Where("lessons.course_id = ?", course.ID).
			Count(&totalContents)

		totalLessons := int(totalContents)

		// Get user's progress for this course
		var progressCount int64
		db.DB.Model(&models.ContentProgress{}).
			Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
			Count(&progressCount)

		completedLessons := int(progressCount)
		progressPercent := 0
		if totalLessons > 0 {
			progressPercent = int((float64(completedLessons) / float64(totalLessons)) * 100)
		}

		// Determine status
		status := "not-started"
		if progressPercent == 100 {
			status = "completed"
			completedStudents++
		} else if progressPercent > 0 {
			status = "not-completed"
			inProgressStudents++
		} else {
			notStartedStudents++
		}

		totalStudents++

		// Apply status filter
		if statusFilter != "" && statusFilter != status {
			continue
		}

		// Get current lesson based on last completed content progress
		currentLesson := ""
		if progressPercent == 100 {
			currentLesson = "เรียนจบแล้ว"
		} else if progressPercent > 0 {
			// Get the last completed content progress to determine current lesson
			var lastContentProgress models.ContentProgress
			if err := db.DB.Preload("ContentLesson").
				Preload("ContentLesson.Lesson").
				Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
				Order("content_progresses.completed_at DESC").
				First(&lastContentProgress).Error; err == nil {
				currentLesson = lastContentProgress.ContentLesson.Lesson.LessonName
			}
		} else {
			// Get the first lesson for users who haven't started
			var firstLesson models.Lessons
			if err := db.DB.Where("course_id = ?", course.ID).
				Order("lessons_order ASC").
				First(&firstLesson).Error; err == nil {
				currentLesson = firstLesson.LessonName
			}
		}

		// Convert user picture to string
		avatar := ""
		if len(user.Picture) > 0 {
			avatar = string(user.Picture)
		}

		studentDetail := models_request.StudentTrackingDetail{
			UserSlug:                user.Slug,
			Name:                    user.FirstName + " " + user.LastName,
			FirstName:               user.FirstName,
			LastName:                user.LastName,
			Position:                user.Position,
			Avatar:                  avatar,
			Progress:                progressPercent,
			LastFinishContentLesson: currentLesson,
			LastLogin:               user.LoginDate,
			Status:                  status,
			CompletedLessons:        completedLessons,
			TotalLessons:            totalLessons,
		}

		filteredStudents = append(filteredStudents, studentDetail)
	}

	// Apply pagination to filtered results
	totalFiltered := len(filteredStudents)
	totalPages := int(math.Ceil(float64(totalFiltered) / float64(limit)))

	start := offset
	end := offset + limit
	if start > totalFiltered {
		start = totalFiltered
	}
	if end > totalFiltered {
		end = totalFiltered
	}

	paginatedStudents := []models_request.StudentTrackingDetail{}
	if start < end {
		paginatedStudents = filteredStudents[start:end]
	}

	// Course overview
	courseOverview := models_request.CourseTrackingOverview{
		CourseID:           course.ID,
		CourseSlug:         course.Slug,
		CourseName:         course.CourseName,
		CourseDescription:  course.CourseDescription,
		TotalStudents:      totalStudents,
		CompletedStudents:  completedStudents,
		InProgressStudents: inProgressStudents,
		NotStartedStudents: notStartedStudents,
	}

	// Pagination info
	pagination := models_request.PaginationInfo{
		Page:        page,
		Limit:       limit,
		TotalItems:  totalFiltered,
		TotalPages:  totalPages,
		HasNext:     page < totalPages,
		HasPrevious: page > 1,
	}

	response := models_request.TrackLearningDetailResponse{
		CourseOverview: courseOverview,
		Students:       paginatedStudents,
		Pagination:     pagination,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}
