package controllers

import (
	"math"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

// GetUserPathDashboard returns user's path dashboard data with pagination
// Returns paths assigned to the user with their courses and progress (4 items per page)
func GetUserPathDashboard(c *fiber.Ctx) error {
	userSlug := c.Locals("user_slug").(string)
	if userSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug is required"})
	}

	// Get pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	const itemsPerPage = 4
	offset := (page - 1) * itemsPerPage

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get total count of user's assigned paths
	var totalCount int64
	if err := db.DB.Model(&models.AssignPath{}).Where("user_id = ?", user.ID).Count(&totalCount).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to count user assignments"})
	}

	// Get user's assigned paths with pagination
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ?", user.ID).
		Preload("Path").
		Limit(itemsPerPage).
		Offset(offset).
		Find(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user assignments"})
	}

	var userPathDashboards []models_request.UserPathDashboard

	for _, assignment := range assignments {
		// Get courses in this path
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_path_id = ?", assignment.PathID).
			Preload("Course").
			Order("store_order ASC").
			Find(&courseStores).Error; err != nil {
			continue
		}

		// Calculate path statistics
		totalCourses := len(courseStores)
		completedLessons := 0
		totalDuration := 0
		totalLessonsInPath := 0

		var pathCourses []models_request.UserPathCourse

		for _, cs := range courseStores {
			course := cs.Course

			// Get lessons for this course
			var lessons []models.Lessons
			db.DB.Where("course_id = ?", course.ID).Order("lessons_order ASC").Find(&lessons)

			totalLessonsInPath += len(lessons)

			// Calculate course progress
			var progressCount int64
			db.DB.Model(&models.ContentProgress{}).
				Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
				Count(&progressCount)

			// Get total content lessons for this course
			var totalContents int64
			db.DB.Model(&models.ContentLesson{}).
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ?", course.ID).
				Count(&totalContents)

			// Calculate lesson progress percentage
			var lessonProgress int
			if totalContents > 0 {
				lessonProgress = int((float64(progressCount) / float64(totalContents)) * 100)
			}

			// Convert course duration from string to int (assuming it's in minutes)
			courseDuration := 0
			if course.CourseDuration != "" {
				// Parse duration string to minutes
				// For now, let's assume it's already in minutes format or default to 60
				courseDuration = 60 // Default duration, you may need to parse this properly
			}

			totalDuration += courseDuration

			// Count completed lessons for this course
			if lessonProgress == 100 {
				completedLessons += len(lessons)
			} else {
				// Count individual completed lessons
				completedLessons += int(progressCount)
			}

			var LessonCountExam int64
			// Check if user have exams for this course
			db.DB.Model(&models.Exam{}).
				Where("course_id = ?", course.ID).
				Count(&LessonCountExam)

			lessonHasExam := LessonCountExam > 0

			// Check if user has completed exams for this course
			var examCompletedCount int64
			db.DB.Model(&models.ExamRecord{}).
				Joins("JOIN exams ON exam_records.exam_id = exams.id").
				Where("exams.course_id = ? AND exam_records.user_id = ?", course.ID, user.ID).
				Count(&examCompletedCount)

			lessonExamined := examCompletedCount > 0

			// Create course entry for the path
			pathCourse := models_request.UserPathCourse{
				LessonSlug:       course.Slug,
				LessonName:       course.CourseName,
				LessonProgress:   lessonProgress,
				LessonDuration:   courseDuration,
				LessonDifficulty: course.CourseDifficulty,
				LessonHasExam:    lessonHasExam,
				LessonExamined:   lessonExamined,
			}

			pathCourses = append(pathCourses, pathCourse)
		}

		// Calculate overall path progress percentage
		pathProcessing := 0
		if totalLessonsInPath > 0 {
			pathProcessing = int((float64(completedLessons) / float64(totalLessonsInPath)) * 100)
		}

		// Create path dashboard entry
		pathDashboard := models_request.UserPathDashboard{
			PathName:           assignment.Path.Name, // Using Name as slug since there's no slug field
			PathSlug:           assignment.Path.Slug, // Using Name as slug since there's no slug field
			PathDescription:    assignment.Path.PathDescription,
			PathAmount:         totalCourses,
			PathCourseLessoned: completedLessons,
			PathDuration:       totalDuration,
			PathProcessing:     pathProcessing,
			Courses:            pathCourses,
		}

		userPathDashboards = append(userPathDashboards, pathDashboard)
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(itemsPerPage)))

	// Return paginated response
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"data": userPathDashboards,
		"pagination": fiber.Map{
			"current_page":   page,
			"total_pages":    totalPages,
			"total_items":    totalCount,
			"items_per_page": itemsPerPage,
			"has_next":       page < totalPages,
			"has_prev":       page > 1,
		},
	})
}

// GetUserPathDashboardByType returns user's path dashboard data filtered by learning type with pagination
// Returns paths assigned to the user based on the learning type (true/false) (4 items per page)
func GetUserPathDashboardByType(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	learningType := c.Query("type", "true") // Default to true if not specified

	if userSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug is required"})
	}

	// Get pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	const itemsPerPage = 4
	offset := (page - 1) * itemsPerPage

	// Convert string to boolean
	var typeFilter bool
	if learningType == "true" || learningType == "1" {
		typeFilter = true
	} else {
		typeFilter = false
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get total count of user's assigned paths filtered by learning type
	var totalCount int64
	if err := db.DB.Model(&models.AssignPath{}).Where("user_id = ? AND learning_type = ?", user.ID, typeFilter).Count(&totalCount).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to count user assignments"})
	}

	// Get user's assigned paths filtered by learning type with pagination
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ? AND learning_type = ?", user.ID, typeFilter).
		Preload("Path").
		Limit(itemsPerPage).
		Offset(offset).
		Find(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user assignments"})
	}

	var userPathDashboards []models_request.UserPathDashboard

	for _, assignment := range assignments {
		// Get courses in this path
		var courseStores []models.CourseStore
		if err := db.DB.Where("course_path_id = ?", assignment.PathID).
			Preload("Course").
			Order("store_order ASC").
			Find(&courseStores).Error; err != nil {
			continue
		}

		// Calculate path statistics
		totalCourses := len(courseStores)
		completedLessons := 0
		totalDuration := 0
		totalLessonsInPath := 0

		var pathCourses []models_request.UserPathCourse

		for _, cs := range courseStores {
			course := cs.Course

			// Get lessons for this course
			var lessons []models.Lessons
			db.DB.Where("course_id = ?", course.ID).Order("lessons_order ASC").Find(&lessons)

			totalLessonsInPath += len(lessons)

			// Calculate course progress
			var progressCount int64
			db.DB.Model(&models.ContentProgress{}).
				Joins("JOIN content_lessons ON content_progresses.content_lesson_id = content_lessons.id").
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ? AND content_progresses.user_id = ?", course.ID, user.ID).
				Count(&progressCount)

			// Get total content lessons for this course
			var totalContents int64
			db.DB.Model(&models.ContentLesson{}).
				Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
				Where("lessons.course_id = ?", course.ID).
				Count(&totalContents)

			// Calculate lesson progress percentage
			var lessonProgress int
			if totalContents > 0 {
				lessonProgress = int((float64(progressCount) / float64(totalContents)) * 100)
			}

			// Convert course duration from string to int (assuming it's in minutes)
			courseDuration := 0
			if course.CourseDuration != "" {
				// Parse duration string to minutes
				// For now, let's assume it's already in minutes format or default to 60
				courseDuration = 60 // Default duration, you may need to parse this properly
			}

			totalDuration += courseDuration

			// Count completed lessons for this course
			if lessonProgress == 100 {
				completedLessons += len(lessons)
			} else {
				// Count individual completed lessons
				completedLessons += int(progressCount)
			}

			// Check if user has completed exams for this course
			var examCompletedCount int64
			db.DB.Model(&models.ExamRecord{}).
				Joins("JOIN exams ON exam_records.exam_id = exams.id").
				Where("exams.course_id = ? AND exam_records.user_id = ?", course.ID, user.ID).
				Count(&examCompletedCount)

			lessonExamined := examCompletedCount > 0

			// Create course entry for the path
			pathCourse := models_request.UserPathCourse{
				LessonSlug:       course.Slug,
				LessonName:       course.CourseName,
				LessonProgress:   lessonProgress,
				LessonDuration:   courseDuration,
				LessonDifficulty: course.CourseDifficulty,
				LessonExamined:   lessonExamined,
			}

			pathCourses = append(pathCourses, pathCourse)
		}

		// Calculate overall path progress percentage
		pathProcessing := 0
		if totalLessonsInPath > 0 {
			pathProcessing = int((float64(completedLessons) / float64(totalLessonsInPath)) * 100)
		}

		// Create path dashboard entry
		pathDashboard := models_request.UserPathDashboard{
			PathSlug:           assignment.Path.Name, // Using Name as slug since there's no slug field
			PathDescription:    assignment.Path.PathDescription,
			PathAmount:         totalCourses,
			PathCourseLessoned: completedLessons,
			PathDuration:       totalDuration,
			PathProcessing:     pathProcessing,
			Courses:            pathCourses,
		}

		userPathDashboards = append(userPathDashboards, pathDashboard)
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(itemsPerPage)))

	// Return paginated response
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"data": userPathDashboards,
		"pagination": fiber.Map{
			"current_page":   page,
			"total_pages":    totalPages,
			"total_items":    totalCount,
			"items_per_page": itemsPerPage,
			"has_next":       page < totalPages,
			"has_prev":       page > 1,
		},
	})
}
