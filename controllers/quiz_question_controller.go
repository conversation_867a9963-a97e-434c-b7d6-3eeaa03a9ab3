package controllers

import (
	"encoding/base64"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"sort"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

// Helper function to compare two string slices (order-independent)
func areSlicesEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	// Create copies and sort them
	sortedA := make([]string, len(a))
	sortedB := make([]string, len(b))
	copy(sortedA, a)
	copy(sortedB, b)
	sort.Strings(sortedA)
	sort.Strings(sortedB)

	// Compare sorted slices
	for i := range sortedA {
		if sortedA[i] != sortedB[i] {
			return false
		}
	}
	return true
}

func GetQuizDisplayByContentSlug(c *fiber.Ctx) error {
	contentSlug := c.Params("content_slug")
	if contentSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Content slug is required"})
	}

	// Find the content lesson by slug
	var contentLesson models.ContentLesson
	if err := db.DB.Where("content_slug = ?", contentSlug).
		Preload("ContentType").
		First(&contentLesson).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Content not found"})
	}

	// Find all quizzes for this content lesson
	var quizzes []models.Quiz
	if err := db.DB.Where("content_lesson_id = ?", contentLesson.ID).Find(&quizzes).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch quizzes"})
	}

	var quizQuestions []models_request.QuizQuestionDisplay
	for _, quiz := range quizzes {
		// Find all questions for each quiz
		var questions []models.Question
		if err := db.DB.Where("quiz_id = ?", quiz.ID).
			Preload("TypeQuiz").
			Find(&questions).Error; err != nil {
			continue // Skip this quiz if there's an error
		}

		for _, question := range questions {
			// Find all choices for each question
			var choices []models.QuizChoices
			if err := db.DB.Where("quiz_question_id = ?", question.ID).
				Preload("ChoiceType").
				Find(&choices).Error; err != nil {
				continue // Skip this question if there's an error
			}

			var choiceDisplays []models_request.QuizChoiceDisplay
			for _, choice := range choices {
				var choiceImage string
				if len(choice.ChoiceImage) > 0 {
					choiceImage = base64.StdEncoding.EncodeToString(choice.ChoiceImage)
				}

				choiceDisplays = append(choiceDisplays, models_request.QuizChoiceDisplay{
					ChoiceSlug:  choice.Slug,
					Choice:      choice.Choice,
					ChoiceImage: choiceImage,
					ChoiceType:  choice.QuizChoiceType,
				})
			}

			var questionImage string
			if len(question.Image) > 0 {
				questionImage = base64.StdEncoding.EncodeToString(question.Image)
			}

			quizQuestions = append(quizQuestions, models_request.QuizQuestionDisplay{
				QuestionSlug:    question.Slug,
				QuestionTitle:   question.Title,
				QuestionDetails: question.Detail,
				QuestionImage:   questionImage,
				QuestionTime:    strconv.Itoa(question.TimeInsert),
				QuestionType:    question.TypeQuiz.ContentName,
				QuestionChoices: choiceDisplays,
			})
		}
	}

	contentDisplay := models_request.ContentDisplay{
		ContentSlug:        contentLesson.ContentSlug,
		ContentType:        contentLesson.ContentType.ContentName,
		ContentName:        contentLesson.ContentLessonName,
		ContentDescription: contentLesson.ContentDescription,
		QuizQuestions:      quizQuestions,
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"data":    contentDisplay,
	})
}

func SubmitQuizAnswers(c *fiber.Ctx) error {
	var submission models_request.QuizAnswerSubmission
	if err := c.BodyParser(&submission); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid request format"})
	}

	// Process the submitted answer and create response
	var result models_request.QuizCorrectResponse

	// Find the question by slug directly
	var question models.Question
	if err := db.DB.Where("slug = ?", submission.QuestionSlug).First(&question).Error; err != nil {
		// If question not found, return response indicating error
		result = models_request.QuizCorrectResponse{
			QuestionSlug: submission.QuestionSlug,
			CorrectSlugs: []string{},
			IsCorrect:    false,
		}
		return c.Status(fiber.StatusOK).JSON(result)
	}

	// Find all choices for this question
	var choices []models.QuizChoices
	if err := db.DB.Where("quiz_question_id = ?", question.ID).Find(&choices).Error; err != nil {
		// If choices not found, return response indicating error
		result = models_request.QuizCorrectResponse{
			QuestionSlug: submission.QuestionSlug,
			CorrectSlugs: []string{},
			IsCorrect:    false,
		}
		return c.Status(fiber.StatusOK).JSON(result)
	}

	// Get all correct choices for this question
	var correctChoices []string
	allChoices := make(map[string]bool)
	for _, choice := range choices {
		allChoices[choice.Slug] = true
		if choice.IsCorrect {
			correctChoices = append(correctChoices, choice.Slug)
		}
	}

	// Validate that all selected choices exist
	validSubmission := true
	for _, selectedChoice := range submission.ChoiceSlug {
		if !allChoices[selectedChoice] {
			validSubmission = false
			break
		}
	}

	if !validSubmission {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Invalid choice selected for question: " + submission.QuestionSlug})
	}

	// Check if the selected choices match the correct choices exactly
	isCorrect := areSlicesEqual(submission.ChoiceSlug, correctChoices)

	// Create result for this question
	result = models_request.QuizCorrectResponse{
		QuestionSlug: submission.QuestionSlug,
		CorrectSlugs: correctChoices,
		IsCorrect:    isCorrect,
	}

	return c.Status(fiber.StatusOK).JSON(result)
}
