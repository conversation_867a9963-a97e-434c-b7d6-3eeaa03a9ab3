package controllers

import (
	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	models_request "med-api/models-request"
	"med-api/utils"

	"github.com/gofiber/fiber/v2"
)

func CreateQuiz(c *fiber.Ctx) error {
	var quizReq models_request.QuizRequest
	if err := c.BodyParser(&quizReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	Lesson := models.ContentLesson{}
	query := db.DB.Preload("Lesson.Course.Lecture")

	if userRole == "lecturer" {
		// Ensure lecturer can only create quizzes for their own courses
		query = query.Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Joins("JOIN courses ON lessons.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("content_lessons.content_slug = ?", quizReq.ContentLessonID).First(&Lesson).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "ContentLesson not found"})
	}

	quiz := models.Quiz{
		QuizName:        quizReq.QuizName,
		QuizDescription: quizReq.QuizDescription,
		QuizNumber:      quizReq.QuizNumber,
		QuizTime:        quizReq.QuizTime,
		QuizStatus:      quizReq.QuizStatus,
		Slug:            utils.GenerateUUIDSlug(),
		ContentLessonID: Lesson.ID,
	}
	if err := db.DB.Create(&quiz).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create quiz", "db_error": err.Error()})
	}

	for _, q := range quizReq.Question {
		question := models.Question{
			Title:      q.Question,
			Detail:     q.Detail,
			Image:      []byte(q.QuestionImage),
			TimeInsert: q.TimeInsert,
			Slug:       utils.GenerateUUIDSlug(),
			QuizID:     quiz.ID,
			QuizType:   q.QuestionType,
		}
		if err := db.DB.Create(&question).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create question"})
		}

		for _, ch := range q.Choice {
			choice := models.QuizChoices{
				Choice:         ch.Choice,
				ChoiceImage:    []byte(ch.ChoiceImage),
				IsCorrect:      ch.IsCorrect,
				Slug:           utils.GenerateUUIDSlug(),
				QuizChoiceType: ch.ChoiceType,
				QuizQuestionID: question.ID,
			}
			if err := db.DB.Create(&choice).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
			}
		}
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Quiz created successfully"})
}

func GetQuizByID(c *fiber.Ctx) error {
	slug := c.Params("slug")
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var quiz models.Quiz
	query := db.DB.Preload("ContentLesson.Lesson.Course.Lecture")

	if userRole == "lecturer" {
		// Filter quizzes to only show those from courses owned by the lecturer
		query = query.Joins("JOIN content_lessons ON quizs.content_lesson_id = content_lessons.id").
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Joins("JOIN courses ON lessons.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("slug = ?", slug).First(&quiz).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Quiz not found"})
	}

	var questions []models.Question
	db.DB.Where("quiz_id = ?", quiz.ID).Find(&questions)

	quizResp := models_api.QuizRespond{
		ID:              quiz.ID,
		QuizName:        quiz.QuizName,
		QuizDescription: quiz.QuizDescription,
		QuizNumber:      quiz.QuizNumber,
		QuizTime:        quiz.QuizTime,
		QuizStatus:      quiz.QuizStatus,
		Slug:            quiz.Slug,
		ContentSlug:     quiz.ContentLesson.ContentSlug,
		LessonSlug:      quiz.ContentLesson.Lesson.LessonSlug,
		CourseSlug:      quiz.ContentLesson.Lesson.Course.Slug,
		CourseName:      quiz.ContentLesson.Lesson.Course.CourseName,
	}

	for _, q := range questions {
		var choices []models.QuizChoices
		db.DB.Where("quiz_question_id = ?", q.ID).Find(&choices)

		choiceResps := make([]models_api.QuizChoiceRespond, 0, len(choices))
		for _, ch := range choices {
			choiceResps = append(choiceResps, models_api.QuizChoiceRespond{
				ID:          ch.ID,
				Choice:      ch.Choice,
				ChoiceImage: string(ch.ChoiceImage),
				IsCorrect:   ch.IsCorrect,
				ChoiceType:  ch.QuizChoiceType,
				Slug:        ch.Slug,
			})
		}

		quizResp.Questions = append(quizResp.Questions, models_api.QuizQuestionRespond{
			ID:            q.ID,
			Question:      q.Title,
			Detail:        q.Detail,
			QuestionImage: string(q.Image),
			TimeInsert:    q.TimeInsert,
			QuestionType:  q.QuizType,
			Slug:          q.Slug,
			Choices:       choiceResps,
		})
	}

	return c.Status(fiber.StatusOK).JSON(quizResp)
}

func UpdateQuiz(c *fiber.Ctx) error {
	slug := c.Params("slug")
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var quiz models.Quiz
	query := db.DB.Preload("ContentLesson.Lesson.Course.Lecture")

	if userRole == "lecturer" {
		// Filter quizzes to only allow updates to those from courses owned by the lecturer
		query = query.Joins("JOIN content_lessons ON quizs.content_lesson_id = content_lessons.id").
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Joins("JOIN courses ON lessons.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("quizs.slug = ?", slug).First(&quiz).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Quiz not found"})
	}

	var quizReq models_request.QuizRequest
	if err := c.BodyParser(&quizReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	Lesson := models.ContentLesson{}
	contentQuery := db.DB.Preload("Lesson.Course.Lecture")

	if userRole == "lecturer" {
		// Ensure lecturer can only update quizzes for their own courses
		contentQuery = contentQuery.Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Joins("JOIN courses ON lessons.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := contentQuery.Where("content_lessons.content_slug = ?", quizReq.ContentLessonID).First(&Lesson).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "ContentLesson not found"})
	}

	// Update quiz basic information
	quizUpdates := map[string]interface{}{
		"quiz_name":         quizReq.QuizName,
		"quiz_description":  quizReq.QuizDescription,
		"quiz_number":       quizReq.QuizNumber,
		"quiz_time":         quizReq.QuizTime,
		"quiz_status":       quizReq.QuizStatus,
		"content_lesson_id": Lesson.ID,
	}
	if err := db.DB.Model(&quiz).Updates(quizUpdates).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update quiz", "db_error": err.Error()})
	}

	// Track existing question and choice slugs
	existingQuestionSlugs := make(map[string]bool)
	existingChoiceSlugs := make(map[string]bool)

	if len(quizReq.Question) > 0 {
		for _, q := range quizReq.Question {
			var question models.Question
			if err := db.DB.Where("slug = ?", q.Slug).First(&question).Error; err == nil {
				// Update existing question
				questionUpdates := map[string]interface{}{
					"title":       q.Question,
					"detail":      q.Detail,
					"image":       []byte(q.QuestionImage),
					"time_insert": q.TimeInsert,
					"quiz_type":   q.QuestionType,
				}
				if err := db.DB.Model(&question).Updates(questionUpdates).Error; err != nil {
					return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update question"})
				}
			} else {
				// Create new question
				question = models.Question{
					Title:      q.Question,
					Detail:     q.Detail,
					Image:      []byte(q.QuestionImage),
					TimeInsert: q.TimeInsert,
					Slug:       utils.GenerateUUIDSlug(),
					QuizID:     quiz.ID,
					QuizType:   q.QuestionType,
				}
				if err := db.DB.Create(&question).Error; err != nil {
					return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create question"})
				}
			}

			existingQuestionSlugs[question.Slug] = true

			for _, ch := range q.Choice {
				var choice models.QuizChoices
				if err := db.DB.Where("slug = ?", ch.Slug).First(&choice).Error; err == nil {
					// Update existing choice
					choiceUpdates := map[string]interface{}{
						"choice":           ch.Choice,
						"choice_image":     []byte(ch.ChoiceImage),
						"is_correct":       ch.IsCorrect,
						"quiz_choice_type": ch.ChoiceType,
					}
					if err := db.DB.Model(&choice).Updates(choiceUpdates).Error; err != nil {
						return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update choice"})
					}
				} else {
					// Create new choice
					choice = models.QuizChoices{
						Choice:         ch.Choice,
						ChoiceImage:    []byte(ch.ChoiceImage),
						IsCorrect:      ch.IsCorrect,
						Slug:           utils.GenerateUUIDSlug(),
						QuizChoiceType: ch.ChoiceType,
						QuizQuestionID: question.ID,
					}
					if err := db.DB.Create(&choice).Error; err != nil {
						return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
					}
				}

				existingChoiceSlugs[choice.Slug] = true
			}
		}
	}

	// Delete old questions not in the new data
	var oldQuestions []models.Question
	db.DB.Where("quiz_id = ?", quiz.ID).Find(&oldQuestions)
	for _, oldQuestion := range oldQuestions {
		if !existingQuestionSlugs[oldQuestion.Slug] {
			db.DB.Delete(&oldQuestion)
		}
	}

	// Delete old choices not in the new data
	var oldChoices []models.QuizChoices
	db.DB.Where("quiz_question_id IN (SELECT id FROM questions WHERE quiz_id = ?)", quiz.ID).Find(&oldChoices)
	for _, oldChoice := range oldChoices {
		if !existingChoiceSlugs[oldChoice.Slug] {
			db.DB.Delete(&oldChoice)
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Quiz updated successfully"})
}

func DeleteQuiz(c *fiber.Ctx) error {
	slug := c.Params("slug")
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var quiz models.Quiz
	query := db.DB.Preload("ContentLesson.Lesson.Course.Lecture")

	if userRole == "lecturer" {
		// Filter quizzes to only allow deletion of those from courses owned by the lecturer
		query = query.Joins("JOIN content_lessons ON quizs.content_lesson_id = content_lessons.id").
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Joins("JOIN courses ON lessons.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("slug = ?", slug).First(&quiz).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Quiz not found"})
	}

	if err := db.DB.Delete(&quiz).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete quiz", "db_error": err.Error()})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Quiz deleted successfully"})
}

func GetAllQuizzes(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	var quizzes []models.Quiz
	query := db.DB.Preload("ContentLesson.Lesson.Course.Lecture")

	if userRole == "lecturer" {
		// Filter quizzes to only show those from courses owned by the lecturer
		query = query.Joins("JOIN content_lessons ON quizs.content_lesson_id = content_lessons.id").
			Joins("JOIN lessons ON content_lessons.lesson_id = lessons.id").
			Joins("JOIN courses ON lessons.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Find(&quizzes).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve quizzes"})
	}

	var resp []models_api.QuizRespond
	for _, quiz := range quizzes {
		var questions []models.Question
		db.DB.Where("quiz_id = ?", quiz.ID).Find(&questions)

		quizResp := models_api.QuizRespond{
			ID:              quiz.ID,
			QuizName:        quiz.QuizName,
			QuizDescription: quiz.QuizDescription,
			QuizNumber:      quiz.QuizNumber,
			QuizTime:        quiz.QuizTime,
			QuizStatus:      quiz.QuizStatus,
			Slug:            quiz.Slug,
			ContentSlug:     quiz.ContentLesson.ContentSlug,
			LessonSlug:      quiz.ContentLesson.Lesson.LessonSlug,
			CourseSlug:      quiz.ContentLesson.Lesson.Course.Slug,
			CourseName:      quiz.ContentLesson.Lesson.Course.CourseName,
		}

		for _, q := range questions {
			var choices []models.QuizChoices
			db.DB.Where("quiz_question_id = ?", q.ID).Find(&choices)

			choiceResps := make([]models_api.QuizChoiceRespond, 0, len(choices))
			for _, ch := range choices {
				choiceResps = append(choiceResps, models_api.QuizChoiceRespond{
					ID:         ch.ID,
					Choice:     ch.Choice,
					IsCorrect:  ch.IsCorrect,
					ChoiceType: ch.QuizChoiceType,
					Slug:       ch.Slug,
				})
			}

			quizResp.Questions = append(quizResp.Questions, models_api.QuizQuestionRespond{
				ID:           q.ID,
				Question:     q.Title,
				Detail:       q.Detail,
				TimeInsert:   q.TimeInsert,
				QuestionType: q.QuizType,
				Slug:         q.Slug,
				Choices:      choiceResps,
			})
		}
		resp = append(resp, quizResp)
	}

	return c.Status(fiber.StatusOK).JSON(resp)
}
