package controllers

import (
	"log"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"
	"med-api/utils"
	"time"

	"github.com/gofiber/fiber/v2"
)

func CreateExam(c *fiber.Ctx) error {
	var examReq models_request.ExamRequest
	if err := c.BodyParser(&examReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	userRole := c.Locals("role").(string)

	// Find course by slug with role-based filtering
	var course models.Courses
	query := db.DB

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("courses.slug = ?", examReq.CourseSlug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Course not found for provided slug"})
	}

	exam := models.Exam{
		ExamName:        examReq.ExamName,
		ExamDescription: examReq.ExamDescription,
		ExamTime:        examReq.ExamTime,
		ExamStatus:      examReq.ExamStatus,
		PassingScore:    examReq.PassingScore,
		Slug:            utils.GenerateUUIDSlug(),
		CourseID:        course.ID,
	}
	if err := db.DB.Create(&exam).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create exam", "db_error": err.Error()})
	}

	for _, q := range examReq.Questions {
		question := models.ExamQuestion{
			Question:      q.Question,
			QuestionImage: []byte(q.QuestionImage),
			Detail:        q.Detail,
			TimeInsert:    q.TimeInsert,
			QuestionType:  q.QuestionType,
			Slug:          utils.GenerateUUIDSlug(),
			ExamID:        exam.ID,
		}
		if err := db.DB.Create(&question).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create question"})
		}

		for _, ch := range q.Choices {
			choice := models.ExamChoice{
				Choice:         ch.Choice,
				ChoiceImage:    []byte(ch.ChoiceImage),
				IsCorrect:      ch.IsCorrect,
				ChoiceType:     ch.ChoiceType,
				Slug:           utils.GenerateUUIDSlug(),
				ExamQuestionID: question.ID,
			}
			if err := db.DB.Create(&choice).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
			}
		}
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Exam created successfully"})
}

func GetExamByID(c *fiber.Ctx) error {
	examSlug := c.Params("slug")
	userRole := c.Locals("role").(string)

	var exam models.Exam
	query := db.DB.Preload("Course")

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("exams.slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	// Get questions with choices
	var questions []models.ExamQuestion
	if err := db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&questions).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch questions"})
	}

	// Transform to ExamRequest format
	examRequest := models_request.ExamRequest{
		ExamName:        exam.ExamName,
		ExamDescription: exam.ExamDescription,
		ExamTime:        exam.ExamTime,
		ExamStatus:      exam.ExamStatus,
		PassingScore:    exam.PassingScore,
		ExamSlug:        exam.Slug,
		CourseSlug:      exam.Course.Slug,
		Questions:       make([]models_request.ExamQuestion, len(questions)),
	}

	// Transform questions
	for i, q := range questions {
		examRequest.Questions[i] = models_request.ExamQuestion{
			Question:      q.Question,
			QuestionImage: string(q.QuestionImage),
			Detail:        q.Detail,
			TimeInsert:    q.TimeInsert,
			QuestionType:  q.QuestionType,
			Choices:       make([]models_request.ExamChoice, len(q.Choices)),
		}

		// Transform choices
		for j, ch := range q.Choices {
			examRequest.Questions[i].Choices[j] = models_request.ExamChoice{
				Choice:      ch.Choice,
				ChoiceImage: string(ch.ChoiceImage),
				IsCorrect:   ch.IsCorrect,
				ChoiceType:  ch.ChoiceType,
			}
		}
	}

	return c.JSON(examRequest)
}

func GetExamsTable(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)

	var exams []models.Exam
	query := db.DB.Preload("Course")

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Find(&exams).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch exams"})
	}

	examTable := make([]fiber.Map, len(exams))
	for i, exam := range exams {
		var questionCount int64
		db.DB.Model(&models.ExamQuestion{}).Where("exam_id = ?", exam.ID).Count(&questionCount)

		examTable[i] = fiber.Map{
			"slug":          exam.Slug,
			"exam_name":     exam.ExamName,
			"course_name":   exam.Course.CourseName,
			"exam_amount":   questionCount,
			"passing_score": exam.PassingScore,
			"exam_time":     exam.ExamTime,
		}
	}

	return c.JSON(examTable)
}

func UpdateExamByID(c *fiber.Ctx) error {
	examSlug := c.Params("slug")
	userRole := c.Locals("role").(string)

	var examReq models_request.ExamRequest
	if err := c.BodyParser(&examReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find course by slug with role-based filtering
	var course models.Courses
	courseQuery := db.DB

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		courseQuery = courseQuery.Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := courseQuery.Where("courses.slug = ?", examReq.CourseSlug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Course not found for provided slug"})
	}

	// Find exam by slug with role-based filtering
	var exam models.Exam
	query := db.DB

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("exams.slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	// Update exam directly
	updateData := map[string]interface{}{
		"exam_name":        examReq.ExamName,
		"exam_description": examReq.ExamDescription,
		"exam_time":        examReq.ExamTime,
		"exam_status":      examReq.ExamStatus,
		"passing_score":    examReq.PassingScore,
		"course_id":        course.ID,
	}

	if err := db.DB.Model(&models.Exam{}).Where("slug = ?", examSlug).Updates(updateData).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update exam"})
	}

	// Get existing questions with their choices
	var existingQuestions []models.ExamQuestion
	if err := db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&existingQuestions).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch existing questions"})
	}

	// Map to track existing questions and choices
	existingQuestionMap := make(map[string]models.ExamQuestion)
	for _, q := range existingQuestions {
		existingQuestionMap[q.Slug] = q
	}

	// Update existing questions or create new ones
	newQuestionSlugs := make(map[string]bool)
	for _, q := range examReq.Questions {
		newQuestionSlugs[q.Slug] = true
		if existingQuestion, exists := existingQuestionMap[q.Slug]; exists {
			// Update existing question
			updateQuestionData := map[string]interface{}{
				"question":       q.Question,
				"question_image": []byte(q.QuestionImage),
				"detail":         q.Detail,
				"time_insert":    q.TimeInsert,
				"question_type":  q.QuestionType,
			}
			if err := db.DB.Model(&existingQuestion).Updates(updateQuestionData).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update question"})
			}

			// Handle choices for this question
			existingChoiceMap := make(map[string]models.ExamChoice)
			for _, ch := range existingQuestion.Choices {
				existingChoiceMap[ch.Slug] = ch
			}

			newChoiceSlugs := make(map[string]bool)
			for _, ch := range q.Choices {
				newChoiceSlugs[ch.Slug] = true
				if existingChoice, exists := existingChoiceMap[ch.Slug]; exists {
					// Update existing choice
					updateChoiceData := map[string]interface{}{
						"choice":       ch.Choice,
						"choice_image": []byte(ch.ChoiceImage),
						"is_correct":   ch.IsCorrect,
						"choice_type":  ch.ChoiceType,
					}
					if err := db.DB.Model(&existingChoice).Updates(updateChoiceData).Error; err != nil {
						return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update choice"})
					}
				} else {
					// Create new choice
					newChoice := models.ExamChoice{
						Choice:         ch.Choice,
						ChoiceImage:    []byte(ch.ChoiceImage),
						IsCorrect:      ch.IsCorrect,
						ChoiceType:     ch.ChoiceType,
						Slug:           utils.GenerateUUIDSlug(),
						ExamQuestionID: existingQuestion.ID,
					}
					if err := db.DB.Create(&newChoice).Error; err != nil {
						return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
					}
				}
			}

			// Delete old choices not in new data
			for _, existingChoice := range existingQuestion.Choices {
				if !newChoiceSlugs[existingChoice.Slug] {
					if err := db.DB.Delete(&existingChoice).Error; err != nil {
						return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete old choice"})
					}
				}
			}
		} else {
			// Create new question
			newQuestion := models.ExamQuestion{
				Question:      q.Question,
				QuestionImage: []byte(q.QuestionImage),
				Detail:        q.Detail,
				TimeInsert:    q.TimeInsert,
				QuestionType:  q.QuestionType,
				Slug:          utils.GenerateUUIDSlug(),
				ExamID:        exam.ID,
			}
			if err := db.DB.Create(&newQuestion).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create question"})
			}

			// Create choices for the new question
			for _, ch := range q.Choices {
				newChoice := models.ExamChoice{
					Choice:         ch.Choice,
					ChoiceImage:    []byte(ch.ChoiceImage),
					IsCorrect:      ch.IsCorrect,
					ChoiceType:     ch.ChoiceType,
					Slug:           utils.GenerateUUIDSlug(),
					ExamQuestionID: newQuestion.ID,
				}
				if err := db.DB.Create(&newChoice).Error; err != nil {
					return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
				}
			}
		}
	}

	// Delete old questions not in new data
	for _, existingQuestion := range existingQuestions {
		if !newQuestionSlugs[existingQuestion.Slug] {
			// Delete associated choices
			if err := db.DB.Where("exam_question_id = ?", existingQuestion.ID).Delete(&models.ExamChoice{}).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete old choices"})
			}
			// Delete question
			if err := db.DB.Delete(&existingQuestion).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete old question"})
			}
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Exam updated successfully"})
}

func DeleteExam(c *fiber.Ctx) error {
	slug := c.Params("slug")
	userRole := c.Locals("role").(string)

	var exam models.Exam
	query := db.DB

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("exams.slug = ?", slug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}
	if err := db.DB.Delete(&exam).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete exam", "db_error": err.Error()})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Exam deleted successfully"})
}

func DeleteExamByID(c *fiber.Ctx) error {
	examSlug := c.Params("slug")
	userRole := c.Locals("role").(string)

	var exam models.Exam
	query := db.DB

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Where("exams.slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}
	// Delete related questions and choices
	var questions []models.ExamQuestion
	db.DB.Where("exam_id = ?", exam.ID).Find(&questions)
	for _, q := range questions {
		db.DB.Where("exam_question_id = ?", q.ID).Delete(&models.ExamChoice{})
	}
	db.DB.Where("exam_id = ?", exam.ID).Delete(&models.ExamQuestion{})
	if err := db.DB.Delete(&exam).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete exam"})
	}
	return c.JSON(fiber.Map{"message": "Exam deleted successfully"})
}

func GetAllExams(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)

	var exams []models.Exam
	query := db.DB.Preload("Questions.Choices")

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		userSlug := c.Locals("user_slug").(string)
		query = query.Joins("JOIN courses ON exams.course_id = courses.id").
			Joins("JOIN user_infos ON courses.lecturer_id = user_infos.id").
			Where("user_infos.slug = ?", userSlug)
	}

	if err := query.Find(&exams).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch exams"})
	}
	return c.JSON(exams)
}

// FinalExam API functions based on frontend interface
func CreateFinalExam(c *fiber.Ctx) error {
	var finalExamReq models_request.FinalExamRequest
	if err := c.BodyParser(&finalExamReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find course by slug
	var course models.Courses
	if err := db.DB.Where("slug = ?", finalExamReq.CourseId).First(&course).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Course not found"})
	}

	// Create exam
	exam := models.Exam{
		ExamName:        finalExamReq.Name,
		ExamDescription: finalExamReq.Description,
		ExamTime:        finalExamReq.TimeLimit,
		ExamStatus:      true,
		PassingScore:    finalExamReq.PassingScore,
		Slug:            utils.GenerateUUIDSlug(),
		CourseID:        course.ID,
	}

	if err := db.DB.Create(&exam).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create exam"})
	}

	// Create questions and choices
	for _, q := range finalExamReq.Questions {
		question := models.ExamQuestion{
			Question:      q.Title,
			QuestionImage: []byte(q.ImageUrl), // Convert URL to bytes if needed
			Detail:        q.Content,
			TimeInsert:    0,
			QuestionType:  getQuestionTypeID(q.Type),
			Slug:          utils.GenerateUUIDSlug(),
			ExamID:        exam.ID,
		}

		if err := db.DB.Create(&question).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create question"})
		}

		for _, ch := range q.Choices {
			choice := models.ExamChoice{
				Choice:         ch.Content,
				ChoiceImage:    []byte(ch.ImageUrl), // Convert URL to bytes if needed
				IsCorrect:      ch.IsCorrect,
				ChoiceType:     getChoiceTypeID(ch.Type),
				Slug:           utils.GenerateUUIDSlug(),
				ExamQuestionID: question.ID,
			}

			if err := db.DB.Create(&choice).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
			}
		}
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Final exam created successfully",
		"exam_id": exam.Slug,
	})
}

func GetFinalExamsByCourse(c *fiber.Ctx) error {
	courseSlug := c.Params("courseSlug")

	// Find course by slug
	var course models.Courses
	if err := db.DB.Where("slug = ?", courseSlug).First(&course).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Course not found"})
	}

	// Get all exams for this course
	var exams []models.Exam
	if err := db.DB.Where("course_id = ?", course.ID).Find(&exams).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to fetch exams"})
	}

	// Convert to frontend format
	var finalExams []map[string]interface{}
	for _, exam := range exams {
		// Get questions for this exam
		var questions []models.ExamQuestion
		db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&questions)

		// Convert questions to frontend format
		var frontendQuestions []map[string]interface{}
		for _, q := range questions {
			var choices []map[string]interface{}
			for _, ch := range q.Choices {
				choices = append(choices, map[string]interface{}{
					"id":       ch.Slug,
					"content":  ch.Choice,
					"type":     getChoiceTypeName(ch.ChoiceType),
					"imageUrl": string(ch.ChoiceImage),
				})
			}

			frontendQuestions = append(frontendQuestions, map[string]interface{}{
				"id":       q.Slug,
				"title":    q.Question,
				"type":     getQuestionTypeName(q.QuestionType),
				"content":  q.Detail,
				"imageUrl": string(q.QuestionImage),
				"choices":  choices,
			})
		}

		finalExams = append(finalExams, map[string]interface{}{
			"examSlug":     exam.Slug,
			"id":           exam.Slug, // Keep for backward compatibility
			"slug":         exam.Slug, // Keep for consistency
			"name":         exam.ExamName,
			"description":  exam.ExamDescription,
			"courseId":     courseSlug,
			"questions":    frontendQuestions,
			"passingScore": exam.PassingScore,
			"timeLimit":    exam.ExamTime,
		})
	}

	return c.Status(fiber.StatusOK).JSON(finalExams)
}

func GetFinalExamBySlug(c *fiber.Ctx) error {
	examSlug := c.Params("examSlug")

	var exam models.Exam
	if err := db.DB.Preload("Course").Where("slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	// Get questions with choices
	var questions []models.ExamQuestion
	db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&questions)

	// Convert to frontend format
	var frontendQuestions []map[string]interface{}
	for _, q := range questions {
		var choices []map[string]interface{}
		for _, ch := range q.Choices {
			choices = append(choices, map[string]interface{}{
				"slug":     ch.Slug,
				"content":  ch.Choice,
				"type":     getChoiceTypeName(ch.ChoiceType),
				"imageUrl": string(ch.ChoiceImage),
			})
		}

		frontendQuestions = append(frontendQuestions, map[string]interface{}{
			"slug":     q.Slug,
			"title":    q.Question,
			"type":     getQuestionTypeName(q.QuestionType),
			"content":  q.Detail,
			"imageUrl": string(q.QuestionImage),
			"choices":  choices,
		})
	}

	finalExam := map[string]interface{}{
		"examSlug":     exam.Slug,
		"slug":         exam.Slug, // Keep both for backward compatibility
		"name":         exam.ExamName,
		"description":  exam.ExamDescription,
		"courseId":     exam.Course.Slug,
		"questions":    frontendQuestions,
		"passingScore": exam.PassingScore,
		"timeLimit":    exam.ExamTime,
	}

	return c.Status(fiber.StatusOK).JSON(finalExam)
}

func UpdateFinalExam(c *fiber.Ctx) error {
	examSlug := c.Params("examSlug")
	var finalExamReq models_request.FinalExamRequest

	if err := c.BodyParser(&finalExamReq); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find exam by slug
	var exam models.Exam
	if err := db.DB.Where("slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	// Update exam details
	exam.ExamName = finalExamReq.Name
	exam.ExamDescription = finalExamReq.Description
	exam.ExamTime = finalExamReq.TimeLimit
	exam.PassingScore = finalExamReq.PassingScore

	if err := db.DB.Save(&exam).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update exam"})
	}

	// Delete existing questions and choices
	db.DB.Where("exam_id = ?", exam.ID).Delete(&models.ExamQuestion{})

	// Create new questions and choices
	for _, q := range finalExamReq.Questions {
		question := models.ExamQuestion{
			Question:      q.Title,
			QuestionImage: []byte(q.ImageUrl),
			Detail:        q.Content,
			TimeInsert:    0,
			QuestionType:  getQuestionTypeID(q.Type),
			Slug:          utils.GenerateUUIDSlug(),
			ExamID:        exam.ID,
		}

		if err := db.DB.Create(&question).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create question"})
		}

		for _, ch := range q.Choices {
			choice := models.ExamChoice{
				Choice:         ch.Content,
				ChoiceImage:    []byte(ch.ImageUrl),
				IsCorrect:      ch.IsCorrect,
				ChoiceType:     getChoiceTypeID(ch.Type),
				Slug:           utils.GenerateUUIDSlug(),
				ExamQuestionID: question.ID,
			}

			if err := db.DB.Create(&choice).Error; err != nil {
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create choice"})
			}
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Final exam updated successfully"})
}

func DeleteFinalExam(c *fiber.Ctx) error {
	examSlug := c.Params("examSlug")

	var exam models.Exam
	if err := db.DB.Where("slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	// Delete exam (cascade will handle questions and choices)
	if err := db.DB.Delete(&exam).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete exam"})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{"message": "Final exam deleted successfully"})
}

// Helper functions for type conversion
func getQuestionTypeID(typeName string) uint {
	switch typeName {
	case "text":
		return 1
	case "image":
		return 2
	case "text_image":
		return 3
	case "video":
		return 4
	default:
		return 1
	}
}

func getQuestionTypeName(typeID uint) string {
	switch typeID {
	case 1:
		return "text"
	case 2:
		return "image"
	case 3:
		return "text_image"
	case 4:
		return "video"
	default:
		return "text"
	}
}

func getChoiceTypeID(typeName string) uint {
	switch typeName {
	case "text":
		return 1
	case "image":
		return 2
	default:
		return 1
	}
}

func getChoiceTypeName(typeID uint) string {
	switch typeID {
	case 1:
		return "text"
	case 2:
		return "image"
	default:
		return "text"
	}
}

// Request struct for answer submission
// Now using only selectedChoiceSlug as an array for both single and multiple choice
// Single choice: { "answers": [ { "questionSlug": "q1", "selectedChoiceSlug": ["c1"] } ] }
// Multiple choice: { "answers": [ { "questionSlug": "q1", "selectedChoiceSlug": ["c1", "c3"] } ] }
type ExamAnswerSubmission struct {
	Answers []struct {
		QuestionSlug       string   `json:"questionSlug"`
		SelectedChoiceSlug []string `json:"selectedChoiceSlug"` // Array for both single and multiple choice
	} `json:"answers"`
}

// Response struct for answer checking
// Example: { "score": 8, "total": 10, "passed": true, "details": [ { "questionSlug": "q1", "correct": true, "userAnswers": ["choice1"] }, ... ] }
type ExamCheckResult struct {
	Score   int  `json:"score"`
	Total   int  `json:"total"`
	Passed  bool `json:"passed"`
	Details []struct {
		QuestionSlug   string   `json:"questionSlug"`
		Correct        bool     `json:"correct"`
		UserAnswers    []string `json:"userAnswers"`              // Show all user selected answers
		CorrectAnswers []string `json:"correctAnswers,omitempty"` // Show all correct answers
	} `json:"details"`
}

// POST /final-exams/:examSlug/submit
func SubmitFinalExamAnswers(c *fiber.Ctx) error {
	examSlug := c.Params("examSlug")
	var submission ExamAnswerSubmission
	if err := c.BodyParser(&submission); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Debug: Log the submission data
	log.Printf("DEBUG: Exam submission for examSlug: %s", examSlug)
	log.Printf("DEBUG: Number of answers received: %d", len(submission.Answers))
	for i, ans := range submission.Answers {
		log.Printf("DEBUG: Answer %d - QuestionSlug: %s, SelectedChoiceSlug: %v",
			i, ans.QuestionSlug, ans.SelectedChoiceSlug)
	}

	// Get exam and questions
	var exam models.Exam
	if err := db.DB.Where("slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}
	var questions []models.ExamQuestion
	db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&questions)

	// Debug: Log questions and choices
	log.Printf("DEBUG: Found %d questions for exam", len(questions))
	for _, q := range questions {
		log.Printf("DEBUG: Question %s has %d choices", q.Slug, len(q.Choices))
		for _, ch := range q.Choices {
			log.Printf("DEBUG: Choice %s - IsCorrect: %v", ch.Slug, ch.IsCorrect)
		}
	}

	// Map for quick lookup
	questionMap := make(map[string]models.ExamQuestion)
	for _, q := range questions {
		questionMap[q.Slug] = q
	}

	score := 0
	details := make([]struct {
		QuestionSlug   string   `json:"questionSlug"`
		Correct        bool     `json:"correct"`
		UserAnswers    []string `json:"userAnswers"`              // Show all user selected answers
		CorrectAnswers []string `json:"correctAnswers,omitempty"` // Show all correct answers
	}, 0, len(submission.Answers))
	total := len(questions)

	for _, ans := range submission.Answers {
		q, ok := questionMap[ans.QuestionSlug]
		if !ok {
			log.Printf("DEBUG: Question not found for slug: %s", ans.QuestionSlug)
			continue // skip unknown question
		}

		// Find correct choices for this question
		correctChoices := make(map[string]bool)
		correctAnswerSlugs := make([]string, 0)
		for _, ch := range q.Choices {
			if ch.IsCorrect {
				correctChoices[ch.Slug] = true
				correctAnswerSlugs = append(correctAnswerSlugs, ch.Slug)
			}
		}
		log.Printf("DEBUG: Question %s has correct choices: %v", ans.QuestionSlug, correctAnswerSlugs)

		// Get user's selected choices - now using selectedChoiceSlug as array
		var userChoices map[string]bool
		var userAnswerDisplay []string

		if len(ans.SelectedChoiceSlug) > 0 {
			// Process the array of selected choices
			userChoices = make(map[string]bool)
			for _, slug := range ans.SelectedChoiceSlug {
				if slug != "" { // Skip empty strings
					userChoices[slug] = true
				}
			}
			userAnswerDisplay = ans.SelectedChoiceSlug
			log.Printf("DEBUG: User selected choices: %v (count: %d)", ans.SelectedChoiceSlug, len(userChoices))
		} else {
			// No answer provided
			userChoices = make(map[string]bool)
			userAnswerDisplay = []string{}
			log.Printf("DEBUG: No answer provided for question %s", ans.QuestionSlug)
		}

		// Check if user's answer matches correct choices EXACTLY
		// User must select exactly the same choices as correct answers, no more, no less
		isCorrect := len(userChoices) == len(correctChoices)
		if isCorrect {
			// Check that all user choices are correct
			for slug := range userChoices {
				if !correctChoices[slug] {
					isCorrect = false
					log.Printf("DEBUG: User selected incorrect choice: %s", slug)
					break
				}
			}
		}

		if !isCorrect && len(userChoices) != len(correctChoices) {
			log.Printf("DEBUG: Question %s - WRONG: User selected %d choices but %d are correct",
				ans.QuestionSlug, len(userChoices), len(correctChoices))
		}

		log.Printf("DEBUG: Question %s - User correct: %v (user: %v, correct: %v)",
			ans.QuestionSlug, isCorrect, userAnswerDisplay, correctAnswerSlugs)

		if isCorrect {
			score++
		}

		details = append(details, struct {
			QuestionSlug   string   `json:"questionSlug"`
			Correct        bool     `json:"correct"`
			UserAnswers    []string `json:"userAnswers"`              // Show all user selected answers
			CorrectAnswers []string `json:"correctAnswers,omitempty"` // Show all correct answers
		}{
			QuestionSlug:   ans.QuestionSlug,
			Correct:        isCorrect,
			UserAnswers:    userAnswerDisplay, // Now shows all selected choices
			CorrectAnswers: correctAnswerSlugs,
		})
	}

	passed := score*100/total >= exam.PassingScore

	// Get user by slug from context (set by auth middleware)
	userSlug := c.Locals("user_slug").(string)
	if userSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug is required"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Create exam record to save submission
	examRecord := models.ExamRecord{
		Score:    score,
		SubmitAt: time.Now(),
		ExamID:   exam.ID,
		UserID:   user.ID,
	}

	// Save the exam record to database
	if err := db.DB.Create(&examRecord).Error; err != nil {
		log.Printf("ERROR: Failed to save exam record: %v", err)
		// Continue to return result even if record save fails
	} else {
		log.Printf("DEBUG: Exam record saved with ID: %d for user %d, exam %d, score %d",
			examRecord.ID, user.ID, exam.ID, score)
	}

	result := ExamCheckResult{
		Score:   score,
		Total:   total,
		Passed:  passed,
		Details: details,
	}
	return c.Status(fiber.StatusOK).JSON(result)
}

// DEBUG: GET /final-exams/:examSlug/debug
func DebugExamData(c *fiber.Ctx) error {
	examSlug := c.Params("examSlug")

	// Get exam and questions
	var exam models.Exam
	if err := db.DB.Where("slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	var questions []models.ExamQuestion
	db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&questions)

	debugData := map[string]interface{}{
		"examSlug":  examSlug,
		"examName":  exam.ExamName,
		"questions": make([]map[string]interface{}, len(questions)),
	}

	for i, q := range questions {
		choices := make([]map[string]interface{}, len(q.Choices))
		for j, ch := range q.Choices {
			choices[j] = map[string]interface{}{
				"slug":      ch.Slug,
				"choice":    ch.Choice,
				"isCorrect": ch.IsCorrect,
			}
		}

		debugData["questions"].([]map[string]interface{})[i] = map[string]interface{}{
			"slug":    q.Slug,
			"title":   q.Question,
			"choices": choices,
		}
	}

	return c.JSON(debugData)
}

// Helper endpoint: GET /final-exams/:examSlug/structure
// Returns the exam structure showing question types and choice information for testing
func GetExamStructure(c *fiber.Ctx) error {
	examSlug := c.Params("examSlug")

	// Get exam and questions
	var exam models.Exam
	if err := db.DB.Where("slug = ?", examSlug).First(&exam).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Exam not found"})
	}

	var questions []models.ExamQuestion
	db.DB.Preload("Choices").Where("exam_id = ?", exam.ID).Find(&questions)

	examStructure := map[string]interface{}{
		"examSlug":       examSlug,
		"examName":       exam.ExamName,
		"totalQuestions": len(questions),
		"questions":      make([]map[string]interface{}, len(questions)),
	}

	for i, q := range questions {
		choices := make([]map[string]interface{}, len(q.Choices))
		correctChoices := make([]string, 0)

		for j, ch := range q.Choices {
			choices[j] = map[string]interface{}{
				"slug":      ch.Slug,
				"choice":    ch.Choice,
				"isCorrect": ch.IsCorrect,
			}
			if ch.IsCorrect {
				correctChoices = append(correctChoices, ch.Slug)
			}
		}

		questionType := "single-choice"
		if len(correctChoices) > 1 {
			questionType = "multiple-choice"
		}

		examStructure["questions"].([]map[string]interface{})[i] = map[string]interface{}{
			"slug":                q.Slug,
			"title":               q.Question,
			"type":                questionType,
			"totalChoices":        len(q.Choices),
			"correctChoicesCount": len(correctChoices),
			"correctChoices":      correctChoices, // Only for testing/debugging
			"choices":             choices,
			"suggestedFormat":     getSuggestedSubmissionFormat(q.Slug, correctChoices),
		}
	}

	return c.JSON(examStructure)
}

// Helper function to suggest submission format based on question type
func getSuggestedSubmissionFormat(questionSlug string, correctChoices []string) map[string]interface{} {
	return map[string]interface{}{
		"type": "array-format", // Always array format now
		"example": map[string]interface{}{
			"questionSlug":       questionSlug,
			"selectedChoiceSlug": correctChoices, // Always an array
		},
		"note": "selectedChoiceSlug is always an array, whether single or multiple choice",
	}
}

/*
UPDATED EXAM SUBMISSION FORMAT:

✅ NEW FORMAT - Only selectedChoiceSlug as array:

1. Single Choice Question:
{
  "answers": [
    {
      "questionSlug": "what-is-dna",
      "selectedChoiceSlug": ["deoxyribonucleic-acid"]
    }
  ]
}

2. Multiple Choice Question:
{
  "answers": [
    {
      "questionSlug": "which-are-mammals",
      "selectedChoiceSlug": ["dog", "cat", "whale"]
    }
  ]
}

✅ EXACT MATCHING LOGIC:
- If correct answers are ["choice1", "choice2"]
- User selects ["choice1", "choice2"] → ✅ CORRECT (gets point)
- User selects ["choice1"] → ❌ WRONG (missing choice2)
- User selects ["choice1", "choice2", "choice3"] → ❌ WRONG (extra choice3)
- User selects ["choice1", "choice3"] → ❌ WRONG (wrong choice)

✅ RESPONSE FORMAT:
{
  "score": 8,
  "total": 10,
  "passed": true,
  "details": [
    {
      "questionSlug": "q1",
      "correct": true,
      "userAnswers": ["choice1", "choice2"],
      "correctAnswers": ["choice1", "choice2"]
    }
  ]
}
*/
/*
Example API Usage for Exam Submission:

1. Single Choice Question:
POST /final-exams/biology-101/submit
{
  "answers": [
    {
      "questionSlug": "what-is-dna",
      "selectedChoiceSlug": ["deoxyribonucleic-acid"]
    }
  ]
}

2. Multiple Choice Question (select all correct answers):
POST /final-exams/biology-101/submit
{
  "answers": [
    {
      "questionSlug": "which-are-mammals",
      "selectedChoiceSlug": ["dog", "cat", "whale"]
    }
  ]
}

3. Mixed Question Types:
POST /final-exams/biology-101/submit
{
  "answers": [
    {
      "questionSlug": "what-is-dna",
      "selectedChoiceSlug": ["deoxyribonucleic-acid"]
    },
    {
      "questionSlug": "which-are-mammals",
      "selectedChoiceSlug": ["dog", "cat", "whale"]
    }
  ]
}

Scoring Logic: User must select EXACTLY the correct choices.
- If correct answers are [choice1, choice2] and user selects [choice1, choice2, choice3], it's WRONG.
- User must select exactly [choice1, choice2] to get the point.
*/
