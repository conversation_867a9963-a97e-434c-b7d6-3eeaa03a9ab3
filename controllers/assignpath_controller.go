package controllers

import (
	"med-api/db"
	"med-api/models"

	"github.com/gofiber/fiber/v2"
)

// Assign multiple paths to a user by user_slug
func AssignPathToUser(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	currentUserSlug := c.Locals("user_slug").(string)

	type AssignRequest struct {
		PathID       uint `json:"path_id"`
		LearningType bool `json:"learning_type"`
	}
	type Req struct {
		UserSlug string          `json:"user_slug"`
		Assign   []AssignRequest `json:"assign"`
	}
	var req Req
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", req.UserSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		// Lecturers can only assign paths to themselves or users they have permission to modify
		if req.UserSlug != currentUserSlug {
			// Restrict lecturers from assigning paths to admin users
			if user.RoleID == 1 { // Assuming role_id 1 is admin
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "Access denied - cannot assign paths to admin users"})
			}
		}
	}

	// Process each assignment
	var assignments []models.AssignPath
	for _, assign := range req.Assign {
		// Check if path exists
		var path models.CoursePath
		if err := db.DB.First(&path, assign.PathID).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found", "path_id": assign.PathID})
		}

		ap := models.AssignPath{
			UserID:       user.ID,
			PathID:       assign.PathID,
			LearningType: assign.LearningType,
		}
		assignments = append(assignments, ap)
	}

	// Create all assignments
	if err := db.DB.Create(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message":   "Paths assigned to user successfully",
		"user_slug": req.UserSlug,
	})
}

// Get all assigned users with their details and assigned paths
func GetAllAssignedUsers(c *fiber.Ctx) error {
	userRole := c.Locals("role").(string)
	userSlug := c.Locals("user_slug").(string)

	type AssignedPath struct {
		PathID       uint   `json:"path_id"`
		PathName     string `json:"path_name"`
		LearningType bool   `json:"learning_type"`
	}

	type AssignedUser struct {
		UserSlug      string         `json:"user_slug"`
		FName         string         `json:"fname"`
		LName         string         `json:"lname"`
		Picture       string         `json:"picture"`
		Email         string         `json:"email"`
		Position      string         `json:"position"`
		Status        bool           `json:"status"`
		AssignedPaths []AssignedPath `json:"assigned_paths"`
	}

	// Get all unique users who have path assignments
	var assignedUsers []AssignedUser

	// Use a map to group assignments by user
	userAssignments := make(map[uint]*AssignedUser)

	// Get all assignments with user and path details
	var assignments []models.AssignPath
	query := db.DB.Preload("User").Preload("Path")

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		// For lecturers, only show users they have assigned or users in their scope
		// Assuming lecturers can only see users they have direct access to
		var currentUser models.UserInfo
		if err := db.DB.Where("slug = ?", userSlug).First(&currentUser).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Current user not found"})
		}

		// Here you can add specific filtering logic for lecturers
		// For example, if lecturers can only see users from their department or assigned to them
		// This is a basic implementation - adjust based on your business logic
		query = query.Joins("JOIN user_infos ON assign_paths.user_id = user_infos.id").
			Where("user_infos.role_id != ? OR assign_paths.user_id = ?", 1, currentUser.ID) // Assuming role_id 1 is admin
	}

	if err := query.Find(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve assignments"})
	}

	// Group assignments by user
	for _, assignment := range assignments {
		userID := assignment.UserID

		// If user not in map, add them
		if _, exists := userAssignments[userID]; !exists {
			userAssignments[userID] = &AssignedUser{
				UserSlug:      assignment.User.Slug,
				FName:         assignment.User.FirstName,
				LName:         assignment.User.LastName,
				Picture:       string(assignment.User.Picture),
				Email:         assignment.User.Email,
				Position:      assignment.User.Position,
				Status:        assignment.User.Status,
				AssignedPaths: []AssignedPath{},
			}
		}

		// Add the path to user's assigned paths
		assignedPath := AssignedPath{
			PathID:   assignment.Path.ID,
			PathName: assignment.Path.Name,
		}
		userAssignments[userID].AssignedPaths = append(userAssignments[userID].AssignedPaths, assignedPath)
	}

	// Convert map to slice
	for _, user := range userAssignments {
		assignedUsers = append(assignedUsers, *user)
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"data": assignedUsers,
	})
}

// Get assigned path data for a specific user by user slug
func GetUserAssignedPaths(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	userRole := c.Locals("role").(string)
	currentUserSlug := c.Locals("user_slug").(string)

	type AssignedPathData struct {
		PathID          uint   `json:"path_id"`
		PathName        string `json:"path_name"`
		PathDescription string `json:"path_description"`
		LearningType    bool   `json:"learning_type"`
	}

	type UserAssignedPathsResponse struct {
		UserSlug      string             `json:"user_slug"`
		FName         string             `json:"fname"`
		LName         string             `json:"lname"`
		AssignedPaths []AssignedPathData `json:"assigned_paths"`
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		// Lecturers can only view their own assigned paths or users they have access to
		if userSlug != currentUserSlug {
			// Additional logic to check if lecturer has access to this user
			// For now, restrict lecturers to only view their own data
			var currentUser models.UserInfo
			if err := db.DB.Where("slug = ?", currentUserSlug).First(&currentUser).Error; err != nil {
				return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Current user not found"})
			}

			// Check if the requested user is accessible by this lecturer
			// This could be based on department, assigned courses, etc.
			// For basic implementation, only allow access to non-admin users
			if user.RoleID == 1 { // Assuming role_id 1 is admin
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "Access denied"})
			}
		}
	}

	// Get user's assignments with path details
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ?", user.ID).Preload("Path").Find(&assignments).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user assignments"})
	}

	// Build assigned paths data
	var assignedPaths []AssignedPathData
	for _, assignment := range assignments {
		pathData := AssignedPathData{
			PathID:          assignment.Path.ID,
			PathName:        assignment.Path.Name,
			PathDescription: assignment.Path.PathDescription,
			LearningType:    assignment.LearningType,
		}
		assignedPaths = append(assignedPaths, pathData)
	}

	// Build response
	response := UserAssignedPathsResponse{
		UserSlug:      user.Slug,
		FName:         user.FirstName,
		LName:         user.LastName,
		AssignedPaths: assignedPaths,
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"data": response,
	})
}

func UpdateUserAssignedPaths(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	userRole := c.Locals("role").(string)
	currentUserSlug := c.Locals("user_slug").(string)

	type UpdateAssignRequest struct {
		PathID       uint `json:"path_id"`
		LearningType bool `json:"learning_type"`
	}

	type UpdateReq struct {
		Assign []UpdateAssignRequest `json:"assigned_paths"`
	}

	var req UpdateReq
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		// Lecturers can only update their own assigned paths or users they have permission to modify
		if userSlug != currentUserSlug {
			// Additional permission check for lecturers
			var currentUser models.UserInfo
			if err := db.DB.Where("slug = ?", currentUserSlug).First(&currentUser).Error; err != nil {
				return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Current user not found"})
			}

			// Restrict lecturers from modifying admin users
			if user.RoleID == 1 { // Assuming role_id 1 is admin
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "Access denied - cannot modify admin users"})
			}
		}
	}

	// ❌ Delete all existing assignments for this user
	if err := db.DB.Where("user_id = ?", user.ID).Delete(&models.AssignPath{}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete old assignments"})
	}

	// ✅ Create new assignments
	for _, assign := range req.Assign {
		// Check if path exists
		var path models.CoursePath
		if err := db.DB.First(&path, assign.PathID).Error; err != nil {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Path not found", "path_id": assign.PathID})
		}

		newAssign := models.AssignPath{
			UserID:       user.ID,
			PathID:       assign.PathID,
			LearningType: assign.LearningType,
		}

		if err := db.DB.Create(&newAssign).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error":   "Failed to create assignment",
				"path_id": assign.PathID,
			})
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message":   "User assigned paths updated successfully",
		"user_slug": userSlug,
	})
}

func AssignPathDel(c *fiber.Ctx) error {
	id := c.Params("id")
	slug := c.Params("slug")
	userRole := c.Locals("role").(string)
	currentUserSlug := c.Locals("user_slug").(string)

	if id == "" || slug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Missing required parameters"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", slug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		// Lecturers can only delete their own assignments or users they have permission to modify
		if slug != currentUserSlug {
			// Restrict lecturers from modifying admin users
			if user.RoleID == 1 { // Assuming role_id 1 is admin
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "Access denied - cannot modify admin users"})
			}
		}
	}

	// Delete assignments for the user and specified paths
	if err := db.DB.Where("user_id = ? AND path_id IN (?)", user.ID, id).Delete(&models.AssignPath{}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete assignments"})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message":   "Assignments deleted successfully",
		"user_slug": slug,
	})
}

func AssignPathDelByUser(c *fiber.Ctx) error {
	slug := c.Params("slug")
	userRole := c.Locals("role").(string)
	currentUserSlug := c.Locals("user_slug").(string)

	if slug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Missing user slug"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", slug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Filter data if user role is lecturer
	if userRole == "lecturer" {
		// Lecturers can only delete their own assignments or users they have permission to modify
		if slug != currentUserSlug {
			// Restrict lecturers from modifying admin users
			if user.RoleID == 1 { // Assuming role_id 1 is admin
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{"error": "Access denied - cannot modify admin users"})
			}
		}
	}

	// Delete all assignments for the user
	if err := db.DB.Where("user_id = ?", user.ID).Delete(&models.AssignPath{}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to delete assignments"})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message":   "All assignments deleted successfully",
		"user_slug": slug,
	})
}
