package controllers

import (
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"

	"github.com/gofiber/fiber/v2"
)

// GetOverViewUserDashboard returns dashboard overview data for authenticated user
// Returns total courses and total certificates
func GetOverViewUserDashboard(c *fiber.Ctx) error {
	userSlug := c.Locals("user_slug").(string)

	// Fetch user from database using slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get total courses assigned to user through learning paths
	totalCourses, err := getTotalUserCourses(user.ID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user courses"})
	}

	// Get total certificates for user
	var totalCertificates int64
	if err := db.DB.Model(&models.Certificate{}).Where("user_id = ?", user.ID).Count(&totalCertificates).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user certificates"})
	}

	// Create response
	overview := models_request.OverViewUserDashboard{
		TotalCourses:      int(totalCourses),
		TotalCertificates: int(totalCertificates),
	}

	return c.Status(fiber.StatusOK).JSON(overview)
}

// getTotalUserCourses calculates the total number of courses assigned to a user
func getTotalUserCourses(userID uint) (int64, error) {
	// Get user's assigned paths
	var assignments []models.AssignPath
	if err := db.DB.Where("user_id = ?", userID).Find(&assignments).Error; err != nil {
		return 0, err
	}

	if len(assignments) == 0 {
		return 0, nil
	}

	// Extract path IDs
	var pathIDs []uint
	for _, assignment := range assignments {
		pathIDs = append(pathIDs, assignment.PathID)
	}

	// Count unique courses across all assigned paths
	var totalCourses int64
	if err := db.DB.Model(&models.CourseStore{}).
		Where("course_path_id IN ?", pathIDs).
		Distinct("course_id").
		Count(&totalCourses).Error; err != nil {
		return 0, err
	}

	return totalCourses, nil
}

// GetOverViewUserDashboardBySlug returns dashboard overview data for a specific user by slug
// This endpoint allows fetching overview data for any user by their slug (admin/lecturer use)
func GetOverViewUserDashboardBySlug(c *fiber.Ctx) error {
	userSlug := c.Params("user_slug")
	if userSlug == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User slug is required"})
	}

	// Find user by slug
	var user models.UserInfo
	if err := db.DB.Where("slug = ?", userSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "User not found"})
	}

	// Get total courses assigned to user through learning paths
	totalCourses, err := getTotalUserCourses(user.ID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user courses"})
	}

	// Get total certificates for user
	var totalCertificates int64
	if err := db.DB.Model(&models.Certificate{}).Where("user_id = ?", user.ID).Count(&totalCertificates).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to retrieve user certificates"})
	}

	// Create response
	overview := models_request.OverViewUserDashboard{
		TotalCourses:      int(totalCourses),
		TotalCertificates: int(totalCertificates),
	}

	return c.Status(fiber.StatusOK).JSON(overview)
}
