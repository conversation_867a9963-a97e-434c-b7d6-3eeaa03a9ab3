package models

type Exam struct {
	ID              uint   `json:"id" gorm:"primaryKey"`
	ExamName        string `json:"exam_name" binding:"required"`
	ExamDescription string `json:"exam_description"`
	ExamTime        int    `json:"exam_time"`
	ExamStatus      bool   `json:"exam_status"`
	PassingScore    int    `json:"passing_score" binding:"required"`
	Slug            string `json:"exam_slug" gorm:"unique"`

	CourseID uint    `json:"course_id"`
	Course   Courses `json:"course" gorm:"foreignKey:CourseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

type ExamQuestion struct {
	ID            uint         `json:"id" gorm:"primaryKey"`
	Question      string       `json:"question" binding:"required"`
	QuestionImage []byte       `json:"question_image,omitempty" gorm:"type:longblob"`
	Detail        string       `json:"detail"`
	TimeInsert    int          `json:"time_insert"`
	QuestionType  uint         `json:"question_type"`
	ExamID        uint         `json:"exam_id"`
	Exam          Exam         `json:"exam" gorm:"foreignKey:ExamID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Choices       []ExamChoice `json:"choices" gorm:"foreignKey:ExamQuestionID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Slug          string       `json:"question_slug" gorm:"unique"`
}

type ExamChoice struct {
	ID             uint   `json:"id" gorm:"primaryKey"`
	Choice         string `json:"choice" binding:"required"`
	ChoiceImage    []byte `json:"choice_image,omitempty" gorm:"type:longblob"`
	IsCorrect      bool   `json:"is_correct"`
	ChoiceType     uint   `json:"choice_type"`
	ExamQuestionID uint   `json:"exam_question_id"`
	Slug           string `json:"choice_slug" gorm:"unique"`
}
