package models

type Question struct {
	ID         uint   `json:"id" gorm:"primaryKey"`
	Title      string `json:"title" binding:"required"`
	Detail     string `json:"question_detail" binding:"required"`
	Image      []byte `json:"question_image,omitempty" gorm:"type:longblob"`
	TimeInsert int    `json:"time_insert" binding:"required"`
	Slug       string `json:"slug" gorm:"unique"`

	QuizID uint `json:"quiz_id"`
	Quiz   Quiz `json:"quiz" gorm:"foreignKey:QuizID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	QuizType uint        `json:"quiz_type"`
	TypeQuiz ContentType `json:"type_quiz" gorm:"foreignKey:QuizType;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
