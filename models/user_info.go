package models

import (
	"time"
)

type UserInfo struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	Picture   []byte `json:"course_picture" gorm:"type:longblob"`
	Username  string `json:"username" gorm:"unique" binding:"required"`
	Password  string `json:"password" binding:"required"`
	FirstName string `json:"user_fname" binding:"required"`
	LastName  string `json:"user_lname" binding:"required"`
	Email     string `json:"user_email" gorm:"unique" binding:"email"`
	Position  string `json:"user_position"`
	LoginDate string `json:"user_login_date"`
	Status    bool   `json:"user_status"`
	Slug      string `json:"user_slug"`

	RoleID uint `json:"role_id"`
	Role   Role `json:"role" gorm:"foreignKey:RoleID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	// ✅ OTP Fields สำหรับ Reset Password
	OTP          *string    `json:"otp" gorm:"default:null"`
	OTPExpiresAt *time.Time `json:"otp_expires_at" gorm:"default:null"`
	OTPUsed      bool       `json:"otp_used" gorm:"default:false"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
