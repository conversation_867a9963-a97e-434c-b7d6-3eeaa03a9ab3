package models

type Lessons struct {
	ID                uint   `json:"id" gorm:"primaryKey"`
	LessonName        string `json:"lesson_name"  binding:"required"`
	LessonTime        string `json:"lesson_time"`
	LessonDescription string `json:"lesson_description"`
	LessonsOrder      int    `json:"lesson_order"`
	LessonSlug        string `json:"lesson_slug" gorm:"unique"`

	CourseID uint    `json:"course_id"`
	Course   Courses `json:"course" gorm:"foreignKey:CourseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
