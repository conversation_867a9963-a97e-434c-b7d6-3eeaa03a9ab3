package models

type Certificate struct {
	ID              uint   `json:"id" gorm:"primaryKey"`
	CertificateDate string `json:"certificate_date" gorm:"type:date"`

	UserID   uint     `json:"user_id"`
	User     UserInfo `json:"user" gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	CourseID uint     `json:"course_id"`
	Course   Courses  `json:"course" gorm:"foreignKey:CourseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
