package models

type QuizChoices struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Choice      string `json:"quiz_choice" binding:"required"`
	ChoiceImage []byte `json:"quiz_choice_image,omitempty" gorm:"type:longblob"`
	IsCorrect   bool   `json:"is_correct" binding:"required"`
	Slug        string `json:"slug" gorm:"unique"`

	QuizChoiceType uint        `json:"quiz_choice_type"`
	ChoiceType     ContentType `json:"choice_type" gorm:"foreignKey:QuizChoiceType;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	QuizQuestionID uint     `json:"quiz_question_id"`
	QuizQuestion   Question `json:"quiz_question" gorm:"foreignKey:QuizQuestionID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
