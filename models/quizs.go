package models

type Quiz struct {
	ID              uint    `json:"id" gorm:"primaryKey"`
	QuizName        string  `json:"quiz_name" binding:"required"`
	QuizDescription string  `json:"quiz_description"`
	QuizNumber      int     `json:"quiz_number"`
	QuizTime        float32 `json:"quiz_time"`
	QuizStatus      bool    `json:"quiz_status"`
	Slug            string  `json:"slug" gorm:"unique"`

	ContentLessonID uint          `json:"content_lesson_id"`
	ContentLesson   ContentLesson `json:"content_lesson" gorm:"foreignKey:ContentLessonID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
