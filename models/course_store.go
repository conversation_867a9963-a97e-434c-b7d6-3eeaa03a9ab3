package models

type CourseStore struct {
	CoursePathID uint `json:"course_path_id" gorm:"primaryKey;"`
	CourseID     uint `json:"course_id" gorm:"primaryKey;"`
	StoreOrder   int  `json:"store_order"`

	CoursePath CoursePath `json:"course_path" gorm:"foreignKey:CoursePathID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Course     Courses    `json:"course" gorm:"foreignKey:CourseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
