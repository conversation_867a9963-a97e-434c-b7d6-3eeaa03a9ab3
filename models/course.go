package models

type Courses struct {
	ID                uint   `json:"id" gorm:"primaryKey;autoIncrement"`
	CourseName        string `json:"course_name" gorm:"unique" binding:"required"`
	CoursePicture     []byte `json:"course_picture" gorm:"type:longblob"`
	CourseDescription string `json:"course_description"`
	CourseInstruction string `json:"course_instruction"`
	CourseDifficulty  string `json:"course_difficulty"`
	CourseDuration    string `json:"course_duration"`
	CourseStatus      bool   `json:"course_status"`
	CourseCertificate bool   `json:"course_certificate"`
	Slug              string `json:"course_slug"`

	LecturerID uint     `json:"user_id"`
	Lecture    UserInfo `json:"user" gorm:"foreignKey:LecturerID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
