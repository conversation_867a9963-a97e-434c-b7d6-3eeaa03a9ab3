package models

import "time"

type ContentProgress struct {
	ContentLessonID uint          `json:"content_lesson_id" gorm:"primaryKey"`
	CourseRecord    string        `json:"course_record" gorm:"-"`
	LessonRecord    string        `json:"lesson_record" gorm:"-"`
	CompletedAt     time.Time     `json:"completed_at,omitempty" gorm:"autoCreateTime"`
	ContentLesson   ContentLesson `json:"content_lesson" gorm:"references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	UserID uint     `json:"user_id" gorm:"primaryKey"`
	User   UserInfo `json:"user" gorm:"references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
