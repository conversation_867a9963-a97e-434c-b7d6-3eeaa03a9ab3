package models

type AssignPath struct {
	UserID uint     `json:"user_id" gorm:"column:user_id;uniqueIndex:idx_user_path"`
	User   UserInfo `json:"user" gorm:"foreignKey:UserID"`

	PathID uint       `json:"path_id" gorm:"column:path_id;uniqueIndex:idx_user_path"`
	Path   CoursePath `json:"path" gorm:"foreignKey:PathID"`

	LearningType bool `json:"course_learning_type"` // Assuming this is a boolean indicating the type of path
}

type DeleteAssignPathRequest struct {
	UserSlug string `json:"user_slug" binding:"required"`
	PathIDs  []uint `json:"path_ids" binding:"required"`
}
