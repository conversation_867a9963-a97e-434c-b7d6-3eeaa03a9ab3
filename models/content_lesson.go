package models

type ContentLesson struct {
	ID                 uint   `json:"id" gorm:"primaryKey"`
	ContentLessonName  string `json:"content_lesson_name"`
	ContentDescription string `json:"content_lesson_description"`
	LessonTime         int    `json:"lesson_time"`
	ContentOrder       int    `json:"content_lesson_order"`
	ContentSlug        string `json:"content_lesson_slug" gorm:"unique"`

	ContentLessonType uint        `json:"content_lesson_type"`
	ContentType       ContentType `json:"content_type" gorm:"foreignKey:ContentLessonType;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	LessonID uint    `json:"lesson_id"`
	Lesson   Lessons `json:"lesson" gorm:"foreignKey:LessonID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
