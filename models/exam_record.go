package models

import "time"

type ExamRecord struct {
	ID       uint      `json:"id" gorm:"primaryKey"`
	Score    int       `json:"score" binding:"required"`
	SubmitAt time.Time `json:"submit_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`

	ExamID uint `json:"exam_id"`
	Exam   Exam `json:"Exam" gorm:"foreignKey:ExamID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	UserID uint     `json:"user_id"`
	User   UserInfo `json:"user" gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}
