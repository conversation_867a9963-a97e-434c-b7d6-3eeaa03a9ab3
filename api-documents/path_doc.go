package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRoutePath(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/path",
		Description: "Create a new course path",
		Responses: map[string]string{
			"201": "Created",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.CreatePath,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_request.CreatePathRequest{},
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/path",
		Description: "Get all course paths",
		Responses: map[string]string{
			"200": "OK",
			"500": "Internal Server Error",
		},
		Handler:         controllers.GetAllPaths,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: []models_request.LearningPathResponse{},
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/path/table",
		Description: "Get all course paths in table format",
		Responses: map[string]string{
			"200": "OK",
			"500": "Internal Server Error",
		},
		Handler:         controllers.GetPathTable,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: []models_request.LearningPathResponseTable{},
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/path/:id",
		Description: "Get a course path by ID",
		Responses: map[string]string{
			"200": "OK",
			"404": "Not Found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetPathByID,
		Params: []notelink.Parameter{
			{
				Name:        "id",
				In:          "path",
				Required:    true,
				Description: "ID of the course path",
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/path/:id",
		Description: "Update a course path by ID",
		Responses: map[string]string{
			"200": "OK",
			"400": "Bad Request",
			"404": "Not Found",
			"500": "Internal Server Error",
		},
		Handler: controllers.UpdatePathByID,
		Params: []notelink.Parameter{
			{
				Name:        "id",
				In:          "path",
				Required:    true,
				Description: "ID of the course path to update",
			},
		},
		SchemasRequest:  models_request.CreatePathRequest{},
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/path/:id",
		Description: "Delete a course path by ID",
		Responses: map[string]string{
			"200": "Path deleted successfully",
			"404": "Path not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.DeletePathByID,
		Params: []notelink.Parameter{
			{
				Name:        "id",
				In:          "path",
				Required:    true,
				Description: "ID of the course path to delete",
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
