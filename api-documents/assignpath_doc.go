package api_documents

import (
	"med-api/controllers"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteAssignPath(api *notelink.ApiNote) {
	// POST /assign-path - Assign multiple paths to a user
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/assign-path",
		Description: "Assign paths to users",
		Responses: map[string]string{
			"201": "Paths assigned to user successfully",
			"400": "Bad Request",
			"404": "User or Path not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.AssignPathToUser,
		Params:  []notelink.Parameter{},
	})

	// GET /assign-path - Get all assigned users with their details and assigned paths
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/assign-path",
		Description: "Get all users who have path assignments.",
		Responses: map[string]string{
			"200": "Successfully retrieved all assigned users",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetAllAssignedUsers,
		Params:  []notelink.Parameter{},
	})

	// GET /assign-path/:user_slug - Get assigned path data for a specific user by user slug
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/assign-path/:user_slug",
		Description: "Get assigned path data for a specific user by user slug..",
		Responses: map[string]string{
			"200": "Successfully retrieved user assigned paths",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetUserAssignedPaths,
		Params: []notelink.Parameter{
			{
				Name:        "user_slug",
				In:          "path",
				Description: "The user slug to get assigned paths for",
				Required:    true,
			},
		},
	})

	// PUT /assign-path/:user_slug - Update assigned paths for a user by user slug
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/assign-path/:user_slug",
		Description: "Update assigned paths and learning types for a specific user by user slug.",
		Responses: map[string]string{
			"200": "User assigned paths updated successfully",
			"400": "Bad Request",
			"404": "User, Path, or Assignment not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.UpdateUserAssignedPaths,
		Params: []notelink.Parameter{
			{
				Name:        "user_slug",
				In:          "path",
				Description: "The user slug to update assigned paths for",
				Required:    true,
			},
		},
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/assign-path/:slug/:id",
		Description: "Delete assigned paths for a user.",
		Responses: map[string]string{
			"200": "Assigned paths deleted successfully",
			"400": "Bad Request",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.AssignPathDel,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Description: "The user slug to delete assigned paths for",
				Required:    true,
			},
			{
				Name:        "id",
				In:          "path",
				Description: "Comma-separated list of path IDs to delete assignments for",
				Required:    true,
			},
		}, // No response schema needed
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/assign-path/:slug",
		Description: "Delete all assigned paths for a user by user slug.",
		Responses: map[string]string{
			"200": "All assigned paths deleted successfully",
			"400": "Bad Request",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.AssignPathDelByUser,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Description: "The user slug to delete all assigned paths for",
				Required:    true,
			},
		},
	})
}
