package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteCertificatesCourse(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/certificates/course/:course_slug",
		Description: "Get course certificates for authenticated user by course slug",
		Responses: map[string]string{
			"200": "Success",
			"401": "Unauthorized",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetCertificatesCourseByUserSlug,
		Params: []notelink.Parameter{
			{
				Name:        "course_slug",
				In:          "path",
				Description: "Course slug to filter certificates (optional)",
				Required:    false,
				Type:        "string",
			},
		},
		SchemasResponse: []models_request.CertificateCourse{},
	})
}

func NoteRouteCertificatesPath(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/certificates/path/:path_slug",
		Description: "Get path certificates for authenticated user by path slug",
		Responses: map[string]string{
			"200": "Success",
			"401": "Unauthorized",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetCertificatesPathByUserSlug,
		Params: []notelink.Parameter{
			{
				Name:        "path_slug",
				In:          "path",
				Description: "Path slug to filter certificates (optional)",
				Required:    false,
				Type:        "string",
			},
		},
		SchemasResponse: []models_request.CertificatePath{},
	})
}
