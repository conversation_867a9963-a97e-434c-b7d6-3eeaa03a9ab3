package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteQuizCreate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/quizs",
		Description: "Create quiz with questions and choices",
		Responses: map[string]string{
			"201": "Quiz created successfully",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.CreateQuiz,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_request.QuizRequest{},
		SchemasResponse: nil,
	})
}

func NoteRouteGetAllQuiz(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/quizs",
		Description: "Get all quizzes",
		Responses: map[string]string{
			"200": "Success",
			"500": "Internal Server Error",
		},
		Handler:         controllers.GetAllQuizzes,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteQuizGetByID(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/quizs/:slug",
		Description: "Get quiz by slug",
		Responses: map[string]string{
			"200": "Success",
			"404": "Quiz not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetQuizByID,
		Params: []notelink.Parameter{{
			Name:        "slug",
			In:          "path",
			Type:        "string",
			Description: "Slug of the quiz to retrieve",
			Required:    true,
		}},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteQuizUpdate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/quizs/:slug",
		Description: "Update quiz by slug",
		Responses: map[string]string{
			"200": "Quiz updated successfully",
			"400": "Bad Request",
			"404": "Quiz not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.UpdateQuiz,
		Params: []notelink.Parameter{{
			Name:        "slug",
			In:          "path",
			Type:        "string",
			Description: "Slug of the quiz to update",
			Required:    true,
		}},
		SchemasRequest:  models_request.QuizRequest{},
		SchemasResponse: nil,
	})
}

func NoteRouteQuizDelete(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/quizs/:slug",
		Description: "Delete quiz by slug",
		Responses: map[string]string{
			"200": "Quiz deleted successfully",
			"404": "Quiz not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.DeleteQuiz,
		Params: []notelink.Parameter{{
			Name:        "slug",
			In:          "path",
			Type:        "string",
			Description: "Slug of the quiz to delete",
			Required:    true,
		}},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
