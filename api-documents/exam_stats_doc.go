package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

// NoteRouteExamStats documents the exam statistics list endpoint
func NoteRouteExamStats(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/exam-stats",
		Description: "Retrieve a paginated list of exams with their statistics including student counts, pass rates, and scores",
		Responses: map[string]string{
			"200": "List of exams with statistics",
			"400": "Bad Request",
			"401": "Unauthorized",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetExamStats,
		Params: []notelink.Parameter{
			{
				Name:        "page",
				Type:        "int",
				Description: "Page number for pagination (default: 1)",
				Required:    false,
			},
			{
				Name:        "limit",
				Type:        "int",
				Description: "Number of items per page (default: 5, max: 100)",
				Required:    false,
			},
			{
				Name:        "search",
				Type:        "string",
				Description: "Search term to filter exams by name or course name",
				Required:    false,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: models_request.ExamStatsResponse{},
	})
}

// NoteRouteExamDetailStats documents the exam detail statistics endpoint
func NoteRouteExamDetailStats(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/exam-stats/:exam_slug",
		Description: "Retrieve detailed statistics for a specific exam including student results, scores, and completion rates",
		Responses: map[string]string{
			"200": "Detailed exam statistics with student results",
			"400": "Bad Request",
			"401": "Unauthorized",
			"404": "Exam not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetExamDetailStats,
		Params: []notelink.Parameter{
			{
				Name:        "exam_slug",
				Type:        "string",
				Description: "The slug of the exam to get statistics for",
				Required:    true,
			},
			{
				Name:        "page",
				Type:        "int",
				Description: "Page number for pagination (default: 1)",
				Required:    false,
			},
			{
				Name:        "limit",
				Type:        "int",
				Description: "Number of students per page (default: 10, max: 100)",
				Required:    false,
			},
			{
				Name:        "search",
				Type:        "string",
				Description: "Search term to filter students by name",
				Required:    false,
			},
			{
				Name:        "status",
				Type:        "string",
				Description: "Filter students by exam status (passed, failed, not_taken)",
				Required:    false,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: models_request.ExamDetailStats{},
	})
}

// NoteRouteExamTrackingDetails documents the exam tracking details endpoint
func NoteRouteExamTrackingDetails(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/exam-stats/details/:exam_slug",
		Description: "Get detailed exam tracking data with student attempts and scores",
		Responses: map[string]string{
			"200": "Exam tracking details with student attempts",
			"400": "Bad Request",
			"401": "Unauthorized",
			"403": "Forbidden",
			"404": "Exam not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetExamTrackingDetails,
		Params: []notelink.Parameter{
			{
				Name:        "exam_slug",
				Type:        "string",
				Description: "Exam slug identifier",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: models_request.ExamTrackData{},
	})
}
