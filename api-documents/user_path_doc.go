package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteUserPathDashboard(api *notelink.ApiNote) {
	// Route for getting all user path dashboard data
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/dashboard/paths",
		Handler:     controllers.GetUserPathDashboard,
		Description: "Retrieve user path dashboard data with all assigned learning paths",
		Responses: map[string]string{
			"200": "Success",
			"400": "Bad Request - User slug is required",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Params: []notelink.Parameter{
			{
				Name:        "page",
				In:          "query",
				Type:        "integer",
				Description: "Page number for pagination (default is 1)",
				Required:    false,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: []models_request.UserPathDashboard{},
	})

	// Route for getting user path dashboard data filtered by type
	// api.DocumentedRoute(notelink.DocumentedRouteInput{
	// 	Method:      "GET",
	// 	Path:        "/user/{user_slug}/dashboard/paths/type",
	// 	Handler:     controllers.GetUserPathDashboardByType,
	// 	Description: "Retrieve user path dashboard data filtered by learning type",
	// 	Responses: map[string]string{
	// 		"200": "Success",
	// 		"400": "Bad Request - User slug is required",
	// 		"404": "User not found",
	// 		"500": "Internal Server Error",
	// 	},
	// 	Params: []notelink.Parameter{
	// 		{
	// 			Name:        "user_slug",
	// 			In:          "path",
	// 			Type:        "string",
	// 			Description: "Slug of the user to retrieve path dashboard for",
	// 			Required:    true,
	// 		},
	// 		{
	// 			Name:        "type",
	// 			In:          "query",
	// 			Type:        "string",
	// 			Description: "Learning type filter (true/false or 1/0). Default is 'true'",
	// 			Required:    false,
	// 		},
	// 	},
	// 	SchemasRequest:  nil,
	// 	SchemasResponse: []models_request.UserPathDashboard{},
	// })
}
