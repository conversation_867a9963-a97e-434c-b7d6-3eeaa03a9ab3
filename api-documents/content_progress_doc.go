package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteMarkContentProgress(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/content-progress/complete",
		Description: "Mark a content lesson as completed for a user",
		Responses: map[string]string{
			"201": "Created - Content lesson marked as completed",
			"400": "Bad Request - Invalid request body",
			"401": "Unauthorized",
			"404": "Not Found - User or content lesson not found",
			"409": "Conflict - Content lesson already completed",
			"500": "Internal Server Error",
		},
		Handler:         controllers.MarkContentProgress,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_request.ContentProgressRequest{},
		SchemasResponse: map[string]interface{}{},
	})
}
