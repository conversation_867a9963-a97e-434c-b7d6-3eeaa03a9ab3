package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteUserCourseDashboard(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/dashboard/courses",
		Handler:     controllers.GetUserCoursesDashboard,
		Description: "Retrieve user course dashboard data",
		Responses: map[string]string{
			"200": "Success",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Params: []notelink.Parameter{
			{
				Name:        "page",
				In:          "query",
				Type:        "integer",
				Description: "Page number for pagination (default is 1)",
				Required:    false,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: []models_request.UserCourse{},
	})
}

func NoteRouteGetUserCours(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/user-course/:slug",
		Description: "Get user course information",
		Responses: map[string]string{
			"200": "User course fetched successfully",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetUserCourseSlug,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Type:        "integer",
				Description: "Slug of the course to update",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
