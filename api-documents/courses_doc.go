package api_documents

import (
	"med-api/controllers"
	models_api "med-api/models-api"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteCourses(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/courses",
		Description: "List courses",
		Responses: map[string]string{
			"200": "Success",
			"401": "Unauthorized",
		},
		Handler:         controllers.GetCourses,
		Params:          []notelink.Parameter{},
		SchemasResponse: models_api.CourseResponse{},
	})
}

func NoteRouteCoursesID(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/courses/:slug",
		Description: "Get courses by slug",
		Responses: map[string]string{
			"200": "Success",
			"404": "Courses not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetCourseID,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Type:        "integer",
				Description: "Slug of the course to retrieve",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteCoursesCreate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/courses",
		Description: "Create courses",
		Responses: map[string]string{
			"201": "Created",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.CreateCourse,
		SchemasRequest:  models_request.RequestCourse{},
		SchemasResponse: nil,
	})
}

func NoteRouteCoursesUpdate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/courses/:slug",
		Description: "Update courses",
		Responses: map[string]string{
			"200": "Success",
			"400": "Invalid Slug",
			"500": "Internal Server Error",
		},
		Handler: controllers.UpdateCourse,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Type:        "integer",
				Description: "Slug of the course to update",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteCoursesDelete(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/courses/:slug",
		Description: "Delete courses",
		Responses: map[string]string{
			"200": "Course deleted successfully",
			"404": "Course not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.DeleteCourse,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path", // It's a path param like /courses/:id
				Type:        "integer",
				Description: "Slug of the course to delete",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
