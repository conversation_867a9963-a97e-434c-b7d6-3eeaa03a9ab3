package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

// NoteRouteOverviewDashboard documents the route for getting overview dashboard for authenticated user
func NoteRouteOverviewDashboard(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/dashboard/overview-dashboard",
		Handler:     controllers.GetOverViewUserDashboard,
		Description: "Retrieve overview dashboard data for authenticated user (total courses and certificates)",
		Responses: map[string]string{
			"200": "Success - Returns overview dashboard data",
			"401": "Unauthorized - Invalid or missing authentication token",
			"404": "Not Found - User not found",
			"500": "Internal Server Error - Failed to retrieve data",
		},
		Params:          nil, // No parameters needed, uses JWT token
		SchemasRequest:  nil, // No request body
		SchemasResponse: models_request.OverViewUserDashboard{},
	})
}

// NoteRouteOverviewDashboardBySlug documents the route for getting overview dashboard by user slug
func NoteRouteOverviewDashboardBySlug(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/overview-dashboard/:user_slug",
		Handler:     controllers.GetOverViewUserDashboardBySlug,
		Description: "Retrieve overview dashboard data for a specific user by slug (admin/lecturer use)",
		Responses: map[string]string{
			"200": "Success - Returns overview dashboard data",
			"400": "Bad Request - User slug is required",
			"404": "Not Found - User not found",
			"500": "Internal Server Error - Failed to retrieve data",
		},
		Params: []notelink.Parameter{
			{
				Name:        "user_slug",
				In:          "path",
				Type:        "string",
				Description: "Slug of the user to retrieve overview dashboard for",
			},
		},
		SchemasRequest:  nil, // No request body
		SchemasResponse: models_request.OverViewUserDashboard{},
	})
}

// NoteRouteOverviewPathDashboard documents the route for getting path overview dashboard for authenticated user
func NoteRouteOverviewPathDashboard(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/dashboard/overview-path",
		Handler:     controllers.GetOverViewUserPath,
		Description: "Retrieve path overview dashboard data for authenticated user (total paths, completed, in progress, and average time)",
		Responses: map[string]string{
			"200": "Success - Returns path overview dashboard data",
			"401": "Unauthorized - Invalid or missing authentication token",
			"404": "Not Found - User not found",
			"500": "Internal Server Error - Failed to retrieve data",
		},
		Params:          nil, // No parameters needed, uses JWT token
		SchemasRequest:  nil, // No request body
		SchemasResponse: models_request.OverviewPath{},
	})
}

// NoteRouteOverviewPathDashboardBySlug documents the route for getting path overview dashboard by user slug
func NoteRouteOverviewPathDashboardBySlug(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/overview-path/:user_slug",
		Handler:     controllers.GetOverViewUserPathBySlug,
		Description: "Retrieve path overview dashboard data for a specific user by slug (admin/lecturer use)",
		Responses: map[string]string{
			"200": "Success - Returns path overview dashboard data",
			"400": "Bad Request - User slug is required",
			"404": "Not Found - User not found",
			"500": "Internal Server Error - Failed to retrieve data",
		},
		Params: []notelink.Parameter{
			{
				Name:        "user_slug",
				In:          "path",
				Type:        "string",
				Description: "Slug of the user to retrieve path overview dashboard for",
				Required:    true,
			},
		},
		SchemasRequest:  nil, // No request body
		SchemasResponse: models_request.OverviewPath{},
	})
}
