package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteExamCreate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/exams",
		Description: "Create exam with questions and choices",
		Responses: map[string]string{
			"201": "Exam created successfully",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.CreateExam,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_request.ExamRequest{},
		SchemasResponse: nil,
	})
}

func NoteRouteExamGetByID(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/exams/:slug",
		Description: "Get exam by slug, including questions and choices",
		Responses: map[string]string{
			"200": "Exam found",
			"404": "Exam not found",
		},
		Handler: controllers.GetExamByID,
		Params: []notelink.Parameter{
			{Name: "slug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteExamGetAll(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/exams",
		Description: "Get all exams, including questions and choices for each exam",
		Responses: map[string]string{
			"200": "List of exams returned",
		},
		Handler:         controllers.GetAllExams,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteExamGetTable(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/exams/table",
		Description: "Get exam table with basic information",
		Responses: map[string]string{
			"200": "List of exams returned",
			"500": "Internal Server Error",
		},
		Handler:         controllers.GetExamsTable,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}

func NoteRouteExamUpdateByID(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/exams/:slug",
		Description: "Update exam by slug, including questions and choices",
		Responses: map[string]string{
			"200": "Exam updated successfully",
			"400": "Bad Request",
			"404": "Exam not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.UpdateExamByID,
		Params: []notelink.Parameter{
			{Name: "slug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  models_request.ExamRequest{},
		SchemasResponse: nil,
	})
}

func NoteRouteExamDeleteByID(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/exams/:slug",
		Description: "Delete exam by slug, including all related questions and choices",
		Responses: map[string]string{
			"200": "Exam deleted successfully",
			"404": "Exam not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.DeleteExamByID,
		Params: []notelink.Parameter{
			{Name: "slug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
