package api_documents

import (
	"med-api/controllers"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteGetCourseDispla(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/display-course/:slug",
		Description: "Get course display information with lessons and content details",
		Responses: map[string]string{
			"200": "Course display data fetched successfully",
			"400": "Bad Request - Course slug is required",
			"404": "Course not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetUserDisplayCourse,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Type:        "string",
				Description: "Slug of the course to retrieve display information",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
