package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteTrackLearning(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/track-learning",
		Description: "Get track learning data for lecturer - shows courses with their paths, assigned student count, and course status",
		Responses: map[string]string{
			"200": "Success",
			"401": "Unauthorized",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetTrackLearning,
		Params: []notelink.Parameter{
			{
				Name:        "page",
				In:          "query",
				Type:        "integer",
				Description: "Page number for pagination (default: 1)",
				Required:    false,
			},
			{
				Name:        "search",
				In:          "query",
				Type:        "string",
				Description: "Search term to filter courses by name",
				Required:    false,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: &models_request.TrackLearningResponse{},
	})
}

func NoteRouteTrackLearningByCourse(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/track-learning/:course_id",
		Description: "Get detailed track learning data for a specific course",
		Responses: map[string]string{
			"200": "Success",
			"400": "Bad Request - Invalid course ID",
			"404": "Course not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetTrackLearningByCourse,
		Params: []notelink.Parameter{
			{
				Name:        "course_id",
				In:          "path",
				Type:        "integer",
				Description: "ID of the course to retrieve track learning data for",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: &models_request.TrackLearningRow{},
	})
}

func NoteRouteTrackLearningCourses(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/track-learning-courses",
		Description: "Get all courses overview for track learning - shows course statistics and student counts",
		Responses: map[string]string{
			"200": "Success",
			"401": "Unauthorized",
			"500": "Internal Server Error",
		},
		Handler:         controllers.GetTrackLearningCourses,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: &models_request.TrackLearningCoursesResponse{},
	})
}

func NoteRouteTrackLearningDetail(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/track-learning-detail/:course_slug",
		Description: "Get detailed student tracking data for a specific course - includes individual student progress, current lessons, and completion status",
		Responses: map[string]string{
			"200": "Success",
			"400": "Bad Request",
			"404": "Course not found or access denied",
			"401": "Unauthorized",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetTrackLearningDetail,
		Params: []notelink.Parameter{
			{
				Name:        "course_slug",
				In:          "path",
				Type:        "string",
				Description: "Course slug to get detailed tracking data for",
				Required:    true,
			},
			{
				Name:        "page",
				In:          "query",
				Type:        "integer",
				Description: "Page number for pagination (default: 1)",
				Required:    false,
			},
			{
				Name:        "limit",
				In:          "query",
				Type:        "integer",
				Description: "Number of items per page (default: 9, max: 100)",
				Required:    false,
			},
			{
				Name:        "search",
				In:          "query",
				Type:        "string",
				Description: "Search term to filter students by name",
				Required:    false,
			},
			{
				Name:        "status",
				In:          "query",
				Type:        "string",
				Description: "Filter by completion status: 'completed', 'not-completed', or empty for all",
				Required:    false,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: &models_request.TrackLearningDetailResponse{},
	})
}
