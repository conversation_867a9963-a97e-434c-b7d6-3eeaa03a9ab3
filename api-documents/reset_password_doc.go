package api_documents

import (
	"med-api/controllers"
	models_api "med-api/models-api"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteRequestOTP(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/auth/request-otp",
		Description: "Request OTP for password reset",
		Responses: map[string]string{
			"200": "OTP sent successfully",
			"400": "Invalid input",
			"404": "Email not found",
			"500": "Internal Server Error",
		},
		Handler:         controllers.RequestOTP,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_api.RequestOTPRequest{},
		SchemasResponse: models_api.OTPResponse{},
	})
}

func NoteRouteVerifyOTP(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/auth/verify-otp",
		Description: "Verify OTP code",
		Responses: map[string]string{
			"200": "OTP verified successfully",
			"400": "Invalid or expired OTP",
			"500": "Internal Server Error",
		},
		Handler:         controllers.VerifyOTP,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_api.VerifyOTPRequest{},
		SchemasResponse: models_api.OTPResponse{},
	})
}

func NoteRouteResetPassword(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/auth/reset-password",
		Description: "Reset password with OTP",
		Responses: map[string]string{
			"200": "Password reset successfully",
			"400": "Invalid input or OTP",
			"500": "Internal Server Error",
		},
		Handler:         controllers.ResetPassword,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_api.ResetPasswordRequest{},
		SchemasResponse: models_api.OTPResponse{},
	})
}
