package api_documents

import (
	"med-api/controllers"
	models_request "med-api/models-request"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteGetQuizDisplayByContentSlug(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/user/quiz-display/content/:content_slug",
		Description: "Get quiz display data by content slug",
		Responses: map[string]string{
			"200": "Quiz display data retrieved successfully",
			"400": "Bad Request - Content slug is required",
			"404": "Content not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetQuizDisplayByContentSlug,
		Params: []notelink.Parameter{
			{
				Name:        "content_slug",
				Type:        "string",
				In:          "path",
				Required:    true,
				Description: "The slug of the content lesson",
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: models_request.ContentDisplay{},
	})
}

func NoteRouteSubmitQuizAnswers(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/user/quiz-display/submit",
		Description: "Submit quiz answers and get results",
		Responses: map[string]string{
			"200": "Quiz submitted successfully with results",
			"400": "Bad Request - Invalid request format or invalid choice selected",
			"500": "Internal Server Error",
		},
		Handler:         controllers.SubmitQuizAnswers,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_request.QuizAnswerSubmission{},
		SchemasResponse: models_request.QuizCorrectResponse{},
	})
}
