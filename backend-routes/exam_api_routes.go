package backend_routes

import (
	api_documents "med-api/api-documents"
	"med-api/controllers"

	"github.com/canvas-tech-horizon/notelink"
)

func ExamApiRoutes(api *notelink.ApiNote) {
	api_documents.NoteRouteExamCreate(api)
	api_documents.NoteRouteExamGetAll(api)
	api_documents.NoteRouteExamGetTable(api)
	api_documents.NoteRouteExamGetByID(api)
	api_documents.NoteRouteExamUpdateByID(api)
	api_documents.NoteRouteExamDeleteByID(api)

	// FinalExam routes
	// api.DocumentedRoute(notelink.DocumentedRouteInput{
	// 	Method:      "POST",
	// 	Path:        "/final-exams",
	// 	Description: "Create a final exam for a course (with questions and choices)",
	// 	Responses: map[string]string{
	// 		"201": "Final exam created successfully",
	// 		"400": "Bad Request",
	// 		"500": "Internal Server Error",
	// 	},
	// 	Handler:         controllers.CreateFinalExam,
	// 	Params:          []notelink.Parameter{},
	// 	SchemasRequest:  nil, // models_request.FinalExamRequest
	// 	SchemasResponse: nil,
	// })

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/final-exams/course/:courseSlug",
		Description: "Get all final exams for a course by course slug",
		Responses: map[string]string{
			"200": "List of final exams returned",
			"404": "Course not found",
		},
		Handler: controllers.GetFinalExamsByCourse,
		Params: []notelink.Parameter{
			{Name: "courseSlug", In: "path", Required: true, Type: "string", Description: "Course Slug"},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/final-exams/:examSlug",
		Description: "Get a final exam by exam slug",
		Responses: map[string]string{
			"200": "Final exam found",
			"404": "Final exam not found",
		},
		Handler: controllers.GetFinalExamBySlug,
		Params: []notelink.Parameter{
			{Name: "examSlug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/final-exams/:examSlug",
		Description: "Update a final exam by exam slug",
		Responses: map[string]string{
			"200": "Final exam updated successfully",
			"404": "Final exam not found",
		},
		Handler: controllers.UpdateFinalExam,
		Params: []notelink.Parameter{
			{Name: "examSlug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil, // models_request.FinalExamRequest
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/final-exams/:examSlug",
		Description: "Delete a final exam by exam slug",
		Responses: map[string]string{
			"200": "Final exam deleted successfully",
			"404": "Final exam not found",
		},
		Handler: controllers.DeleteFinalExam,
		Params: []notelink.Parameter{
			{Name: "examSlug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})

	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/final-exams/:examSlug/submit",
		Description: "Submit answers for a final exam and get score/pass/fail result",
		Responses: map[string]string{
			"200": "Result returned",
			"400": "Bad Request",
			"404": "Exam not found",
		},
		Handler: controllers.SubmitFinalExamAnswers,
		Params: []notelink.Parameter{
			{Name: "examSlug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil, // ExamAnswerSubmission
		SchemasResponse: nil, // ExamCheckResult
	})

	// Debug endpoint to check exam data
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/final-exams/:examSlug/debug",
		Description: "Debug endpoint to check exam questions and choices",
		Responses: map[string]string{
			"200": "Debug data returned",
			"404": "Exam not found",
		},
		Handler: controllers.DebugExamData,
		Params: []notelink.Parameter{
			{Name: "examSlug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})

	// Helper endpoint to show exam structure for testing multiple choice
	// api.DocumentedRoute(notelink.DocumentedRouteInput{
	// 	Method:      "GET",
	// 	Path:        "/final-exams/:examSlug/structure",
	// 	Description: "Get exam structure showing question types and suggested submission formats",
	// 	Responses: map[string]string{
	// 		"200": "Exam structure returned",
	// 		"404": "Exam not found",
	// 	},
	// 	Handler: controllers.GetExamStructure,
	// 	Params: []notelink.Parameter{
	// 		{Name: "examSlug", In: "path", Required: true, Type: "string", Description: "Exam Slug"},
	// 	},
	// 	SchemasRequest:  nil,
	// 	SchemasResponse: nil,
	// })
}
