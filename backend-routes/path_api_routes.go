package backend_routes

import (
	api_documents "med-api/api-documents"

	"github.com/canvas-tech-horizon/notelink"
)

// PathApiRoutes registers the API routes for course paths
func PathApiRoutes(api *notelink.ApiNote) {
	api_documents.NoteRoutePath(api)
	// api_documents.NoteRoutePathID(api)
	// api_documents.NoteRoutePathCreate(api)
	// api_documents.NoteRoutePathUpdate(api)
	// api_documents.NoteRoutePathDelete(api)
}
