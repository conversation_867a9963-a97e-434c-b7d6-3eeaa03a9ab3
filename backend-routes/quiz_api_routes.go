package backend_routes

import (
	api_documents "med-api/api-documents"

	"github.com/canvas-tech-horizon/notelink"
)

func QuizApiRoutes(api *notelink.ApiNote) {
	api_documents.NoteRouteQuizCreate(api)
	api_documents.NoteRouteGetAllQuiz(api)
	api_documents.NoteRouteQuizGetByID(api)
	api_documents.NoteRouteQuizUpdate(api)
	api_documents.NoteRouteQuizDelete(api)
	// You can add more quiz-related routes here (get, update, delete, etc.)
}
