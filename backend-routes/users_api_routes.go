package backend_routes

import (
	api_documents "med-api/api-documents"

	"github.com/canvas-tech-horizon/notelink"
)

func UserApiRoutes(api *notelink.ApiNote) {
	api_documents.NoteRouteUserTable(api)
	api_documents.NoteRouteRole(api)
	api_documents.NoteRouteUser(api)
	api_documents.NoteRouteUserCreate(api)
	api_documents.NoteRouteUserID(api)
	api_documents.NoteRouteUserUpdate(api)
	api_documents.NoteRouteUserDelete(api)
}
